import math
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from mmcv.cnn import Linear
from mmcv.cnn import xavier_init
from mmcv.runner.base_module import BaseModule
from torch.cuda.amp import autocast as autocast
from mmdet.models.utils.builder import TRANSFORMER
from mmcv.cnn.bricks.transformer import build_transformer_layer_sequence, POSITIONAL_ENCODING
from perceptron.utils.e2e_utils.instance import Instances, get_query_embeds
from perceptron.utils.e2e_utils.utils import normalize, xyz_ego_transformation


class PositionWiseFeedForward(nn.Module):
    """FeedForward Neural Networks for each position"""

    def __init__(self, channels: list, has_ln=False):
        super().__init__()
        n = len(channels)
        layers = []
        for i in range(1, n):
            layers.append(nn.Linear(channels[i - 1], channels[i]))
            if i < (n - 1):
                if has_ln:
                    layers.append(nn.LayerNorm(channels[i], eps=1e-6))
                layers.append(nn.GELU())
        nn.init.constant_(layers[-1].bias, 0.0)
        self.mlp = nn.Sequential(*layers)

    def forward(self, x):
        """
        Args:
            x: (B, S, D1)
        Returns:
            outputs: (B, S, D2)
        """
        return self.mlp(x)


class SubgraphLayer(nn.Module):
    def __init__(self, input_channels=256, hidden_channels=128):
        super().__init__()
        self.fc = nn.Linear(input_channels, hidden_channels)
        nn.init.kaiming_normal_(self.fc.weight)

    def forward(self, input_x, padding_mask: torch.BoolTensor):
        hidden_feat = self.fc(input_x)
        encode_feat = F.relu(F.layer_norm(hidden_feat, hidden_feat.size()[1:]))
        kernel_size = encode_feat.size()[1]
        # (B, N, C) - > (B, 1, C)
        polyline_feat = torch.max(encode_feat * (~padding_mask).float().unsqueeze(-1), dim=1, keepdims=True)[0]
        polyline_feat = polyline_feat.repeat(1, kernel_size, 1)
        # -> (B, N, 2C)
        output = torch.cat([encode_feat, polyline_feat], -1)
        return output


class SubgraphNet(nn.Module):
    def __init__(self, input_dims, embed_dims, future_len):
        super().__init__()
        self.sublayer1 = SubgraphLayer(input_dims, embed_dims // 2)
        self.sublayer2 = SubgraphLayer(embed_dims, embed_dims // 2)
        self.sublayer3 = SubgraphLayer(embed_dims, embed_dims // 2)
        self.embed_dims = embed_dims

    def forward(self, input_x, padding_mask: torch.BoolTensor):
        out1 = self.sublayer1(input_x, padding_mask)
        out2 = self.sublayer2(out1, padding_mask)
        out3 = self.sublayer3(out2, padding_mask)
        num_traj, kernel_size, _ = out3.size()
        polyline_feat = torch.max(out3 * (~padding_mask).float().unsqueeze(-1), dim=1, keepdims=True)[0]
        return polyline_feat


def split_last(x, shape):
    """split the last dimension to given shape"""
    shape = list(shape)
    assert shape.count(-1) <= 1
    if -1 in shape:
        shape[shape.index(-1)] = int(x.size(-1) / -np.prod(shape))
    return x.view(*x.size()[:-1], *shape)


def merge_last(x, n_dims):
    """merge the last n_dims to a dimension"""
    s = x.size()
    assert 1 < n_dims < len(s)
    return x.view(*s[:-n_dims], -1)


class MultiHeadedSelfAttention(nn.Module):
    """Multi-Headed Dot Product Attention"""

    def __init__(self, dim, num_heads):
        super().__init__()
        self.proj_q = nn.Linear(dim, dim)
        self.proj_k = nn.Linear(dim, dim)
        self.proj_v = nn.Linear(dim, dim)
        self.proj_o = nn.Linear(dim, dim)
        self.n_heads = num_heads
        # (B, H, S, S)
        self.scores = None

    def forward(self, x, y, mask_x=None, mask_y=None):
        """
        x source1 data, y source2 data
        x, q(query), k(key), v(value): (B(batch_size), S(seq_len), D(dim))
        mask_x: (B(batch_size) x S(seq_len))
        * split D(dim) into (H(n_heads), W(width of head)) ; D = H * W
        """
        # (B, S, D) -proj-> (B, S, D) -split-> (B, S, H, W) -trans-> (B, H, S, W)
        q, k, v = self.proj_q(x), self.proj_k(y), self.proj_v(y)
        q, k, v = (split_last(x, (self.n_heads, -1)).transpose(1, 2) for x in [q, k, v])
        # (B, H, Sx, W) @ (B, H, Sy, W) -> (B, H, Sx, Sy) -softmax-> (B, H, Sx, Sy)
        scores = q @ k.transpose(-2, -1) / np.sqrt(k.size(-1))
        if mask_x is not None and mask_y is not None:
            mask_x = mask_x[:, None, :, None]  # (B, Sx) -> (B, 1, Sx, 1)
            mask_y = mask_y[:, None, None, :]  # (B, Sy) -> (B, 1, 1, Sy)
            mask = mask_x & mask_y
            scores -= 1000.0 * (1.0 - mask)
        scores = F.softmax(scores, dim=-1)
        # (B, H, Sx, Sy) @ (B, H, Sy, W) -> (B, H, Sx, W) -trans-> (B, Sx, H, W)
        h = (scores @ v).transpose(1, 2).contiguous()
        # -merge-> (B, Sx, D)
        h = merge_last(h, 2)
        # (B, Sx, D) -> (B, Sx, D)
        h = self.proj_o(h)
        self.scores = scores
        return h


class TransformerEncodeLayer(nn.Module):
    def __init__(self, feature_dim: int, num_heads: int):
        super().__init__()
        self.layer_norm1 = nn.LayerNorm(feature_dim, eps=1e-6)
        self.attn = MultiHeadedSelfAttention(dim=feature_dim, num_heads=num_heads)
        self.layer_norm2 = nn.LayerNorm(feature_dim, eps=1e-6)
        self.mlp = PositionWiseFeedForward([feature_dim, 2 * feature_dim, feature_dim])

    def forward(self, x, y, mask_x, mask_y):
        message = self.attn(self.layer_norm1(x), self.layer_norm1(y), mask_x, mask_y)
        x = x + message
        message = self.mlp(self.layer_norm2(x))
        x = x + message
        return x


class SelfAttentional(nn.Module):
    def __init__(self, feature_dim: int, num_heads: int, layer_num: int):
        super().__init__()
        self.layers = nn.ModuleList([TransformerEncodeLayer(feature_dim, num_heads) for _ in range(layer_num)])

    def forward(self, desc, mask=None):
        for layer in self.layers:
            desc = layer(desc, desc, mask, mask)
        return desc


class CrossAttentional(nn.Module):
    def __init__(self, feature_dim: int, num_heads: int, layer_num: int):
        super().__init__()
        self.layers = nn.ModuleList([TransformerEncodeLayer(feature_dim, num_heads) for _ in range(layer_num)])

    def forward(self, desc0, desc1, mask0=None, mask1=None):
        for layer in self.layers:
            desc0 = layer(desc0, desc1, mask0, mask1)
        return desc0


@TRANSFORMER.register_module()
class TemporalTransformer(BaseModule):
    """Implement a DETR transformer.
    Adapting the input and output to the motion reasoning purpose.
    """

    def __init__(self, encoder=None, decoder=None, init_cfg=None, cross=False):
        super(TemporalTransformer, self).__init__(init_cfg=init_cfg)
        if encoder is not None:
            self.encoder = build_transformer_layer_sequence(encoder)
        else:
            self.encoder = None
        self.decoder = build_transformer_layer_sequence(decoder)
        self.embed_dims = self.decoder.embed_dims
        self.cross = cross

    def init_weights(self):
        for m in self.modules():
            if hasattr(m, "weight") and m.weight.dim() > 1:
                xavier_init(m, distribution="uniform")
        self._is_init = True
        return

    def forward(self, target, x, query_embed, pos_embed, query_key_padding_mask=None, key_padding_mask=None):
        """The general transformer interface for temporal/spatial cross attention
        Args:
            target: query feature [num_query, len, dim]
            x: key/value features [num_query, len, dim]
            query_embed: query positional embedding [num_query, len, dim]
            pos_embed: key positional embedding [num_query, len, dim]
        """
        # suit the shape for transformer
        # bs = 1
        memory = x.transpose(0, 1)
        pos_embed = pos_embed.transpose(0, 1)
        query_embed = query_embed.transpose(0, 1)  # [num_query, dim] -> [num_query, bs, dim]
        target = target.transpose(0, 1)  # [num_query, dim] -> [num_query, bs, dim]

        # if query_key_padding_mask is not None:
        #     query_key_padding_mask = query_key_padding_mask.transpose(0, 1)

        # if key_padding_mask is not None:
        #     key_padding_mask = key_padding_mask.transpose(0, 1)

        # out_dec: [num_layers, num_query, bs, dim]
        out_dec = self.decoder(
            query=target,
            key=memory,
            value=memory,
            key_pos=pos_embed,
            query_pos=query_embed,
            query_key_padding_mask=query_key_padding_mask,
            key_padding_mask=key_padding_mask,
        )[-1]
        out_dec = out_dec.transpose(0, 1)
        return out_dec


class TrackTransform(nn.Module):
    def __init__(self, pc_range, tracking_cfg, embed_dims=256, feat_transform=False):
        super(TrackTransform, self).__init__()

        self.pc_range = pc_range
        self.hist_len = tracking_cfg[0]
        self.fut_len = tracking_cfg[1]
        self.embed_dims = embed_dims
        self.feat_transform = feat_transform
        if feat_transform:
            self.weight = nn.Parameter(torch.tensor([1.0]))

        self.init_params_and_layers()

    def init_params_and_layers(self):
        if self.feat_transform:
            pose_encoder = []
            enc_channels = [4 * 4, self.embed_dims // 2, self.embed_dims, self.embed_dims]
            for i in range(1, len(enc_channels)):
                pose_encoder.append(Linear(enc_channels[i - 1], enc_channels[i]))
                if i < len(enc_channels) - 1:
                    pose_encoder.append(nn.LayerNorm(enc_channels[i], eps=1e-6))
                    pose_encoder.append(nn.GELU())
            nn.init.constant_(pose_encoder[-1].bias, 0.0)
            self.pose_encoder = nn.Sequential(*pose_encoder)

            pose_transform = []
            trans_channels = [self.embed_dims * 2, self.embed_dims, self.embed_dims]
            for i in range(1, len(trans_channels)):
                pose_transform.append(Linear(trans_channels[i - 1], trans_channels[i]))
                if i < len(trans_channels) - 1:
                    pose_transform.append(nn.LayerNorm(trans_channels[i], eps=1e-6))
                    pose_transform.append(nn.GELU())
            nn.init.constant_(pose_transform[-1].bias, 0.0)
            self.pose_transform = nn.Sequential(*pose_transform)
        return

    @autocast(False)
    def transform_hist_embeddings(self, track_instances, l2g0, l2g1):
        hist_embeds = track_instances.hist_embeds.clone()
        N, H, D = hist_embeds.shape

        g2l1 = torch.linalg.inv(l2g1)
        l0_to_l1 = (g2l1 @ l2g0).to(torch.float32)

        pose_feat = self.pose_encoder(l0_to_l1.view(-1))
        pose_feat = pose_feat.reshape(1, 1, -1).repeat(N, H, 1)
        assert pose_feat.shape == hist_embeds.shape

        hist_embeds_pose = torch.cat([hist_embeds, pose_feat], dim=-1)
        hist_embeds_transform = self.pose_transform(hist_embeds_pose)
        track_instances.hist_embeds = self.weight * hist_embeds_transform + hist_embeds
        # track_instances.hist_embeds_transform = hist_embeds_transform + hist_embeds
        return track_instances

    @autocast(False)
    def update_reference_points(self, track_instances, time_deltas, use_prediction=True, is_infer=False):
        """ Update the ref points according to the fut-prediction. Used for next frame. """
        if use_prediction:
            if is_infer:  # infer mode: use multi-step forecasting to modify reference points
                motions = track_instances.fut_predictions[:, 0, :2].clone()
                reference_points = track_instances.reference_points.clone()
                motions[:, 0] /= self.pc_range[3] - self.pc_range[0]
                motions[:, 1] /= self.pc_range[4] - self.pc_range[1]
                reference_points[..., :2] += motions.clone().detach()
                track_instances.reference_points = reference_points
            else:  # training mode: user single-step prediction
                track_instances.reference_points = track_instances.fut_xyz[:, 0, :].clone()
                track_instances.bboxes = track_instances.fut_bboxes[:, 0, :].clone()
        else:
            velos = track_instances.bboxes[..., 7:9].clone()
            reference_points = track_instances.reference_points.clone()
            velos[:, 0] /= self.pc_range[3] - self.pc_range[0]
            velos[:, 1] /= self.pc_range[4] - self.pc_range[1]
            reference_points[..., :2] += (velos * time_deltas).clone().detach()
            track_instances.reference_points = reference_points
        return track_instances

    @autocast(False)
    def update_ego(self, track_instances: Instances, l2g0, l2g1):
        """Update the ego coordinates for reference points, hist_xyz, and fut_xyz of the track_instances
           Modify the centers of the bboxes at the same time
        Args:
            track_instances: objects
            l2g0: a [4x4] matrix for current frame lidar-to-global transformation
            l2g1: a [4x4] matrix for target frame lidar-to-global transformation
        Return:
            transformed track_instances (inplace)
        """
        # TODO: orientation of the bounding boxes
        """1. Current states"""
        ref_points = track_instances.reference_points.clone()
        physical_ref_points = xyz_ego_transformation(
            ref_points, l2g0, l2g1, self.pc_range, src_normalized=True, tgt_normalized=False
        )
        track_instances.bboxes[..., [0, 1, 4]] = physical_ref_points.clone()
        track_instances.reference_points = normalize(physical_ref_points, self.pc_range)

        """2. History states"""
        inst_num = len(track_instances)
        hist_ref_xyz = track_instances.hist_xyz.clone().view(inst_num * self.hist_len, 3)
        physical_hist_ref = xyz_ego_transformation(
            hist_ref_xyz, l2g0, l2g1, self.pc_range, src_normalized=True, tgt_normalized=False
        )
        physical_hist_ref = physical_hist_ref.reshape(inst_num, self.hist_len, 3)
        track_instances.hist_bboxes[..., [0, 1, 4]] = physical_hist_ref
        track_instances.hist_xyz = normalize(physical_hist_ref, self.pc_range)

        """3. Future states"""
        inst_num = len(track_instances)
        fut_ref_xyz = track_instances.fut_xyz.clone().view(inst_num * self.fut_len, 3)
        physical_fut_ref = xyz_ego_transformation(
            fut_ref_xyz, l2g0, l2g1, self.pc_range, src_normalized=True, tgt_normalized=False
        )
        physical_fut_ref = physical_fut_ref.reshape(inst_num, self.fut_len, 3)
        track_instances.fut_bboxes[..., [0, 1, 4]] = physical_fut_ref
        track_instances.fut_xyz = normalize(physical_fut_ref, self.pc_range)

        return track_instances

    @autocast(False)
    def update_ego_hist_locs(self, track_instances: Instances, l2g0, l2g1, traj_len=20):
        inst_num = len(track_instances)
        hist_locs = track_instances.history_locs.clone().view(inst_num * traj_len, 3)
        hist_locs_transformed = xyz_ego_transformation(
            hist_locs, l2g0, l2g1, self.pc_range, src_normalized=False, tgt_normalized=False
        )
        hist_locs_transformed = hist_locs_transformed.reshape(inst_num, traj_len, 3)
        track_instances.history_locs = hist_locs_transformed

        return track_instances

    @autocast(False)
    def sync_pos_embedding(self, track_instances: Instances, radar_points, query_embedding, bev_query_embed):
        """Synchronize the positional embedding across all fields"""

        if query_embedding is not None:
            track_instances.query_embeds = get_query_embeds(
                track_instances.reference_points, radar_points, query_embedding, bev_query_embed
            )
            track_instances.hist_position_embeds = get_query_embeds(
                track_instances.hist_xyz, radar_points, query_embedding, bev_query_embed
            )
        return track_instances


@POSITIONAL_ENCODING.register_module()
class SinePositionalEncoding3D(BaseModule):
    """Position encoding with sine and cosine functions.
    See `End-to-End Object Detection with Transformers
    <https://arxiv.org/pdf/2005.12872>`_ for details.
    Args:
        num_feats (int): The feature dimension for each position
            along x-axis or y-axis. Note the final returned dimension
            for each position is 2 times of this value.
        temperature (int, optional): The temperature used for scaling
            the position embedding. Defaults to 10000.
        normalize (bool, optional): Whether to normalize the position
            embedding. Defaults to False.
        scale (float, optional): A scale factor that scales the position
            embedding. The scale will be used only when `normalize` is True.
            Defaults to 2*pi.
        eps (float, optional): A value added to the denominator for
            numerical stability. Defaults to 1e-6.
        offset (float): offset add to embed when do the normalization.
            Defaults to 0.
        init_cfg (dict or list[dict], optional): Initialization config dict.
            Default: None
    """

    def __init__(
        self, num_feats, temperature=10000, normalize=False, scale=2 * math.pi, eps=1e-6, offset=0.0, init_cfg=None
    ):
        super(SinePositionalEncoding3D, self).__init__(init_cfg)
        if normalize:
            assert isinstance(scale, (float, int)), (
                "when normalize is set," "scale should be provided and in float or int type, " f"found {type(scale)}"
            )
        self.num_feats = num_feats
        self.temperature = temperature
        self.normalize = normalize
        self.scale = scale
        self.eps = eps
        self.offset = offset

    def forward(self, mask):
        """Forward function for `SinePositionalEncoding`.
        Args:
            mask (Tensor): ByteTensor mask. Non-zero values representing
                ignored positions, while zero values means valid positions
                for this image. Shape [bs, h, w].
        Returns:
            pos (Tensor): Returned position embedding with shape
                [bs, num_feats*2, h, w].
        """
        # For convenience of exporting to ONNX, it's required to convert
        # `masks` from bool to int.
        mask = mask.to(torch.int)
        not_mask = 1 - mask  # logical_not
        n_embed = not_mask.cumsum(1, dtype=torch.float32)
        y_embed = not_mask.cumsum(2, dtype=torch.float32)
        x_embed = not_mask.cumsum(3, dtype=torch.float32)
        if self.normalize:
            n_embed = (n_embed + self.offset) / (n_embed[:, -1:, :, :] + self.eps) * self.scale
            y_embed = (y_embed + self.offset) / (y_embed[:, :, -1:, :] + self.eps) * self.scale
            x_embed = (x_embed + self.offset) / (x_embed[:, :, :, -1:] + self.eps) * self.scale
        dim_t = torch.arange(self.num_feats, dtype=torch.float32, device=mask.device)
        dim_t = self.temperature ** (2 * (dim_t // 2) / self.num_feats)
        pos_n = n_embed[:, :, :, :, None] / dim_t
        pos_x = x_embed[:, :, :, :, None] / dim_t
        pos_y = y_embed[:, :, :, :, None] / dim_t
        # use `view` instead of `flatten` for dynamically exporting to ONNX
        B, N, H, W = mask.size()
        pos_n = torch.stack((pos_n[:, :, :, :, 0::2].sin(), pos_n[:, :, :, :, 1::2].cos()), dim=4).view(B, N, H, W, -1)
        pos_x = torch.stack((pos_x[:, :, :, :, 0::2].sin(), pos_x[:, :, :, :, 1::2].cos()), dim=4).view(B, N, H, W, -1)
        pos_y = torch.stack((pos_y[:, :, :, :, 0::2].sin(), pos_y[:, :, :, :, 1::2].cos()), dim=4).view(B, N, H, W, -1)
        pos = torch.cat((pos_n, pos_y, pos_x), dim=4).permute(0, 1, 4, 2, 3)
        return pos

    def __repr__(self):
        """str: a string that describes the module"""
        repr_str = self.__class__.__name__
        repr_str += f"(num_feats={self.num_feats}, "
        repr_str += f"temperature={self.temperature}, "
        repr_str += f"normalize={self.normalize}, "
        repr_str += f"scale={self.scale}, "
        repr_str += f"eps={self.eps})"
        return repr_str


@POSITIONAL_ENCODING.register_module()
class LearnedPositionalEncoding3D(BaseModule):
    """Position embedding with learnable embedding weights.
    Args:
        num_feats (int): The feature dimension for each position
            along x-axis or y-axis. The final returned dimension for
            each position is 2 times of this value.
        row_num_embed (int, optional): The dictionary size of row embeddings.
            Default 50.
        col_num_embed (int, optional): The dictionary size of col embeddings.
            Default 50.
        init_cfg (dict or list[dict], optional): Initialization config dict.
    """

    def __init__(self, num_feats, row_num_embed=50, col_num_embed=50, init_cfg=dict(type="Uniform", layer="Embedding")):
        super(LearnedPositionalEncoding3D, self).__init__(init_cfg)
        self.row_embed = nn.Embedding(row_num_embed, num_feats)
        self.col_embed = nn.Embedding(col_num_embed, num_feats)
        self.num_feats = num_feats
        self.row_num_embed = row_num_embed
        self.col_num_embed = col_num_embed

    def forward(self, mask):
        """Forward function for `LearnedPositionalEncoding`.
        Args:
            mask (Tensor): ByteTensor mask. Non-zero values representing
                ignored positions, while zero values means valid positions
                for this image. Shape [bs, h, w].
        Returns:
            pos (Tensor): Returned position embedding with shape
                [bs, num_feats*2, h, w].
        """
        h, w = mask.shape[-2:]
        x = torch.arange(w, device=mask.device)
        y = torch.arange(h, device=mask.device)
        x_embed = self.col_embed(x)
        y_embed = self.row_embed(y)
        pos = (
            torch.cat((x_embed.unsqueeze(0).repeat(h, 1, 1), y_embed.unsqueeze(1).repeat(1, w, 1)), dim=-1)
            .permute(2, 0, 1)
            .unsqueeze(0)
            .repeat(mask.shape[0], 1, 1, 1)
        )
        return pos

    def __repr__(self):
        """str: a string that describes the module"""
        repr_str = self.__class__.__name__
        repr_str += f"(num_feats={self.num_feats}, "
        repr_str += f"row_num_embed={self.row_num_embed}, "
        repr_str += f"col_num_embed={self.col_num_embed})"
        return repr_str
