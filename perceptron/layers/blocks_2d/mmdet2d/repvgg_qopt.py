# SPDX-FileCopyrightText: Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: LicenseRef-NvidiaProprietary
#
# NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
# property and proprietary rights in and to this material, related
# documentation and any modifications thereto. Any use, reproduction,
# disclosure or distribution of this material and related documentation
# without an express license agreement from NVIDIA CORPORATION or
# its affiliates is strictly prohibited.

import torch.nn as nn
import torch
import torch.utils.checkpoint as checkpoint
import torch.nn.functional as F
from collections import OrderedDict, namedtuple
from torch.autograd import Function
from copy import deepcopy
import fnmatch
from dataclasses import dataclass
from mmdet.models import BACKBONES

class NoJitTrace(torch.no_grad):
    def __enter__(self):
        super().__enter__()
        self.state = torch._C._get_tracing_state()
        torch._C._set_tracing_state(None)

    def __exit__(self, *args):
        super().__exit__(*args)
        torch._C._set_tracing_state(self.state)
        self.state = None

class Sparsify(Function):
    @staticmethod
    def forward(ctx, weight, mask, decay):
        ctx.save_for_backward(weight)
        ctx.mask  = mask
        ctx.decay = decay
        return weight * mask

    @staticmethod
    def backward(ctx, grad_output):
        weight, = ctx.saved_tensors
        return grad_output + ctx.decay * (1 - ctx.mask) * weight, None, None

    @staticmethod
    def obtain_mask(weight, sparse):
        if not sparse.enable:
            return torch.ones_like(weight)
        
        M = sparse.M
        N = sparse.N
        O, I, H, W = weight.shape
        weight = weight.detach().permute(0, 2, 3, 1).reshape(-1, M)
        index  = torch.argsort(weight.abs(), dim=1)[:, :int(M-N)]

        mask = torch.ones(weight.shape, device=weight.device, dtype=weight.dtype)
        mask = mask.scatter_(dim=1, index=index, value=0).reshape(O, H, W, I)
        return mask.permute(0, 3, 1, 2).contiguous()

class ResConv2d(nn.Module):
    def __init__(self, w, b, conv_params, residual):
        super().__init__()
        self.conv_params = conv_params
        self.register_buffer("w", w)
        self.register_buffer("b", b)

        if isinstance(residual, torch.Tensor):
            self.register_buffer("residual", residual.view(1, -1, 1, 1))
        else:
            self.register_buffer("residual", None)

    def _conv_forward(self, x, w, b):
        if torch.onnx.is_in_onnx_export():
            if self.residual is not None:
                with NoJitTrace():
                    w = w.clone()
                    idd = torch.arange(w.shape[0]).to(w.device)
                    w[idd, idd, 1, 1] += self.residual.flatten()
            return F.conv2d(x, w, b, **self.conv_params)

        if self.residual is None:
            return F.conv2d(x, w, b, **self.conv_params)
        return F.conv2d(x, w, b, **self.conv_params) + x * self.residual
    
    def forward(self, x):
        return self._conv_forward(x, self.w, self.b)

class DeployBlock(nn.Module):
    def __init__(self, conv_weight, conv_bias, conv_param, scale, activation, sparse):
        super().__init__()
        self.sparse = sparse
        self.conv = ResConv2d(conv_weight, conv_bias, conv_param, scale)
        self.activation = activation

    def extra_repr(self):
        info = f"decay={self.sparse.decay}, M={self.sparse.M}, N={self.sparse.N}, enable={self.sparse.enable}"
        return ", ".join([super().extra_repr(), info])

    def forward(self, x):
        return self.activation(self.conv(x))

class LimitedBatchNorm(nn.BatchNorm2d):
    def get_limited_weight(self):
        return self.weight.clamp(1e-10)

    def forward(self, input: torch.Tensor) -> torch.Tensor:
        self._check_input_dim(input)

        if self.momentum is None:
            exponential_average_factor = 0.0
        else:
            exponential_average_factor = self.momentum

        if self.training and self.track_running_stats:
            if self.num_batches_tracked is not None:  # type: ignore[has-type]
                self.num_batches_tracked.add_(1)  # type: ignore[has-type]
                if self.momentum is None:  # use cumulative moving average
                    exponential_average_factor = 1.0 / float(self.num_batches_tracked)
                else:  # use exponential moving average
                    exponential_average_factor = self.momentum

        if self.training:
            bn_training = True
        else:
            bn_training = (self.running_mean is None) and (self.running_var is None)

        return F.batch_norm(
            input,
            # If buffers are not to be tracked, ensure that they won't be updated
            self.running_mean
            if not self.training or self.track_running_stats
            else None,
            self.running_var if not self.training or self.track_running_stats else None,
            self.get_limited_weight(),
            self.bias,
            bn_training,
            exponential_average_factor,
            self.eps,
        )

@dataclass
class SparsityConfig:
    decay:float = 2e-4
    M:int = 4
    N:int = 2
    enable:bool = False

sparsity_configuration = SparsityConfig(2e-4, 4, 2, False)

def set_sparsity_configuration(sparsity:SparsityConfig):
    global sparsity_configuration
    sparsity_configuration = sparsity
    
class MaskedConv2d(nn.Conv2d):
    def get_weight(self, mask, sparse):
        if sparse is not None and mask is not None:
            weight = Sparsify.apply(self.weight, mask, sparse.decay)
        else:
            weight = self.weight
        return weight

    def forward(self, input, mask, sparse):
        if torch.onnx.is_in_onnx_export():
            with NoJitTrace():
                weight = self.get_weight(mask, sparse)
        else:
            weight = self.get_weight(mask, sparse)
        return F.conv2d(input, weight, self.bias, self.stride, self.padding, self.dilation, self.groups)

def conv_bn(in_channels, out_channels, kernel_size, stride, padding, groups=1):
    result = nn.Sequential()
    result.add_module('conv', MaskedConv2d(in_channels=in_channels, out_channels=out_channels,
                                                  kernel_size=kernel_size, stride=stride, padding=padding, groups=groups, bias=False))
    result.add_module('bn', nn.BatchNorm2d(num_features=out_channels))
    return result

class RepVGGBlock(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size,
                 stride=1, padding=0, dilation=1, groups=1, padding_mode='zeros', fast_evaluate=True):
        super(RepVGGBlock, self).__init__()
        self.groups = groups
        self.in_channels = in_channels
        self.sparse = deepcopy(sparsity_configuration)

        assert kernel_size == 3
        assert padding == 1

        padding_11 = padding - kernel_size // 2

        self.fast_evaluate = fast_evaluate
        self.nonlinearity = nn.ReLU()
        self.fused_kernel_reparams = []
        self.rbr_identity = LimitedBatchNorm(num_features=in_channels) if out_channels == in_channels and stride == 1 else None
        self.rbr_dense = conv_bn(in_channels=in_channels, out_channels=out_channels, kernel_size=kernel_size, stride=stride, padding=padding, groups=groups)
        self.rbr_1x1   = conv_bn(in_channels=in_channels, out_channels=out_channels, kernel_size=1, stride=stride, padding=padding_11, groups=groups)

    def extra_repr(self):
        return super().extra_repr() + f", decay={self.sparse.decay}, M={self.sparse.M}, N={self.sparse.N}, enable={self.sparse.enable}"
    
    def forward(self, inputs):
        if self.fast_evaluate and not self.training:
            assert len(self.fused_kernel_reparams) > 0, f"Failed to execute validate with SparseRepVGGBlock, the self.fused_kernel_reparams is empty."
            weight, bias, bnw, bnb = self.fused_kernel_reparams

            # update: bias -> bias + bnb
            x = F.conv2d(inputs, weight, bias + bnb, self.rbr_dense.conv.stride, self.rbr_dense.conv.padding, self.rbr_dense.conv.dilation, self.rbr_dense.conv.groups)
            if isinstance(bnw, torch.Tensor):
                # update: remove + bnb.view(1, -1, 1, 1)
                x += inputs * bnw.view(1, -1, 1, 1) # + bnb.view(1, -1, 1, 1)
            return self.nonlinearity(x)

        if self.sparse.enable:
            kernel3x3, bias3x3 = self._fuse_bn_tensor(self.rbr_dense)
            kernel1x1, bias1x1 = self._fuse_bn_tensor(self.rbr_1x1)
            weight = kernel3x3 + self._pad_1x1_to_3x3_tensor(kernel1x1)
            dense_mask = Sparsify.obtain_mask(weight, self.sparse)
            rbr1_mask  = dense_mask[:, :, 1:2, 1:2]

            if self.rbr_identity is None:
                id_out = 0
            else:
                id_out = self.rbr_identity(inputs)

            x0 = self.rbr_dense.conv(inputs, dense_mask, self.sparse)
            x0 = self.rbr_dense.bn(x0)
            
            x1 = self.rbr_1x1.conv(inputs, rbr1_mask, self.sparse)
            x1 = self.rbr_1x1.bn(x1)
            return self.nonlinearity(x0 + x1 + id_out)
        else:
            if self.rbr_identity is None:
                id_out = 0
            else:
                id_out = self.rbr_identity(inputs)

            x0 = self.rbr_dense.conv(inputs, None, None)
            x0 = self.rbr_dense.bn(x0)
            
            x1 = self.rbr_1x1.conv(inputs, None, None)
            x1 = self.rbr_1x1.bn(x1)
            return self.nonlinearity(x0 + x1 + id_out)

    def get_equivalent_kernel_bias(self):
        kernel3x3, bias3x3 = self._fuse_bn_tensor(self.rbr_dense)
        kernel1x1, bias1x1 = self._fuse_bn_tensor(self.rbr_1x1)

        alpha, beta = 0, 0
        if self.rbr_identity is not None:
            running_mean = self.rbr_identity.running_mean
            running_var = self.rbr_identity.running_var
            gamma = self.rbr_identity.get_limited_weight()
            beta = self.rbr_identity.bias
            eps = self.rbr_identity.eps
            rstd = (running_var + eps).rsqrt()
            alpha = rstd * gamma
            beta  = beta - running_mean * rstd * gamma

        return kernel3x3 + self._pad_1x1_to_3x3_tensor(kernel1x1), bias3x3 + bias1x1, alpha, beta

    def _pad_1x1_to_3x3_tensor(self, kernel1x1):
        if kernel1x1 is None:
            return 0
        else:
            return torch.nn.functional.pad(kernel1x1, [1,1,1,1])

    def _fuse_bn_tensor(self, branch):
        if branch is None:
            return 0, 0
        
        kernel = branch.conv.weight
        running_mean = branch.bn.running_mean
        running_var = branch.bn.running_var
        gamma = branch.bn.weight
        beta = branch.bn.bias
        eps = branch.bn.eps
        std = (running_var + eps).sqrt()
        t = (gamma / std).reshape(-1, 1, 1, 1)
        return kernel * t, beta - running_mean * gamma / std
    
    def make_deploy_module(self):
        weight, bias, bnw, bnb = self.get_equivalent_kernel_bias()
        if self.sparse.enable:
            weight = weight * Sparsify.obtain_mask(weight, self.sparse)

        ref_conv:nn.Conv2d = self.rbr_dense.conv
        return DeployBlock(weight, bias + bnb, dict(
            stride   = ref_conv.stride,
            dilation = ref_conv.dilation,
            padding  = ref_conv.padding,
            groups   = ref_conv.groups
        ), bnw, self.nonlinearity, self.sparse)

    def train(self, mode):
        super().train(mode)
        if not self.fast_evaluate:
            return
        
        if mode:
            # training
            # clear the fused_kernel_reparams
            self.fused_kernel_reparams = []
        else:
            # evaluate
            with torch.no_grad():
                weight, bias, bnw, bnb = self.get_equivalent_kernel_bias()

                if self.sparse.enable:
                    weight_mask = Sparsify.obtain_mask(weight, self.sparse)
                    self.fused_kernel_reparams = [weight * weight_mask, bias, bnw, bnb]
                else:
                    self.fused_kernel_reparams = [weight, bias, bnw, bnb]

@BACKBONES.register_module()
class RepVGGQOpt(nn.Module):

    def __init__(self, num_blocks, num_classes=1000, width_multiplier=None, override_groups_map=None, use_checkpoint=False, fast_evaluate=False, pretrained=None):
        super(RepVGGQOpt, self).__init__()
        assert len(width_multiplier) == 4
        self.override_groups_map = override_groups_map or dict()
        assert 0 not in self.override_groups_map
        self.use_checkpoint = use_checkpoint

        self.deploy_mode = False
        self.in_planes = min(64, int(64 * width_multiplier[0]))
        self.fast_evaluate = fast_evaluate
        self.stage0 = RepVGGBlock(in_channels=3, out_channels=self.in_planes, kernel_size=3, stride=2, padding=1, fast_evaluate=fast_evaluate)
        self.cur_layer_idx = 1
        self.stage1 = self._make_stage(int(64 * width_multiplier[0]), num_blocks[0], stride=2)
        self.stage2 = self._make_stage(int(128 * width_multiplier[1]), num_blocks[1], stride=2)
        self.stage3 = self._make_stage(int(256 * width_multiplier[2]), num_blocks[2], stride=2)
        self.stage4 = self._make_stage(int(512 * width_multiplier[3]), num_blocks[3], stride=2)
        # self.gap = nn.AdaptiveAvgPool2d(output_size=1)
        # self.linear = nn.Linear(int(512 * width_multiplier[3]), num_classes)

        if pretrained is not None:
            self.init_weights(pretrained)

    def init_weights(self, pretrained=None):
        if pretrained is None:
            pass
        else:
            import refile

            ckpt_path = pretrained
            model_file = refile.smart_open(ckpt_path, "rb")
            ckpt = torch.load(model_file, map_location="cpu")
            if "state_dict" in ckpt:
                _state_dict = ckpt["state_dict"]
            elif "model" in ckpt:
                _state_dict = ckpt["model"]
            else:
                _state_dict = ckpt

            state_dict = _state_dict
            missing_keys, unexpected_keys = self.load_state_dict(state_dict, False)

            # show for debug
            print('missing_keys: ', missing_keys)
            print('unexpected_keys: ', unexpected_keys)

    def _make_stage(self, planes, num_blocks, stride):
        strides = [stride] + [1]*(num_blocks-1)
        blocks = []
        for stride in strides:
            cur_groups = self.override_groups_map.get(self.cur_layer_idx, 1)
            blocks.append(RepVGGBlock(in_channels=self.in_planes, out_channels=planes, kernel_size=3,
                                      stride=stride, padding=1, groups=cur_groups, fast_evaluate=self.fast_evaluate))
            self.in_planes = planes
            self.cur_layer_idx += 1
        return nn.ModuleList(blocks)

    def apply_sparsity_rules(self, *rules):
        # rules = [("stage1.*", True), ("stage2.*", True)]
        for name, module in self.named_modules():
            if isinstance(module, RepVGGBlock):
                has_matched_pattern = False
                final_state = module.sparse.enable
                for path_pattern, state in rules:
                    if fnmatch.fnmatch(name, path_pattern):
                        final_state = state
                        has_matched_pattern = True
                
                if has_matched_pattern:
                    print(f"Set sparsity state: ({name}).sparse.enable = {final_state}")
                    module.sparse.enable = final_state

    def to_deploy(self):
        if self.deploy_mode:
            return

        def stage_convert(stage : nn.Module):
            return nn.Sequential(OrderedDict([(f"block{i}", b.make_deploy_module()) for i, b in enumerate(stage)]))

        self.stage0 = self.stage0.make_deploy_module()
        self.stage1 = stage_convert(self.stage1)
        self.stage2 = stage_convert(self.stage2)
        self.stage3 = stage_convert(self.stage3)
        self.stage4 = stage_convert(self.stage4)
        self.deploy_mode = True
        self.eval()

    # def to_deploy(self):
    #     def block_convert(b : RepVGGBlock):
    #         weight, bias, bnw, bnb = b.get_equivalent_kernel_bias()
    #         if b.sparse.enable:
    #             weight = weight * Sparsify.obtain_mask(weight, b.sparse)

    #         ref_conv:nn.Conv2d = b.rbr_dense.conv
    #         return DeployBlock(weight, bias + bnb, dict(
    #             stride   = ref_conv.stride,
    #             dilation = ref_conv.dilation,
    #             padding  = ref_conv.padding,
    #             groups   = ref_conv.groups
    #         ), bnw, b.nonlinearity, b.sparse)

    #     def stage_convert(stage : nn.Module):
    #         if isinstance(stage, RepVGGBlock):
    #             return block_convert(stage)
    #         return nn.Sequential(OrderedDict([(f"block{i}", block_convert(b)) for i, b in enumerate(stage)]))

    #     stages = [self.stage0, self.stage1, self.stage2, self.stage3, self.stage4]
    #     return nn.Sequential(
    #         OrderedDict(
    #             [(f"stage{i}", stage_convert(item)) for i, item in enumerate(stages)] + 
    #             [("gap", self.gap), ("flatten", nn.Flatten(1)), ("linear", self.linear)]
    #         )
    #     )

    def forward(self, x):
        outs = []
        out = self.stage0(x)
        for stage in (self.stage1, self.stage2, self.stage3, self.stage4):
            for block in stage:
                if self.use_checkpoint:
                    out = checkpoint.checkpoint(block, out)
                else:
                    out = block(out)
            outs.append(out)
        # out = self.gap(out)
        # out = out.view(out.size(0), -1)
        # out = self.linear(out)

        return outs


optional_groupwise_layers = [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26]
g2_map = {l: 2 for l in optional_groupwise_layers}
g4_map = {l: 4 for l in optional_groupwise_layers}

def create_RepVGG_QOPT_A0(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[2, 4, 14, 1], num_classes=1000,
                  width_multiplier=[0.75, 0.75, 0.75, 2.5], override_groups_map=None, use_checkpoint=use_checkpoint)

def create_RepVGG_QOPT_A1(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[2, 4, 14, 1], num_classes=1000,
                  width_multiplier=[1, 1, 1, 2.5], override_groups_map=None, use_checkpoint=use_checkpoint)

def create_RepVGG_QOPT_A2(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[2, 4, 14, 1], num_classes=1000,
                  width_multiplier=[1.5, 1.5, 1.5, 2.75], override_groups_map=None, use_checkpoint=use_checkpoint)

def create_RepVGG_QOPT_B0(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[4, 6, 16, 1], num_classes=1000,
                  width_multiplier=[1, 1, 1, 2.5], override_groups_map=None, use_checkpoint=use_checkpoint)

def create_RepVGG_QOPT_B1(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[4, 6, 16, 1], num_classes=1000,
                  width_multiplier=[2, 2, 2, 4], override_groups_map=None, use_checkpoint=use_checkpoint)

def create_RepVGG_QOPT_B1g2(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[4, 6, 16, 1], num_classes=1000,
                  width_multiplier=[2, 2, 2, 4], override_groups_map=g2_map, use_checkpoint=use_checkpoint)

def create_RepVGG_QOPT_B1g4(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[4, 6, 16, 1], num_classes=1000,
                  width_multiplier=[2, 2, 2, 4], override_groups_map=g4_map, use_checkpoint=use_checkpoint)


def create_RepVGG_QOPT_B2(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[4, 6, 16, 1], num_classes=1000,
                  width_multiplier=[2.5, 2.5, 2.5, 5], override_groups_map=None, use_checkpoint=use_checkpoint)

def create_RepVGG_QOPT_B2g2(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[4, 6, 16, 1], num_classes=1000,
                  width_multiplier=[2.5, 2.5, 2.5, 5], override_groups_map=g2_map, use_checkpoint=use_checkpoint)

def create_RepVGG_QOPT_B2g4(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[4, 6, 16, 1], num_classes=1000,
                  width_multiplier=[2.5, 2.5, 2.5, 5], override_groups_map=g4_map, use_checkpoint=use_checkpoint)


def create_RepVGG_QOPT_B3(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[4, 6, 16, 1], num_classes=1000,
                  width_multiplier=[3, 3, 3, 5], override_groups_map=None, use_checkpoint=use_checkpoint)

def create_RepVGG_QOPT_B3g2(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[4, 6, 16, 1], num_classes=1000,
                  width_multiplier=[3, 3, 3, 5], override_groups_map=g2_map, use_checkpoint=use_checkpoint)

def create_RepVGG_QOPT_B3g4(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[4, 6, 16, 1], num_classes=1000,
                  width_multiplier=[3, 3, 3, 5], override_groups_map=g4_map, use_checkpoint=use_checkpoint)

def create_RepVGG_QOPT_D2se(use_checkpoint=False):
    return RepVGGQOpt(num_blocks=[8, 14, 24, 1], num_classes=1000,
                  width_multiplier=[2.5, 2.5, 2.5, 5], override_groups_map=None, use_se=True, use_checkpoint=use_checkpoint)


func_dict = {
'RepVGGQOpt-A0': create_RepVGG_QOPT_A0,
'RepVGGQOpt-A1': create_RepVGG_QOPT_A1,
'RepVGGQOpt-A2': create_RepVGG_QOPT_A2,
'RepVGGQOpt-B0': create_RepVGG_QOPT_B0,
'RepVGGQOpt-B1': create_RepVGG_QOPT_B1,
'RepVGGQOpt-B1g2': create_RepVGG_QOPT_B1g2,
'RepVGGQOpt-B1g4': create_RepVGG_QOPT_B1g4,
'RepVGGQOpt-B2': create_RepVGG_QOPT_B2,
'RepVGGQOpt-B2g2': create_RepVGG_QOPT_B2g2,
'RepVGGQOpt-B2g4': create_RepVGG_QOPT_B2g4,
'RepVGGQOpt-B3': create_RepVGG_QOPT_B3,
'RepVGGQOpt-B3g2': create_RepVGG_QOPT_B3g2,
'RepVGGQOpt-B3g4': create_RepVGG_QOPT_B3g4,
'RepVGGQOpt-D2se': create_RepVGG_QOPT_D2se,      #   Updated at April 25, 2021. This is not reported in the CVPR paper.
}
def get_RepVGG_QOPT_func_by_name(name):
    return func_dict[name]

if __name__=="__main__":
    
    set_sparsity_configuration(SparsityConfig(2e-4, 4, 2, True))
    model = get_RepVGG_QOPT_func_by_name('RepVGGQOpt-B0')()
    model.apply_sparsity_rules(
        ("stage1.*", False),
        ("stage2.*", True),
        ("stage3.*", True),
        ("stage4.*", True),
        ("stage3.2", False),
    )
    print(model)

    # Update the weight, bias, running_mean, and running_var for each batchnorm layer.
    for name, module in model.named_modules():
        if isinstance(module, nn.BatchNorm2d):
            torch.nn.init.uniform_(module.weight, 0.2, 1.2)
            torch.nn.init.normal_(module.bias)
            torch.nn.init.normal_(module.running_mean)
            torch.nn.init.uniform_(module.running_var, 0.5, 1.5)
    
    # To evaluation state
    model.eval()

    with torch.no_grad():
        x = torch.randn([5,3,224,224])
        out1 = model(x)

        model.to_deploy()
        out2 = model(x)

        diff = (out1 - out2).abs().sum().item()
        print(f"The absolute error: {diff}")
