import functools

import numpy as np
import torch
import torch.nn as nn
import torch.utils.checkpoint as cp
from .sc_conv import <PERSON>B<PERSON>leneck, SCBottleneck2


class BaseBEVBackbone(nn.Module):
    def __init__(
        self,
        layer_nums,
        layer_strides,
        num_filters,
        upsample_strides,
        num_upsample_filters,
        input_channels,
        witch_cp=False,
        use_scconv=False,
        upsample_output=False,
    ):
        super().__init__()

        if layer_nums is not None:
            assert len(layer_nums) == len(layer_strides) == len(num_filters)
        else:
            layer_nums = layer_strides = num_filters = []

        if upsample_strides is not None:
            assert len(upsample_strides) == len(num_upsample_filters)
        else:
            upsample_strides = num_upsample_filters = []

        num_levels = len(layer_nums)
        c_in_list = [input_channels, *num_filters[:-1]]
        self.with_cp = witch_cp
        self.blocks = nn.ModuleList()
        self.deblocks = nn.ModuleList()
        for idx in range(num_levels):
            cur_layers = [
                nn.ZeroPad2d(1),
                nn.Conv2d(
                    c_in_list[idx],
                    num_filters[idx],
                    kernel_size=3,
                    stride=layer_strides[idx],
                    padding=0,
                    bias=False,
                ),
                nn.BatchNorm2d(num_filters[idx], eps=1e-3, momentum=0.01),
                nn.ReLU(inplace=True),
            ]
            for k in range(layer_nums[idx]):
                if not use_scconv:
                    cur_layers.extend(
                        [
                            nn.Conv2d(
                                num_filters[idx],
                                num_filters[idx],
                                kernel_size=3,
                                padding=1,
                                bias=False,
                            ),
                            nn.BatchNorm2d(num_filters[idx], eps=1e-3, momentum=0.01),
                            nn.ReLU(inplace=True),
                        ]
                    )
                else:
                    cur_layers.extend(
                        [
                            SCBottleneck(
                                num_filters[idx],
                                num_filters[idx],
                                norm_layer=functools.partial(nn.BatchNorm2d, eps=1e-3, momentum=0.01),
                            )
                        ]
                    )

            self.blocks.append(nn.Sequential(*cur_layers))
            if len(upsample_strides) > 0:
                stride = upsample_strides[idx]
                if stride >= 1:
                    self.deblocks.append(
                        nn.Sequential(
                            nn.ConvTranspose2d(
                                num_filters[idx],
                                num_upsample_filters[idx],
                                upsample_strides[idx],
                                stride=upsample_strides[idx],
                                bias=False,
                            ),
                            nn.BatchNorm2d(num_upsample_filters[idx], eps=1e-3, momentum=0.01),
                            nn.ReLU(inplace=True),
                        )
                    )
                else:
                    stride = np.round(1 / stride).astype(np.int)
                    self.deblocks.append(
                        nn.Sequential(
                            nn.Conv2d(
                                num_filters[idx],
                                num_upsample_filters[idx],
                                stride,
                                stride=stride,
                                bias=False,
                            ),
                            nn.BatchNorm2d(num_upsample_filters[idx], eps=1e-3, momentum=0.01),
                            nn.ReLU(inplace=True),
                        )
                    )

        c_in = sum(num_upsample_filters)
        if len(upsample_strides) > num_levels:
            self.deblocks.append(
                nn.Sequential(
                    nn.ConvTranspose2d(
                        c_in,
                        c_in,
                        upsample_strides[-1],
                        stride=upsample_strides[-1],
                        bias=False,
                    ),
                    nn.BatchNorm2d(c_in, eps=1e-3, momentum=0.01),
                    nn.ReLU(inplace=True),
                )
            )

        self.num_bev_features = c_in

        self.upsample_featuremap = upsample_output
        if self.upsample_featuremap:
            self.upsample_conv = nn.Sequential(
                nn.ConvTranspose2d(c_in, c_in, 2, stride=2, bias=False),
                nn.BatchNorm2d(c_in, eps=1e-3, momentum=0.01),
                nn.ReLU(inplace=True),
            )

    def forward(self, spatial_features):
        """
        Args:
            data_dict:
                spatial_features
        Returns:
        """
        ups = []
        pyramid = {}
        x = spatial_features
        for i in range(len(self.blocks)):
            if self.with_cp and self.training:
                x = cp.checkpoint(self.blocks[i], x)
            else:
                x = self.blocks[i](x)

            stride = int(spatial_features.shape[2] / x.shape[2])
            pyramid["spatial_features_%dx" % stride] = x
            if len(self.deblocks) > 0:
                ups.append(self.deblocks[i](x))
            else:
                ups.append(x)

        if len(ups) > 1:
            x = torch.cat(ups, dim=1)
        elif len(ups) == 1:
            x = ups[0]

        if len(self.deblocks) > len(self.blocks):
            x = self.deblocks[-1](x)

        if self.upsample_featuremap:
            x = self.upsample_conv(x)

        return x, pyramid


class BaseBEVBackbone2(BaseBEVBackbone):
    def __init__(
        self,
        layer_nums,
        layer_strides,
        num_filters,
        upsample_strides,
        num_upsample_filters,
        input_channels,
        use_scconv=False,
        upsample_output=False,
        upsample_output_channel=-1,
    ):
        super(BaseBEVBackbone, self).__init__()
        # 几处改动：
        # 1）去除nn.ZeroPad层，直接在conv层中使用pad
        # 2）将interpolate 替换成了 deconv

        if layer_nums is not None:
            assert len(layer_nums) == len(layer_strides) == len(num_filters)
        else:
            layer_nums = layer_strides = num_filters = []

        if upsample_strides is not None:
            assert len(upsample_strides) == len(num_upsample_filters)
        else:
            upsample_strides = num_upsample_filters = []

        num_levels = len(layer_nums)
        c_in_list = [input_channels, *num_filters[:-1]]
        self.blocks = nn.ModuleList()
        self.deblocks = nn.ModuleList()
        for idx in range(num_levels):
            cur_layers = [
                nn.Conv2d(
                    c_in_list[idx],
                    num_filters[idx],
                    kernel_size=3,
                    stride=layer_strides[idx],
                    padding=1,
                    bias=False,
                ),
                nn.BatchNorm2d(num_filters[idx], eps=1e-3, momentum=0.01),
                nn.ReLU(inplace=True),
            ]
            for k in range(layer_nums[idx]):
                if not use_scconv:
                    cur_layers.extend(
                        [
                            nn.Conv2d(
                                num_filters[idx],
                                num_filters[idx],
                                kernel_size=3,
                                padding=1,
                                bias=False,
                            ),
                            nn.BatchNorm2d(num_filters[idx], eps=1e-3, momentum=0.01),
                            nn.ReLU(inplace=True),
                        ]
                    )
                else:
                    cur_layers.extend(
                        [
                            SCBottleneck2(
                                num_filters[idx],
                                num_filters[idx],
                                norm_layer=functools.partial(nn.BatchNorm2d, eps=1e-3, momentum=0.01),
                            )
                        ]
                    )

            self.blocks.append(nn.Sequential(*cur_layers))
            if len(upsample_strides) > 0:
                stride = upsample_strides[idx]
                if stride >= 1:
                    self.deblocks.append(
                        nn.Sequential(
                            nn.ConvTranspose2d(
                                num_filters[idx],
                                num_upsample_filters[idx],
                                upsample_strides[idx],
                                stride=upsample_strides[idx],
                                bias=False,
                            ),
                            nn.BatchNorm2d(num_upsample_filters[idx], eps=1e-3, momentum=0.01),
                            nn.ReLU(inplace=True),
                        )
                    )
                else:
                    stride = np.round(1 / stride).astype(np.int)
                    self.deblocks.append(
                        nn.Sequential(
                            nn.Conv2d(
                                num_filters[idx],
                                num_upsample_filters[idx],
                                stride,
                                stride=stride,
                                bias=False,
                            ),
                            nn.BatchNorm2d(num_upsample_filters[idx], eps=1e-3, momentum=0.01),
                            nn.ReLU(inplace=True),
                        )
                    )

        c_in = sum(num_upsample_filters)
        if len(upsample_strides) > num_levels:
            self.deblocks.append(
                nn.Sequential(
                    nn.ConvTranspose2d(
                        c_in,
                        c_in,
                        upsample_strides[-1],
                        stride=upsample_strides[-1],
                        bias=False,
                    ),
                    nn.BatchNorm2d(c_in, eps=1e-3, momentum=0.01),
                    nn.ReLU(inplace=True),
                )
            )

        self.num_bev_features = c_in

        self.upsample_featuremap = upsample_output
        if self.upsample_featuremap:
            if upsample_output_channel == -1:
                upsample_output_channel = c_in
            self.upsample_conv = nn.Sequential(
                nn.ConvTranspose2d(c_in, upsample_output_channel, 2, stride=2, bias=False),
                nn.BatchNorm2d(upsample_output_channel, eps=1e-3, momentum=0.01),
                nn.ReLU(inplace=True),
            )
