import torch
import math
from perceptron.layers.head.det3d.target_assigner.fcos_assigner import FCOSAssigner
from perceptron_ops.roiaware_pool3d.roiaware_pool3d_utils import points_in_boxes_gpu
from perceptron_ops.iou3d_nms.iou3d_nms_utils import boxes_iou_bev, boxes_iou3d_gpu

# 类似OTA，采用动态分配逻辑。（对框内的点，选topk个loss最小的目标作为positive)
# 固定K ? or 动态k (根据topQ个iou之和确定)
# 根据真实计算的iou or 预测的iou?
# 加入ignore逻辑。


class RVSegOtaAssignerIgnore(FCOSAssigner):
    """
    将target投影到球坐标系后映射到range view上。注意基于笛卡尔坐标系下的欧氏距离进行匹配是不稳定的
    选择 *落在gt_box内部的* topk个点。
    """

    def __init__(
        self,
        out_size_factor,
        tasks,
        dense_reg,
        gaussian_overlap,
        max_objs,
        min_radius,
        mapping,
        grid_size,
        pc_range,
        voxel_size,
        assign_topk,
        no_log=False,
        iou_type="3d",
        dynamic_assign_topk=False,
    ):
        super().__init__(
            out_size_factor,
            tasks,
            dense_reg,
            gaussian_overlap,
            max_objs,
            min_radius,
            mapping,
            grid_size,
            pc_range,
            voxel_size,
            assign_topk,
            no_log,
        )
        self.iou_type = iou_type
        self.dynamic_assign_topk = dynamic_assign_topk

    def generate_anchor_grid(self, featmap_size, offsets, stride, device):
        step_x, step_y = featmap_size
        shift = offsets * stride
        grid_x = torch.linspace(shift, (step_x - 1) * stride + shift, steps=step_x, device=device)
        grid_y = torch.linspace(shift, (step_y - 1) * stride + shift, steps=step_y, device=device)
        grids_x, grids_y = torch.meshgrid(grid_y, grid_x)
        return grids_x.reshape(-1), grids_y.reshape(-1)

    def assign_targets(
        self,
        rv_coords,
        gt_boxes,
        incl_array=None,
        ncols=1800,
        rv_col_offset=1350,
        rv_mv_origin=[0, 0, 0],
        rv_range=None,
        forward_ret_dict=None,
    ):
        """
        Args:
            rv_coords: B, 3, H, W
            gt_boxes: (B, M, C + cls)

            Updated at 2022/06/20
            incl_array:

        Returns:

        """

        pred_dicts = forward_ret_dict["multi_head_features"]
        max_objs = self._max_objs * self.dense_reg
        feature_map_size = (rv_coords.shape[3], rv_coords.shape[2])  # W, H
        ANC = feature_map_size[0] * feature_map_size[1]
        device = rv_coords.device

        # grids_x, grids_y = self.generate_anchor_grid(feature_map_size, 0.0, stride=1, device=gt_boxes.device)
        # anchor_points = torch.cat(
        #     [grids_y.unsqueeze(1), grids_x.unsqueeze(1)], dim=1
        # )  # (HW, 2). 存放顺序是 (col_idx, row_idx) / (x, y)

        batch_size = gt_boxes.shape[0]
        gt_classes = gt_boxes[:, :, -1]  # begin from 1
        gt_boxes = gt_boxes[:, :, :-1]

        heatmaps = {}
        gt_inds = {}
        gt_masks = {}
        gt_box_encodings = {}
        gt_cats = {}
        heatmap_weights = {}
        for task_id, task in enumerate(self.tasks):
            heatmaps[task_id] = []
            gt_inds[task_id] = []
            gt_masks[task_id] = []
            gt_box_encodings[task_id] = []
            gt_cats[task_id] = []
            heatmap_weights[task_id] = []

        for k in range(batch_size):
            cur_gt = gt_boxes[k]
            cnt = cur_gt.__len__() - 1
            while cnt > 0 and cur_gt[cnt].sum() == 0:
                cnt -= 1
            cur_gt = cur_gt[: cnt + 1]
            cur_gt_classes = gt_classes[k][: cnt + 1].int()

            # 过滤掉无效anchor
            cur_rv_coords = rv_coords[k]  # 3, H, W.  存放的是xyz
            nr_coord = cur_rv_coords.shape[0]
            cur_rv_coords = cur_rv_coords.view(nr_coord, -1).transpose(0, 1).contiguous()  # (3, HW) -> (HW, 3)
            invalid_anchor_mask = (cur_rv_coords == -1).all(dim=1)  # xyz均为-1的行
            valid_anchor_mask = ~invalid_anchor_mask

            # get ignore mask region

            class_name = "masked_area"
            class_idx = self.class_to_idx[class_name]
            class_mask = cur_gt_classes == class_idx
            cur_gt_of_task = cur_gt[class_mask]

            if len(cur_gt_of_task) > 0:
                # import ipdb
                # ipdb.set_trace()
                inbox_mask_int = points_in_boxes_gpu(
                    cur_rv_coords[None, :, :3].contiguous(), cur_gt_of_task[None].contiguous()
                )
                inbox_mask = inbox_mask_int >= 0
                inbox_mask = inbox_mask & valid_anchor_mask
                inbox_mask = inbox_mask.transpose(1, 0).reshape(1, feature_map_size[1], feature_map_size[0])
                non_ignore_mask_area = (~inbox_mask).float()
            else:
                non_ignore_mask_area = torch.ones((ANC, 1), device=device)

            for task_id, task in enumerate(self.tasks):
                heatmap = torch.zeros(
                    (len(task.class_names), feature_map_size[1], feature_map_size[0]), dtype=torch.float32
                ).to(cur_gt.device)
                gt_ind = torch.zeros(max_objs, dtype=torch.long).to(cur_gt.device)
                gt_mask = torch.zeros(max_objs, dtype=torch.bool).to(cur_gt.device)
                gt_cat = torch.zeros(max_objs, dtype=torch.long).to(cur_gt.device)
                gt_box_encoding = torch.zeros((max_objs, 8), dtype=torch.float32).to(cur_gt.device)

                cur_gts_of_task = []
                cur_classes_of_task = []
                class_offset = 0
                for class_name in task.class_names:
                    class_idx = self.class_to_idx[class_name]
                    class_mask = cur_gt_classes == class_idx
                    cur_gt_of_task = cur_gt[class_mask]
                    cur_class_of_task = cur_gt.new_full((cur_gt_of_task.shape[0],), class_offset).long()
                    cur_gts_of_task.append(cur_gt_of_task)
                    cur_classes_of_task.append(cur_class_of_task)
                    class_offset += 1

                cur_gts_of_task = torch.cat(cur_gts_of_task, dim=0)  # GT, 7
                cur_classes_of_task = torch.cat(cur_classes_of_task, dim=0)  # GT
                # num_boxes_of_task = cur_gts_of_task.shape[0]
                if len(cur_classes_of_task) == 0:
                    heatmaps[task_id].append(heatmap)
                    gt_inds[task_id].append(gt_ind)
                    gt_cats[task_id].append(gt_cat)
                    gt_masks[task_id].append(gt_mask)
                    gt_box_encodings[task_id].append(gt_box_encoding)
                    heatmap_weights[task_id].append(
                        non_ignore_mask_area.transpose(1, 0).reshape(1, feature_map_size[1], feature_map_size[0])
                    )
                    continue

                cur_gts_of_task[:, 6] = self.limit_period(cur_gts_of_task[:, 6], offset=0.5, period=math.pi * 2)

                # 2. 选择框内的topk个anchor作为positive
                topk = self.assign_topk
                assign_ids = rv_coords.new_ones((ANC), dtype=torch.long)

                inbox_mask_int = points_in_boxes_gpu(
                    cur_rv_coords[None, :, :3].contiguous(), cur_gts_of_task[None].contiguous()
                )
                inbox_mask = inbox_mask_int.new_zeros(
                    (cur_rv_coords.shape[0], cur_gts_of_task.shape[0] + 1), dtype=torch.bool
                )
                inbox_mask = torch.scatter(inbox_mask, 1, (inbox_mask_int + 1).permute(1, 0).long(), 1)
                inbox_mask = inbox_mask[:, 1:].contiguous()

                # 对每个gt box，挑选前topk个落在框内的点。
                pos_mask = torch.zeros((ANC), device=device)
                pos_mask = pos_mask.bool()
                nr_gt_boxes = inbox_mask.shape[1]

                pred_dict = pred_dicts[task_id]
                # 根据分类损失以及iou损失来决定positive anchor.
                batch_size = pred_dict["hm"].shape[0]
                # batch_hm = pred_dict['hm'].sigmoid_() inplace may cause errors
                batch_hm = pred_dict["hm"].sigmoid()  # BCHW
                batch_reg = pred_dict["reg"]
                batch_hei = pred_dict["height"]
                # batch_iousocre = pred_dict["iou"]

                if not self.no_log:
                    batch_dim = torch.exp(pred_dict["dim"])
                    # add clamp for good init, otherwise we will get inf with exp
                    batch_dim = torch.clamp(batch_dim, min=0.001, max=30)
                else:
                    batch_dim = pred_dict["dim"]

                batch_rots = pred_dict["rot"][:, 0].unsqueeze(1)
                batch_rotc = pred_dict["rot"][:, 1].unsqueeze(1)
                batch_hm = (
                    batch_hm[k].view(-1, feature_map_size[1] * feature_map_size[0]).transpose(1, 0)
                )  # CHW -> CN -> NC
                batch_reg = batch_reg[k].view(-1, feature_map_size[1] * feature_map_size[0]).transpose(1, 0)
                batch_hei = batch_hei[k].view(-1, feature_map_size[1] * feature_map_size[0]).transpose(1, 0)
                batch_dim = batch_dim[k].view(-1, feature_map_size[1] * feature_map_size[0]).transpose(1, 0)
                batch_rots = batch_rots[k].view(-1, feature_map_size[1] * feature_map_size[0]).transpose(1, 0)
                batch_rotc = batch_rotc[k].view(-1, feature_map_size[1] * feature_map_size[0]).transpose(1, 0)
                for gt_idx in range(nr_gt_boxes):
                    certain_gt_box_cat = cur_classes_of_task[gt_idx]  # 当前框label
                    # certain_gt_box_xyz = cur_gts_of_task[gt_idx : gt_idx + 1, :3]  # (1, 3)
                    in_certain_box_mask = inbox_mask[:, gt_idx]  # (ANCHOR_)

                    # 取出anchor cls score
                    anchor_hm = batch_hm[in_certain_box_mask]  # ANCHOR_, C
                    anchor_hm = anchor_hm[:, certain_gt_box_cat]

                    # 解框 & 计算真实iou

                    anchor_reg = batch_reg[in_certain_box_mask]  # ANCHOR_, 2
                    anchor_hei = batch_hei[in_certain_box_mask]  # ANCHOR_, 1
                    anchor_dim = batch_dim[in_certain_box_mask]  # ANCHOR_, 3
                    anchor_rots = batch_rots[in_certain_box_mask]
                    anchor_rotc = batch_rotc[in_certain_box_mask]
                    anchor_coords = cur_rv_coords[in_certain_box_mask]

                    anchor_box_center = anchor_coords + torch.cat((anchor_reg, anchor_hei), dim=1)
                    anchor_box_size = anchor_dim
                    anchor_box_angle = torch.atan2(anchor_rots, anchor_rotc)

                    anchor_box = torch.cat((anchor_box_center, anchor_box_size, anchor_box_angle), dim=1)

                    # import IPython; IPython.embed()

                    if self.iou_type == "3d":
                        anchor_box_real_iou = boxes_iou3d_gpu(
                            anchor_box, cur_gts_of_task[gt_idx : gt_idx + 1, :]
                        )  # ANCHOR_, 1
                    elif self.iou_type == "bev":
                        anchor_box_real_iou = boxes_iou_bev(
                            anchor_box, cur_gts_of_task[gt_idx : gt_idx + 1, :]
                        )  # ANCHOR_, 1
                    else:
                        assert False, f"Unsupported iou type {self.iou_type}"

                    anchor_box_real_iou = anchor_box_real_iou[:, 0]

                    # cost形式1： -(log(c) + log(iou)), 等价于 -log (c * iou)。 c*iou越大，cost 越小。
                    anchor_cost = -anchor_hm * anchor_box_real_iou

                    # 选择cost最小的k个样本
                    if self.dynamic_assign_topk:
                        # 动态选择topk，根据topk个iou之和来确定k。

                        topk_ious, _ = torch.topk(
                            anchor_box_real_iou, min(topk, anchor_box_real_iou.shape[0]), largest=True
                        )
                        dynamicK = topk_ious.sum().long().item()
                        topk = dynamicK

                    _, tmp_topk_inds = torch.topk(anchor_cost, min(topk, anchor_cost.shape[0]), largest=False)  # topk

                    # tmp_topk_inds = tmp_topk_inds[0]  # topk

                    in_centain_box_inds = torch.where(in_certain_box_mask)[0]

                    certain_positive_anchor_inds = in_centain_box_inds[tmp_topk_inds]  # 特定gt框内对应positive anchor的索引。

                    certain_positive_mask = torch.zeros((ANC), device=device)  # ANC,
                    certain_positive_mask[certain_positive_anchor_inds.flatten()] = 1
                    certain_positive_mask = (certain_positive_mask * valid_anchor_mask).bool()

                    pos_mask = pos_mask | certain_positive_mask
                    assign_ids[certain_positive_mask] = gt_idx

                pos_mask = pos_mask.bool()  # ANC

                # 3. 强制匹配top1，有topk保证其实就不需要了
                # _, top1_inds = torch.topk(center_offsets.t(), 1, largest=False)  # (GT, 1)
                # pos_mask[top1_inds.flatten()] = True

                # 4. 每个anchor point assign给最近的gt
                # _, gt_ids = center_offsets.min(dim=1)  # ANC,
                # pos_gt_ids = gt_ids[pos_mask]  # POS,
                pos_gt_ids = assign_ids[pos_mask]

                # 5. 生成targets
                gt_cat = cur_classes_of_task[pos_gt_ids]  # POS,
                gt_mask = torch.ones_like(pos_gt_ids)  # POS,
                gt_ind = torch.where(pos_mask == 1)[0]  # POS,

                heatmap = torch.zeros((ANC, len(task.class_names)), device=gt_cat.device)  # （ANC, num_class)
                heatmap[pos_mask] = heatmap[pos_mask].scatter_(1, gt_cat.unsqueeze(1), 1)
                heatmap = heatmap.transpose(1, 0).reshape(
                    len(task.class_names), feature_map_size[1], feature_map_size[0]
                )

                loc_targets = cur_gts_of_task[pos_gt_ids]  # POS, 7
                pos_anchor_points = cur_rv_coords[pos_mask]  # POS, 2
                # pos_anchor_points = anchor_points[pos_mask]  # POS, 2
                gt_box_encoding = torch.cat(
                    [
                        loc_targets[:, 0:3] - pos_anchor_points,  # x, y, z
                        torch.log(loc_targets[:, 3:4]),
                        torch.log(loc_targets[:, 4:5]),
                        torch.log(loc_targets[:, 5:6]),
                        torch.sin(loc_targets[:, 6:7]),
                        torch.cos(loc_targets[:, 6:7]),
                    ],
                    dim=1,
                ).to(
                    heatmap.device
                )  # POS, 7

                # padding to fixed shape
                gt_cat = self.padding_to_maxobjs(gt_cat, max_objs).long()
                gt_mask = self.padding_to_maxobjs(gt_mask, max_objs).bool()
                gt_ind = self.padding_to_maxobjs(gt_ind, max_objs).long()
                gt_box_encoding = self.padding_to_maxobjs(gt_box_encoding, max_objs).float()

                heatmap_weight = non_ignore_mask_area.transpose(1, 0).reshape(
                    1, feature_map_size[1], feature_map_size[0]
                )
                heatmap_weights[task_id].append(heatmap_weight)
                heatmaps[task_id].append(heatmap)
                gt_inds[task_id].append(gt_ind)
                gt_cats[task_id].append(gt_cat)
                gt_masks[task_id].append(gt_mask)
                gt_box_encodings[task_id].append(gt_box_encoding)

        for task_id, tasks in enumerate(self.tasks):
            heatmaps[task_id] = torch.stack(heatmaps[task_id], dim=0).contiguous()
            heatmap_weights[task_id] = torch.stack(heatmap_weights[task_id], dim=0).contiguous()
            gt_inds[task_id] = torch.stack(gt_inds[task_id], dim=0).contiguous()
            gt_masks[task_id] = torch.stack(gt_masks[task_id], dim=0).contiguous()
            gt_cats[task_id] = torch.stack(gt_cats[task_id], dim=0).contiguous()
            gt_box_encodings[task_id] = torch.stack(gt_box_encodings[task_id], dim=0).contiguous()

        target_dict = {
            "heatmap": heatmaps,
            "ind": gt_inds,
            "mask": gt_masks,
            "cat": gt_cats,
            "box_encoding": gt_box_encodings,
            "heatmap_weights": heatmap_weights,
        }
        return target_dict
