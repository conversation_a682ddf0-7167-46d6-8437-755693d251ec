import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from perceptron.utils.torch_dist import reduce_mean


class OhemCELoss(nn.Module):
    def __init__(self, thresh, n_min, ignore_lb=255, *args, **kwargs):
        super(Oh<PERSON><PERSON><PERSON><PERSON>, self).__init__()
        self.thresh = torch.tensor(thresh, dtype=torch.float)
        self.n_min = n_min
        self.ignore_lb = ignore_lb
        self.criteria = nn.CrossEntropyLoss(ignore_index=ignore_lb, reduction="none")

    def forward(self, logits, labels):
        loss = self.criteria(logits, labels).view(-1)
        loss, _ = torch.sort(loss, descending=True)
        if loss[self.n_min] > self.thresh:
            loss = loss[loss > self.thresh]
        else:
            loss = loss[: self.n_min]
        return torch.mean(loss)


def enet_weighing(label, num_classes, c=1.02):
    """Computes class weights as described in the ENet paper:
        w_class = 1 / (ln(c + p_class)),
    where c is usually 1.02 and p_class is the propensity score of that
    class:
        propensity_score = freq_class / total_pixels.
    References: https://arxiv.org/abs/1606.02147
    Keyword arguments:
    - dataloader (``data.Dataloader``): A data loader to iterate over the
    dataset.
    - num_classes (``int``): The number of classes.
    - c (``int``, optional): AN additional hyper-parameter which restricts
    the interval of values for the weights. Default: 1.02.
    """
    class_count = 0
    total = 0

    label = label.cpu().numpy()

    # Flatten label
    flat_label = label.flatten()

    # Sum up the number of pixels of each class and the total pixel
    # counts for each label
    class_count += np.bincount(flat_label, minlength=num_classes)
    total += flat_label.size

    # Compute propensity score and then the weights for each class
    propensity_score = class_count / total
    class_weights = 1 / (np.log(c + propensity_score))

    class_weights = torch.from_numpy(class_weights).float()

    return class_weights[:num_classes]


class WeightedOhemCELoss(nn.Module):
    def __init__(self, thresh, n_min, num_classes, ignore_lb=255, *args, **kwargs):
        super(WeightedOhemCELoss, self).__init__()
        self.thresh = thresh
        self.n_min = n_min
        self.ignore_lb = ignore_lb
        self.num_classes = num_classes

    def forward(self, logits, labels):
        criteria = nn.CrossEntropyLoss(
            weight=enet_weighing(labels, self.num_classes), ignore_index=self.ignore_lb, reduction="none"
        )
        loss = criteria(logits, labels).view(-1)
        loss, _ = torch.sort(loss, descending=True)
        if loss[self.n_min] > self.thresh:
            loss = loss[loss > self.thresh]
        else:
            loss = loss[: self.n_min]
        return torch.mean(loss)


def dice_loss_func(input, target):
    smooth = 1.0
    n = input.size(0)
    iflat = input.view(n, -1)
    tflat = target.view(n, -1)
    intersection = (iflat * tflat).sum(1)
    loss = 1 - ((2.0 * intersection + smooth) / (iflat.sum(1) + tflat.sum(1) + smooth))
    return loss.mean()


class DetailAggregateLoss(nn.Module):
    def __init__(self, *args, **kwargs):
        super(DetailAggregateLoss, self).__init__()

        self.laplacian_kernel = (
            torch.tensor([-1, -1, -1, -1, 8, -1, -1, -1, -1], dtype=torch.float32)
            .reshape(1, 1, 3, 3)
            .requires_grad_(False)
            .type(torch.cuda.FloatTensor)
        )

        self.fuse_kernel = torch.nn.Parameter(
            torch.tensor([[6.0 / 10], [3.0 / 10], [1.0 / 10]], dtype=torch.float32)
            .reshape(1, 3, 1, 1)
            .type(torch.cuda.FloatTensor)
        )

    def forward(self, boundary_logits, gtmasks):

        boundary_targets = F.conv2d(gtmasks.unsqueeze(1).type(torch.cuda.FloatTensor), self.laplacian_kernel, padding=1)
        boundary_targets = boundary_targets.clamp(min=0)
        boundary_targets[boundary_targets > 0.1] = 1
        boundary_targets[boundary_targets <= 0.1] = 0

        boundary_targets_x2 = F.conv2d(
            gtmasks.unsqueeze(1).type(torch.cuda.FloatTensor), self.laplacian_kernel, stride=2, padding=1
        )
        boundary_targets_x2 = boundary_targets_x2.clamp(min=0)

        boundary_targets_x4 = F.conv2d(
            gtmasks.unsqueeze(1).type(torch.cuda.FloatTensor), self.laplacian_kernel, stride=4, padding=1
        )
        boundary_targets_x4 = boundary_targets_x4.clamp(min=0)

        boundary_targets_x8 = F.conv2d(
            gtmasks.unsqueeze(1).type(torch.cuda.FloatTensor), self.laplacian_kernel, stride=8, padding=1
        )
        boundary_targets_x8 = boundary_targets_x8.clamp(min=0)

        boundary_targets_x8_up = F.interpolate(boundary_targets_x8, boundary_targets.shape[2:], mode="nearest")
        boundary_targets_x4_up = F.interpolate(boundary_targets_x4, boundary_targets.shape[2:], mode="nearest")
        boundary_targets_x2_up = F.interpolate(boundary_targets_x2, boundary_targets.shape[2:], mode="nearest")

        boundary_targets_x2_up[boundary_targets_x2_up > 0.1] = 1
        boundary_targets_x2_up[boundary_targets_x2_up <= 0.1] = 0

        boundary_targets_x4_up[boundary_targets_x4_up > 0.1] = 1
        boundary_targets_x4_up[boundary_targets_x4_up <= 0.1] = 0

        boundary_targets_x8_up[boundary_targets_x8_up > 0.1] = 1
        boundary_targets_x8_up[boundary_targets_x8_up <= 0.1] = 0

        boudary_targets_pyramids = torch.stack(
            (boundary_targets, boundary_targets_x2_up, boundary_targets_x4_up), dim=1
        )

        boudary_targets_pyramids = boudary_targets_pyramids.squeeze(2)
        boudary_targets_pyramid = F.conv2d(boudary_targets_pyramids, self.fuse_kernel)

        boudary_targets_pyramid[boudary_targets_pyramid > 0.1] = 1
        boudary_targets_pyramid[boudary_targets_pyramid <= 0.1] = 0

        if boundary_logits.shape[-1] != boundary_targets.shape[-1]:
            boundary_logits = F.interpolate(
                boundary_logits, boundary_targets.shape[2:], mode="bilinear", align_corners=True
            )

        bce_loss = F.binary_cross_entropy_with_logits(boundary_logits, boudary_targets_pyramid)
        dice_loss = dice_loss_func(torch.sigmoid(boundary_logits), boudary_targets_pyramid)
        return bce_loss, dice_loss

    def get_params(self):
        nowd_params = []
        for name, module in self.named_modules():
            nowd_params += list(module.parameters())
        return nowd_params


class HeatMapLoss(torch.nn.Module):
    def __init__(self, gamma=2, beta=4):
        super().__init__()
        self.gamma = gamma
        self.beta = beta

    def forward(self, pred, gt, mask=None):
        positive_mask = abs(gt - 1) < 5e-2
        negative_mask = abs(gt - 1) > 5e-2
        loss_positive = ((1 - pred) * positive_mask) ** self.gamma * torch.log(pred * positive_mask + 1e-9)
        loss_negative = (
            ((1 - gt) * negative_mask) ** self.beta
            * (pred * negative_mask) ** self.gamma
            * torch.log((1 - pred) * negative_mask + 1e-9)
        )
        loss = -torch.mean(loss_negative + loss_positive)

        return loss


class OffsetVecLoss(torch.nn.Module):
    def __init__(self, mask_threshold=100):
        super().__init__()
        self.loss = nn.L1Loss(reduction="mean")
        self.mask_threshold = mask_threshold

    def forward(self, pred, gt):
        loss_mask = abs(gt) < self.mask_threshold
        pred = pred * loss_mask
        gt = gt * loss_mask
        return self.loss(pred, gt)


class MultiClassFocalLoss(nn.Module):
    """Multiclass focal loss inherit from losses.det3d.FocalLoss"""

    def __init__(self, alpha, gamma, eps=1e-8, num_classes=-1, ignore_labels=[-1]):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.eps = eps
        self.num_classes = num_classes
        self.ignore_labels = ignore_labels

    def _neg_loss(self, pred, gt):
        """
        Arguments:
          pred (batch x n_class x h x w)
          gt_regr (batch x n_class x h x w)
        """
        pos_inds = gt.eq(1).long()

        loss = 0

        pos_loss = torch.log(pred + self.eps) * torch.pow(1 - pred, self.gamma) * pos_inds * self.alpha
        num_pos = pos_inds.float().sum()

        pos_loss = pos_loss.sum()
        num_pos = reduce_mean(num_pos)

        loss = loss - pos_loss / (num_pos + self.eps)
        return loss

    def forward(self, out, target):
        if len(target.shape) != len(out.shape):
            target_onehot = torch.clone(target)
            for l in self.ignore_labels:
                target_onehot[target == l] = self.num_classes  # process ignore_label
            target_onehot = F.one_hot(target_onehot, num_classes=self.num_classes + 1)
            target_onehot = target_onehot[..., : self.num_classes]  # pop ignore label
        else:
            target_onehot = target
        loss = self._neg_loss(out, target_onehot)
        return loss


class CrossEntropyLabelSmooth(nn.Module):
    """Cross entropy loss with label smoothing regularizer.

    Reference:
    Szegedy et al. Rethinking the Inception Architecture for Computer Vision. CVPR 2016.
    Equation: y = smooth_lambda * y + ( 1- smooth_lambda) / (C - 1) * (1 - y).
    NOTE: different from y = smooth_lambda * y + ( 1- smooth_lambda) / C.

    Args:
        num_classes (int): number of classes.
        smooth_lambda (float): weight.
    """

    def __init__(self, num_classes, smooth_lambda=1.0, ignore_labels=[-1]):
        super(CrossEntropyLabelSmooth, self).__init__()
        self.num_classes = num_classes
        self.ignore_labels = ignore_labels
        self.smooth_lambda = smooth_lambda
        self.logsoftmax = nn.LogSoftmax(dim=-1)

    def forward(self, input, target, smooth_lambda=None):
        """
        Args:
            inputs: prediction matrix (before softmax) with shape (batch_size, ..., num_classes)
            target: ground truth labels with shape (batch_size, ..., num_classes) or (batch_size, ...).
                    if the former, target is one-hot.
            smooth_lambda: label smooth lambda for each instance with shape = (batch_size).
        """
        if len(target.shape) != len(input.shape):
            # one-hot and apply smooth lambda
            target_onehot = torch.clone(target)
            for l in self.ignore_labels:
                target_onehot[target == l] = self.num_classes  # process ignore_label
            target_onehot = F.one_hot(target_onehot, num_classes=self.num_classes + 1)
            target_onehot = target_onehot[..., : self.num_classes]  # pop ignore label
        else:
            target_onehot = target

        if smooth_lambda is None:
            smooth_lambda = self.smooth_lambda
        else:
            ndim = len(target_onehot.shape)
            smooth_lambda = smooth_lambda.view([-1] + [1] * (ndim - 1))  # bs, 1, 1, 1

        target_onehot = self.smooth_lambda * target_onehot + (1 - self.smooth_lambda) / (self.num_classes - 1) * (
            1 - target_onehot
        )
        log_probs = self.logsoftmax(input)
        loss = (-target_onehot * log_probs).sum(dim=-1).mean()
        return loss
