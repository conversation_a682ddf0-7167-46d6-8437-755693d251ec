"""只是简单地把 rv gt / rv dt / bev 拼接起来
上下排布，上面是 dt / 下面是 gt
"""

import cv2
import os
import glob
import numpy as np
from tqdm import tqdm

import cv2
import os
import glob
import numpy as np


def create_video_from_images(frame_rate, image_folder, video_output_path, rv_size = (600, 320), bev_size = (160, 320)):
    # 获取文件夹下所有的图片
    image_files = glob.glob(os.path.join(image_folder, "*.jpg"))
    
    # 初始化视频的宽和高，拼接四张图片
    video_height = rv_size[1] + bev_size[1]
    video_width = rv_size[0] + bev_size[0]
    
    # 使用 mp4v 编码器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    video_writer = cv2.VideoWriter(video_output_path, fourcc, frame_rate, (video_width, video_height))
    
    sort_image_files = sorted(enumerate(image_files), key=lambda x: float(x[1].split('/')[-1].split('_')[5]),
                            reverse=True)  # 从左到右，从后到前排序
    # for path in image_files:
    #     print(float(path.split('/')[-1].split('_')[5]))
    # 预处理：按照 vis_idx 将图片分类
    image_dict = {}
    for index, img_file in sort_image_files:
        img_name = os.path.basename(img_file)
        vis_idx = int(img_name.split('_')[0])

        if vis_idx not in image_dict:
            image_dict[vis_idx] = {}

        if img_name.endswith("rv_dt.jpg"):
            image_dict[vis_idx]['rv_dt'] = img_file
        elif img_name.endswith("bev_dt.jpg"):
            image_dict[vis_idx]['bev_dt'] = img_file
        elif img_name.endswith("rv_gt.jpg"):
            image_dict[vis_idx]['rv_gt'] = img_file
        elif img_name.endswith("bev_gt.jpg"):
            image_dict[vis_idx]['bev_gt'] = img_file

    # 处理并生成视频帧
    # length = max(list(image_dict.keys()))
    for index, img_file in sort_image_files[0:400:4]:
        img_name = os.path.basename(img_file)
        vis_idx = int(img_name.split('_')[0])
        if vis_idx not in image_dict:
            continue

        rv_dt_img = cv2.imread(image_dict[vis_idx].get('rv_dt', ''))
        bev_dt_img = cv2.imread(image_dict[vis_idx].get('bev_dt', ''))
        rv_gt_img = cv2.imread(image_dict[vis_idx].get('rv_gt', ''))
        bev_gt_img = cv2.imread(image_dict[vis_idx].get('bev_gt', ''))

        
        # 如果 rv_dt 或 rv_gt 不存在，则跳过该 vis_idx
        if rv_dt_img is None or rv_gt_img is None:
            continue

        # Resize rv 和 bev 图像
        rv_dt_img = cv2.resize(rv_dt_img, rv_size)
        rv_gt_img = cv2.resize(rv_gt_img, rv_size)

        # 如果 bev 图像不存在，用白板代替
        if bev_dt_img is None:
            bev_dt_img = np.ones((bev_size[1], bev_size[0], 3), dtype=np.uint8) * 255
        else:
            bev_dt_img = cv2.resize(bev_dt_img, bev_size)

        if bev_gt_img is None:
            bev_gt_img = np.ones((bev_size[1], bev_size[0], 3), dtype=np.uint8) * 255
        else:
            bev_gt_img = cv2.resize(bev_gt_img, bev_size)
        
        # 在 rv_dt_img 的左上角写上 'pred'
        cv2.putText(rv_dt_img, 'DT', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2, cv2.LINE_AA)
        
        # 在 rv_gt_img 的左上角写上 'gt'
        cv2.putText(rv_gt_img, 'GT', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2, cv2.LINE_AA)

        # 拼接图像

        # 拼接图像
        top_row = np.hstack((rv_dt_img, bev_dt_img))
        bottom_row = np.hstack((rv_gt_img, bev_gt_img))
        final_frame = np.vstack((top_row, bottom_row))
        
        # 将拼接的图像写入视频
        # cv2.imwrite('tt.jpg', final_frame)
        video_writer.write(final_frame)

    # 释放视频写入器
    video_writer.release()
    print(f"Video saved to {video_output_path}")

if __name__ == "__main__":
    # 图片命名规则  "{vis_idx}_scene_{scene_id}_frame_id_{frame_id}_{step_id}_{bs_id}_rv_dt.jpg"   其中 step_id 和 bs_id 和测试时使用的 bs 有关
    # 示例调用
    frame_rate = 5
    image_folder = "/data/Perceptron-e2e/outputs/maptracker__maptracker_exp_hf_2v0r_single_attr_cam30_permute_less_aw/2024-11-17T03:05:00/pl_vis/"           # 'path/to/your/image/folder'
    video_output_path = "/data/Perceptron-e2e/outputs/maptracker__maptracker_exp_hf_2v0r_single_attr_cam30_permute_less_aw/2024-11-17T03:05:00/ep11_seg_attr_top100.mp4"  # 'path/to/output/video.mp4'
    rv_size = (960, 512)  # rv 的目标分辨率
    bev_size = (250, 512)

    tmp_path = video_output_path.replace('.mp4', '_tmp.mp4')
    create_video_from_images(frame_rate, image_folder, tmp_path, rv_size, bev_size)
    import os
    os.system(f'ffmpeg -i {tmp_path} -c:v libx264 {video_output_path}')
    os.system(f'rm {tmp_path}')
    
    