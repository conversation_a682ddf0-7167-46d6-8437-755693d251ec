"""双线合并相关逻辑
"""

from shapely.geometry import LineString, Point, MultiLineString
import matplotlib.pyplot as plt
from shapely.ops import split
import numpy as np
from collections import defaultdict
import pandas as pd
from perceptron.utils.maptracker_utils.line_merge_utils import find_connected_components
from pdb import set_trace as ste


def split_line_by_buffer(line_a, line_b, buffer_distance, filter_length=1.0):
    assert isinstance(line_a, LineString), "line1 must be a LineString"
    assert isinstance(line_b, LineString), "line2 must be a LineString"

    # 对线 A 生成缓冲区
    buffer_a = line_a.buffer(buffer_distance, join_style=3, cap_style=2)
    
    # 使用缓冲区对线 B 进行切分
    split_lines = list(split(line_b, buffer_a).geoms)
    
    # 初始化结果列表
    result = []
    
    # 遍历切分后的线段
    cnt = 0
    idx = 0
    for line in split_lines:
        # 检查线段是否与缓冲区相交
        if line.length < filter_length:  # 不在重叠区域内的长度小于阈值，就去掉
                continue
        
        flag = line.intersects(buffer_a)
        if flag:
            inter = line.intersection(buffer_a)
            if isinstance(inter, LineString):
                if inter.length < 0.1:
                    flag = False
            elif isinstance(inter, Point):
                flag = False   

        if flag:
            # 如果相交，将线段添加到结果列表，并标记为 True
            cnt += 1
            result.append((line, True, idx))
            idx += 1
        else:
            # 如果不相交，将线段添加到结果列表，并标记为 False
            result.append((line, False, idx))
            idx += 1
    if cnt != 1:
        return None   # 有多段相交区域，不符合预期，不合并
    return result

def compute_center_line(intersect_a, intersect_b, inter_num=20):
    if not isinstance(intersect_a, LineString):
        intersect_a = LineString(intersect_a)
        intersect_b = LineString(intersect_b)

    distances_a = np.linspace(0, intersect_a.length, inter_num)
    sampled_points_a = np.array([list(intersect_a.interpolate(distance).coords) for distance in distances_a]).squeeze()

    distances_b = np.linspace(0, intersect_b.length, inter_num)
    sampled_points_b = np.array([list(intersect_b.interpolate(distance).coords) for distance in distances_b]).squeeze()

    merged_line = (sampled_points_a + sampled_points_b) / 2
    return merged_line


def merge_two_line_with_attr(intersect_a, intersect_b, attr_a, attr_b):
    assert isinstance(intersect_a, LineString), "linea must be a LineString"
    assert isinstance(intersect_b, LineString), "lineb must be a LineString"

    # 1. 如果是一虚一实，保留实线位置，同时如果虚线 x 坐标小，就左虚右实；如果实线 x 坐标小，就左实右虚 （横向会有问题但不重要)
    type_a= attr_a['lane_type']
    dashed_a = type_a.get('dashed', 'NAN')

    type_b = attr_b['lane_type']
    dashed_b = type_b.get('dashed', 'NAN')

    color_a = type_a.get('color', 'NAN')
    color_b = type_b.get('color', 'NAN')

    if 'yellow' in [color_a, color_b]:
        merged_color = 'yellow'
    elif 'white' in [color_a, color_b]:
        merged_color = 'white'
    else:
        merged_color = 'NAN'

    # 如果是一虚一实，保留实线位置，如果虚线 x 坐标小，就左虚右实；实线 x 坐标小就左实右虚
    if (dashed_a == 'dotted' and dashed_b == 'solid') or (dashed_b == 'dotted' and dashed_a == 'solid'):  
        min_x_a = np.array(intersect_a.coords)[:, 1].min()  # 横向最小值，ego 坐标系 y 轴朝左，值越小越偏右
        min_x_b = np.array(intersect_b.coords)[:, 1].min()  # 
        if dashed_a == 'solid':
            merged_line = np.array(intersect_a.coords)
            if min_x_a < min_x_b:
                merged_dashed = 'dotted+solid'
            else:
                merged_dashed = 'solid+dotted'
        else:
            merged_line = np.array(intersect_b.coords)
            if min_x_a < min_x_b:
                merged_dashed = 'solid+dotted' 
            else:
                merged_dashed = 'dotted+solid'

        merged_type = dict(
            dashed=merged_dashed,
            shape='double',
            color=merged_color,
        )
    # 双实线 / 双虚线
    elif (dashed_a == 'dotted' and dashed_b == 'dotted') or (dashed_a == 'solid' and dashed_b == 'solid'):
        merged_line = compute_center_line(intersect_a, intersect_b)
        merged_type = dict(
            dashed=dashed_a,
            shape='double',
            color=merged_color,
        )
    else:  # 暂时考虑不到的情况
        merged_line = compute_center_line(intersect_a, intersect_b)
        merged_type = dict(
            dashed=dashed_a,  # 随便挑一个
            shape='double',
            color=merged_color,
        )
    # 其它属性整理
    other_attr = {}
    for attr in ['cone_barrel', 'crossable', 'diversion_line', 'fishbone', 'guardrail', 'parking_line', 'road_curb', 'water_barrier', 'waiting_zone', 'guide_line', 'height', 'direction', 'self_id']:
        other_attr[attr] = 'NAN'
        if attr_b.get(attr, 'NAN') != 'NAN':
            other_attr[attr] = attr_b.get(attr, 'NAN')
        if attr_a.get(attr, 'NAN') != 'NAN':
            other_attr[attr] = attr_a.get(attr, 'NAN')
    other_attr['lane_type'] = merged_type

    return merged_line, other_attr

def merge_double_lines(line_a, attr_a, line_b, attr_b, buffer_distance, filter_length=1.0):
    line_a = LineString(line_a)
    line_b = LineString(line_b)

    # 1. line_a 生成缓冲区，对 B 切分
    try:
        split_lines_b = split_line_by_buffer(line_a, line_b, buffer_distance, filter_length)   # 切完都是 linestring 类型
    except:
        return [np.array(line_a.coords).tolist(), np.array(line_b.coords).tolist()], [attr_a, attr_b]

    if split_lines_b is None:  # 有异常情况
        return [np.array(line_a.coords).tolist(), np.array(line_b.coords).tolist()], [attr_a, attr_b]

    # 2. 取缓冲区内的 b
    intersect_b = [tmp_b for tmp_b in split_lines_b if tmp_b[1]]
    idx_b = intersect_b[0][2]
    intersect_b = intersect_b[0][0]

    # 3. intersect_b 生成缓冲区，对 a 切分
    try:
        split_lines_a = split_line_by_buffer(intersect_b, line_a, buffer_distance, filter_length)  # 切完都是 linestring 类型
    except:
        return [np.array(line_a.coords).tolist(), np.array(line_b.coords).tolist()], [attr_a, attr_b]
    
    if split_lines_a is None:
        return [np.array(line_a.coords).tolist(), np.array(line_b.coords).tolist()], [attr_a, attr_b]
    
    intersect_a = [tmp_a for tmp_a in split_lines_a if tmp_a[1]]
    idx_a = intersect_a[0][2]
    intersect_a = intersect_a[0][0]

    # 4. 重排 split_lines_a，保证 intersect_a / intersect_b 是同一方向的
    start_a = np.array(intersect_a.coords)[0]
    end_a = np.array(intersect_a.coords)[-1]
    start_b = np.array(intersect_b.coords)[0]

    dist_ab_start_start = np.linalg.norm(start_a - start_b)
    dist_ab_start_end = np.linalg.norm(end_a - start_b)

    new_split_lines_a = []
    if dist_ab_start_start > dist_ab_start_end:
        intersect_a = intersect_a.reverse()
        # 重排 split_lines_a
        max_idx = len(split_lines_a) - 1
        idx_a = max_idx - idx_a
        for item in split_lines_a:
            line, flag, idx = item
            new_line = line.reverse()
            idx = max_idx - idx
            new_split_lines_a.append((new_line, flag, idx))

    # 5. 合并重叠区域的线
    merged_line, merged_type = merge_two_line_with_attr(intersect_a, intersect_b, attr_a, attr_b)
    start_mergeline, end_mergeline = merged_line[0].tolist(), merged_line[-1].tolist()
    merged_line = merged_line.tolist()
    # 6. 整理输出, 主要是连接处，需要和前后的线衔接起来
    res_line, res_type = [], []
    for idx in range(len(split_lines_a)):
        line = np.array(split_lines_a[idx][0].coords).tolist()
        if idx == idx_a - 1:
            line.append(start_mergeline)
            type = attr_a

        elif idx == idx_a + 1:
            line = [end_mergeline] + line
            type = attr_a

        elif idx == idx_a:
            line = merged_line
            type = merged_type

        else:
            type = attr_a
            
        res_line.append(line)
        res_type.append(type)
    
    for idx in range(len(split_lines_b)):
        line = np.array(split_lines_b[idx][0].coords).tolist()
        type = attr_b
        if idx == idx_b - 1:
            line.append(start_mergeline)

        elif idx == idx_b + 1:
            line = [end_mergeline] + line
            
        elif idx == idx_b:  # 已经加入到 res 里了，避免重复
            continue

        res_line.append(line)
        res_type.append(type)
    return res_line, res_type


def check_if_double(line1, line2, dist_thre, inter_length_thre=10, class_name=None):
    # 检查两条线是否相邻 / 构成双线
    line1 = np.array(line1)
    line2 = np.array(line2)
    # 1. 粗糙地根据区间判断，会比较快滤除一些较远的线
    min_x1, max_x1 = np.min(line1[:, 0]), np.max(line1[:, 0])
    min_x2, max_x2 = np.min(line2[:, 0]), np.max(line2[:, 0])

    min_y1, max_y1 = np.min(line1[:, 1]), np.max(line1[:, 1])
    min_y2, max_y2 = np.min(line2[:, 1]), np.max(line2[:, 1])

    intervalx1 = pd.Interval(min_x1, max_x1)
    intervalx2 = pd.Interval(min_x2, max_x2)
    overlap_x = intervalx1.overlaps(intervalx2)
    
    intervaly1 = pd.Interval(min_y1, max_y1)
    intervaly2 = pd.Interval(min_y2, max_y2)
    overlap_y = intervaly1.overlaps(intervaly2)
    if not (overlap_x or overlap_y):
        return False
    
    # 2. 细粒度地根据距离阈值判断
    line1 = LineString(line1)
    line2 = LineString(line2)
    buffer1 = line1.buffer(dist_thre, join_style=3, cap_style=2)
    buffer2 = line2.buffer(dist_thre, join_style=3, cap_style=2)
    
    if line2.intersects(buffer1) and line1.intersects(buffer2):
        # 计算共有区域
        intersection_ab = line2.intersection(buffer1)
        intersection_ba = line1.intersection(buffer2)

        # 确保交集是 LineString 或 MultiLineString
        if isinstance(intersection_ab, LineString) and \
        isinstance(intersection_ba, LineString):
            pass
            # 继续处理
        else:
            return False  # 或者跳过当前处理逻辑

        # 计算首尾距离
        # 获取每条线的起点和终点
        start1, end1 = intersection_ab.coords[0], intersection_ab.coords[-1]
        start2, end2 = intersection_ba.coords[0], intersection_ba.coords[-1]
        start1 = np.array(start1)
        end1 = np.array(end1)
        start2 = np.array(start2)
        end2 = np.array(end2)

        # 计算四个组合之间的欧氏距离
        dist_ss = np.linalg.norm(start1 - start2)  # 线1起点 <-> 线2起点
        dist_se = np.linalg.norm(start1 - end2)    # 线1起点 <-> 线2终点
        dist_es = np.linalg.norm(end1 - start2)    # 线1终点 <-> 线2起点
        dist_ee = np.linalg.norm(end1 - end2)      # 线1终点 <-> 线2终点
        
        dis_min_1 = min(dist_ss, dist_se)
        dis_min_2 = min(dist_es, dist_ee)

        # 如果两个值的差值小于0.2, 且最小值大于6cm, 判定为双线..
        if class_name == "white":
            # 需要非常严格!
            if abs(dis_min_1 - dis_min_2) < 0.2 and min(dis_min_1, dis_min_2) > 0.06 and intersection_ab.length > inter_length_thre:
                return True
        else:
            # 黄线合并可以宽松一些
            if intersection_ab.length > inter_length_thre:
                return True
    return False

def merge_lines_samecls(cls_info, dist_thre, filter_length=1, class_name=None):
    if len(cls_info) == 0:
        return {}
    
    merged_keys = set()
    merge_graph = defaultdict(list)
    for key1 in cls_info:
        for key2 in cls_info:
            if key1 == key2:
                continue
            line1 = cls_info[key1]['points']
            line2 = cls_info[key2]['points']
            if check_if_double(line1, line2, dist_thre, filter_length, class_name):
                merge_graph[key1].append((key2))
                merged_keys.add(key1)
                merged_keys.add(key2)
    if len(merged_keys) == 0:
        return cls_info
    
    merged_lines, merged_types = [], []
    components = find_connected_components(merge_graph)
    for comp in components:
        if len(comp) > 2:  # 如果有多条线并列，选里面最长的两条做 merge
            lengths = []
            for key in comp:
                tmp_line = cls_info[key]['points']
                tmp_length = LineString(tmp_line).length
                lengths.append(tmp_length)
            sorted_with_indices = sorted(enumerate(lengths), key=lambda x: x[1], reverse=True)
            top1, top2 = sorted_with_indices[0][0], sorted_with_indices[1][0]
            key1, key2 = comp[top1], comp[top2]
        else:
            key1, key2 = comp
        line1 = cls_info[key1]['points']
        line2 = cls_info[key2]['points']
        attr1 = cls_info[key1]['attribute']
        attr2 = cls_info[key2]['attribute']
        merged_line, merged_type = merge_double_lines(line1, 
                                                        attr1, 
                                                        line2, 
                                                        attr2, 
                                                        buffer_distance=dist_thre,
                                                        filter_length=filter_length)
        merged_lines.extend(merged_line)
        merged_types.extend(merged_type)
    
    # 组织输出
    cnt = 0
    new_cls_info = {}
    for key in cls_info:
        if key not in merged_keys:
            new_cls_info[cnt] = cls_info[key]
        cnt += 1
    
    for line, type in zip(merged_lines, merged_types):
        new_cls_info[cnt] = {
            'points': line,
            'attribute': type,
        }
        cnt += 1

    return new_cls_info


def merge_double_line_all(cls_info, dist_thre=0.8, filter_length=5):
#   # 1. 检查两条线是否有重合区域：
        # 1.1 coarse: 范围检查, max_x, max_y, min_x, min_y, 看有无重叠区域，如果没有重叠区域，必然不重合
        # 1.2 fine: 如果满足上面 coarse 的条件，再用 shapely 看是否有重叠区域
    #   2. 如果 A B 有重叠：
        # 2.1 A 
            # 2.1 找重叠区域，在不重合处断开
            #    重合部分合并成一条线,
            #        a. 如果一实一虚，就去掉虚线，另一条线给左实右虚、左虚右实 属性
            #        b. 如果双虚或者双实线，就求中线
            #    不重合区域保留原始属性和线形
    # 区分白线和黄线，分别合并白线和黄线
    white, yellow, others = {}, {}, {}
    for key in cls_info:
        if cls_info[key]['attribute']['lane_type']['color'] == 'yellow':
            yellow[key] = cls_info[key]
        elif cls_info[key]['attribute']['lane_type']['color'] == 'white':
            white[key] = cls_info[key]
        else:
            others[key] = cls_info[key]

    white = merge_lines_samecls(white, dist_thre, filter_length, class_name='white')
    yellow = merge_lines_samecls(yellow, dist_thre, filter_length, class_name='yellow')
    others = merge_lines_samecls(others, dist_thre, filter_length, class_name='others')

    # 再进行白线和黄线的合并, 只有当都是单线时才会合并
    sel1, sel2 = [], []
    merged_lines, merged_types = [], []
    for key1 in white:
        for key2 in yellow:
            line1 = white[key1]['points']
            line2 = yellow[key2]['points']
            attr1 = white[key1]['attribute']
            attr2 = yellow[key2]['attribute']
            if attr1['lane_type']['shape'] == 'single' \
                and attr2['lane_type']['shape'] == 'single' \
                and check_if_double(line1, line2, dist_thre, filter_length):
                merged_line, merged_type = merge_double_lines(line1, 
                                                                attr1, 
                                                                line2, 
                                                                attr2, 
                                                                buffer_distance=dist_thre,
                                                                filter_length=filter_length)
                sel1.append(key1)
                sel2.append(key2)
                merged_lines.extend(merged_line)
                merged_types.extend(merged_type)
    
    # 整理输出
    cnt = 0
    new_cls_info = {}
    for key in white:
        if key in sel1:
            continue
        new_cls_info[cnt] = white[key]
        cnt += 1
    
    for key in yellow:
        if key in sel2:
            continue
        new_cls_info[cnt] = yellow[key]
        cnt += 1
    
    for key in others:
        new_cls_info[cnt] = others[key]
        cnt += 1

    for merged_line, merged_type in zip(merged_lines, merged_types):
        new_cls_info[cnt] = {
            'points': merged_line,
            'attribute': merged_type,
        }
        cnt += 1
    return new_cls_info