import json
from refile import smart_open, smart_path_join
import numpy as np
import copy
from tqdm import tqdm
from multiprocessing import Pool
import logging
from shapely.geometry import LineString, Point
from shapely.geometry import MultiLineString, Polygon, LineString, LinearRing, Point, box
from perceptron.utils.maptracker_utils.line_merge_utils import find_connected_components, build_graph

def check_straight(points, threshold=3):
    # 判断是否是直线
    # 逻辑是：连接首尾点构成一条线，看车道线上每个点到线的距离，存在某个点阈值大于 threshold，认为是弯曲的线，否则认为是直线
    line = LineString([points[0], points[-1]])
    is_straight = True
    for point in points:
        p = Point(point)
        distance = p.distance(line)
        if distance > threshold:
            # from IPython import embed; embed(); assert None
            # print(distance)
            is_straight = False
            break
    return is_straight

def check_joint(lines, threshold=0.2):
    # 判断是否有分叉 / 合并 / 分段属性的情况
    # 逻辑是：一堆线里，如果有首尾连接线的情况，认为是有 joint, 否则认为没有, 代码从 dataset 里扒出来的
    graph = build_graph(lines, thre=threshold) # 两点距离小于 thre 认为共点
    components = find_connected_components(graph)
    for comp in components:
        if len(comp) > 1:
            return True
    return False

def sine_angle_between_vectors(v1, v2):
    # 计算向量的叉积的模
    cross_product_magnitude = np.linalg.norm(np.cross(v1, v2))
    
    # 计算向量的模
    magnitude_v1 = np.linalg.norm(v1)
    magnitude_v2 = np.linalg.norm(v2)
    
    # 计算夹角的正弦值
    sin_theta = cross_product_magnitude / (magnitude_v1 * magnitude_v2)
    
    return sin_theta

def check_left_right_turn(lines, degree_thre=20, num_thre=2, cur_dire=[1, 0, 0]):
    # 判断当前帧是否正在进行路口左右转 / 变道
    # 逻辑是：连接每条线的首尾点，如果存在 num_thre 以上数目的线，满足 1.是直线  2. 和当前行进方向夹角大于 30 度

    cnt = 0

    for line in lines:
        v1 = [line[0][0] - line[-1][0], line[0][1] - line[-1][1], 0]
        v2 = cur_dire
        sine_score = sine_angle_between_vectors(v1, v2)
        if check_straight(line) and sine_score > np.sin(degree_thre*np.pi/180):
            cnt += 1
    
    if cnt > num_thre:
        return True
    else:
        return False

def split_lane_to_laneline_curb(bev_info):
    bev_split = {
        "laneline": copy.deepcopy(bev_info["lane"]), 
        "curb": copy.deepcopy(bev_info["lane"]),

    }
    all_lane_id = list(bev_info["lane"].keys())
    for _lane_id in all_lane_id:
        attr = bev_info["lane"][_lane_id]["attribute"]
        if attr["road_curb"] in ["NAN", 0]  and \
        attr["guardrail"] in ["NAN", 0] and \
        attr['water_barrier'] in ["NAN", 0]:
            del bev_split["curb"][_lane_id]
        
        if attr["lane_type"]["color"] == "NAN" or \
            attr["lane_type"]["dashed"] == "NAN" or \
            attr["lane_type"]["shape"] == "NAN":
            del bev_split["laneline"][_lane_id] 
        
    return bev_split

def filter_lanelines(lines, map_point_cloud_range=(0, -15, 100, 15)):
    # 过滤掉 100m 外的线
    new_lines = []
    x_min, y_min, x_max, y_max = map_point_cloud_range
    patch_bev = box(x_min, y_min, x_max, y_max)
    for ego_pts in lines:
        if len(ego_pts) < 2:
            continue
        line_geom_before = LineString(np.array(ego_pts))
        if not line_geom_before.is_valid:
            continue

        line_geom_after = line_geom_before.intersection(patch_bev)  
        if not line_geom_after.is_empty:
            if line_geom_after.geom_type == "LineString":
                line_geom_after = MultiLineString([line_geom_after])
            elif line_geom_after.geom_type == "Point":
                continue
            coords_after = []
            try:
                for new_line in line_geom_after.geoms:
                    coords_after.extend(new_line.coords)
                ego_pts = np.array(list(coords_after)).tolist()
            except:
                continue
            new_lines.append(ego_pts)
        else:
            continue

    return new_lines

def select_data_highway(src_json, tgt_json, select_json_dir, description):
    """汇总
    Args:
        src_json (str): 原始的包含 path list 的 json
        tgt_json (str): 处理的包含 path list 的 json
        select_json_dir (str): 用来存储每个 json 的位置
        description (str): 对数据集的描述
    """
    src_paths = json.load(smart_open(src_json, 'r'))['paths']
    tgt_jsons_info = {
        'author': 'dwj',
        'description': description,
    }

    tgt_paths = []
    for path in tqdm(src_paths):
        name = path.split('/')[-1]
        new_path = smart_path_join(select_json_dir, path.split('/')[-3], path.split('/')[-2],path.split('/')[-1])
        
        info = json.load(smart_open(path, 'r'))
        new_info = copy.deepcopy(info)
        new_frames = []
        curve_cnt = 0
        joint_cnt = 0
        for i, frame in enumerate(info['frames']):
            bev_info = frame['bev']
            bev_split = split_lane_to_laneline_curb(bev_info)
            # from IPython import embed; embed()
            lanelines = [info['points'] for info in bev_split["laneline"].values()]
            new_lines = filter_lanelines(lanelines)
            

            flag = False
            for line in new_lines:
                if not check_straight(line):
                    # print('curve')
                    flag = True
                    break
                    
            if flag:
                # from IPython import embed; embed(); assert None
                new_frames.append(frame)
                curve_cnt += 1
                continue

            if check_joint(new_lines):
                # print('joint')
                new_frames.append(frame)
                joint_cnt += 1
                continue

        if len(new_frames) == 0:
            continue
        print(len(new_frames), len(info['frames']), curve_cnt, joint_cnt)
        new_info["frames"] = new_frames
        json.dump(new_info, smart_open(new_path, 'w'))
        tgt_paths.append(new_info)
    tgt_jsons_info['paths'] = tgt_paths
    json.dump(tgt_jsons_info, smart_open(tgt_json, 'w'))

def select_data_highway_single(i, path, select_json_dir):
    """汇总
    Args:
        src_json (str): 原始的包含 path list 的 json
        tgt_json (str): 处理的包含 path list 的 json
        select_json_dir (str): 用来存储每个 json 的位置
        description (str): 对数据集的描述
    """
    new_path = smart_path_join(select_json_dir, path.split('/')[-3], path.split('/')[-2],path.split('/')[-1])
    info = json.load(smart_open(path, 'r'))
    new_info = copy.deepcopy(info)
    new_frames = []
    curve_cnt = 0
    joint_cnt = 0
    for i, frame in enumerate(info['frames']):
        bev_info = frame['bev']
        bev_split = split_lane_to_laneline_curb(bev_info)
        # from IPython import embed; embed()
        lanelines = [info['points'] for info in bev_split["laneline"].values()]
        new_lines = filter_lanelines(lanelines)

        flag = False
        for line in new_lines:
            if not check_straight(line):
                flag = True
                break
                
        if flag:
            # from IPython import embed; embed(); assert None
            new_frames.append(frame)
            curve_cnt += 1
            continue

        if check_joint(new_lines):
            # print('joint')
            new_frames.append(frame)
            joint_cnt += 1
            continue

    if len(new_frames) == 0:
        return
    
    new_info["frames"] = new_frames
    json.dump(new_info, smart_open(new_path, 'w'))

    print(f"{i} done =>100%:{new_path}, frame_num after: {len(new_frames)}, frame_num before: {len(info['frames'])}")
    return {"frame_num":len(new_frames), "json_path":new_path}


def select_data_city_single(i, path, select_json_dir):
    """汇总
    Args:
        src_json (str): 原始的包含 path list 的 json
        tgt_json (str): 处理的包含 path list 的 json
        select_json_dir (str): 用来存储每个 json 的位置
        description (str): 对数据集的描述
    """
    new_path = smart_path_join(select_json_dir, path.split('/')[-3], path.split('/')[-2],path.split('/')[-1])
    from refile import smart_exists
    if smart_exists(new_path):
        info = json.load(smart_open(new_path, 'r'))
        return {"frame_num":len(info["frames"]), "json_path":new_path}
    
    info = json.load(smart_open(path, 'r'))
    new_info = copy.deepcopy(info)
    new_frames = []
    curve_cnt = 0
    joint_cnt = 0
    for i, frame in enumerate(info['frames']):
        bev_info = frame['bev']
        bev_split = split_lane_to_laneline_curb(bev_info)
        # from IPython import embed; embed()
        lanelines = [info['points'] for info in bev_split["laneline"].values()]
        new_lines = filter_lanelines(lanelines)

        # 看当前帧有没有停止线或者人形横道，都没有，就跳过
        if len(bev_info['crosswalk']) == 0 and len(bev_info['stopline']) == 0:
            continue

        # 看是否正在左右转
        turn_flag = check_left_right_turn(new_lines, degree_thre=20, num_thre=2, cur_dire=[1, 0, 0])
        if turn_flag:
            new_frames.append(frame)


    if len(new_frames) > 0:
        new_info["frames"] = new_frames
        json.dump(new_info, smart_open(new_path, 'w'))
        print(f"{i} done =>100%:{new_path}, frame_num after: {len(new_frames)}, frame_num before: {len(info['frames'])}")
    
        return {"frame_num":len(new_frames), "json_path":new_path}
    else:
        print(f"{i} done =>100%:{new_path}, frame_num after: {len(new_frames)}, frame_num before: {len(info['frames'])}")
        return {"frame_num":0, "json_path":new_path}
    

def multi_process_select_data_city(src_json, tgt_json, select_json_dir):
    # multi task
    def error_callback(error):
        logging.error(f"{error}")
    

    def callback(result):
        if result:
            frame_num_all["all"] += result['frame_num']
            if result['frame_num'] > 0:
                frame_num_all["file_list"].append(result['json_path'])
    if isinstance(src_json, str):
        src_paths = json.load(smart_open(src_json, 'r'))['paths']
    else:
        src_paths = []
        for js in src_json:
            src_paths.extend(json.load(smart_open(js, 'r'))['paths'])

    frame_num_all = {"all": 0, "file_list":[]}
    pool = Pool(processes=50, maxtasksperchild=1)
    for i, json_path in tqdm(enumerate(src_paths)):
        # select_data_city_single(i, json_path, select_json_dir)
        pool.apply_async(select_data_city_single, 
                         args=(i, json_path, select_json_dir),
                         callback=callback,
                         error_callback=error_callback)
    pool.close()
    pool.join()
    with smart_open(tgt_json, 'w') as f:
        f.write(json.dumps({
            "information": f"city data selected left or right turn, frame num: {frame_num_all['all']}",
            "author": "dwj",
            "paths": frame_num_all['file_list'],
        }))
    print(f"frame_num_all: {frame_num_all['all']}; json_list: {tgt_json}")

def multi_process_select_data_highway(src_json, tgt_json, select_json_dir):
    # multi task
    def error_callback(error):
        logging.error(f"{error}")
    

    def callback(result):
        if result:
            frame_num_all["all"] += result['frame_num']
            frame_num_all["file_list"].append(result['json_path'])

    src_paths = json.load(smart_open(src_json, 'r'))['paths']

    frame_num_all = {"all": 0, "file_list":[]}
    pool = Pool(processes=50, maxtasksperchild=1)
    for i, json_path in tqdm(enumerate(src_paths)):
        pool.apply_async(select_data_highway_single, 
                         args=(i, json_path, select_json_dir),
                         callback=callback,
                         error_callback=error_callback)
    pool.close()
    pool.join()
    with smart_open(tgt_json, 'w') as f:
        f.write(json.dumps({
            "information": f"highway data selected curve or joint, frame num: {frame_num_all['all']}",
            "author": "dwj",
            "paths": frame_num_all['file_list'],
        }))
    print(f"frame_num_all: {frame_num_all['all']}; json_list: {tgt_json}")
    



if __name__ == '__main__':
    # 对高速数据，只取非直线和有 joint 的帧
    # src_json = "s3://mj-share/admap_e2e/highway_list/map_hw_ramp_rebuild_p0_4v_checked.json"
    # tgt_json = "s3://dwj-data/e2e_data_select1203_withjoint2/highway_list/sum_json/map_hw_ramp_rebuild_p0_4v_checked.json"
    # select_json_dir = "s3://dwj-data/e2e_data_select1203_withjoint2/highway_list/all_json"

    # description = 'Highway data that are curve or contains joint'
    # # select_data_highway(src_json, tgt_json, select_json_dir, description)
    # multi_process_select_data_highway(src_json, tgt_json, select_json_dir)


    src_json = ["s3://map-cjz/data_rebuild_mm_wfl_newcp/list/map_city_roadentrance_177407_177465_177519_177518_177517_wfl_1116.json",
                "s3://map-cjz/data_rebuild_mm_wfl_newcp/list/map_city_roadentrance_177685_177686_177687_wfl_1120.json"]
    
    tgt_json = "s3://dwj-data/e2e_data_select1203/city_leftrightturn/sum_json/map_city_roadentrance_177407_177465_177519_177518_177517_wfl_1116_and_177685_177686_177687_wfl_1120_new.json"
    select_json_dir = "s3://dwj-data/e2e_data_select1203/city_leftrightturn/all_json"
    
    multi_process_select_data_city(src_json, tgt_json, select_json_dir)




