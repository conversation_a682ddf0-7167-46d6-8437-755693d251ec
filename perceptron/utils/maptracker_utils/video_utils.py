'''
合并maptracker的实验可视化
'''

import os
import os.path as osp
import cv2
from tqdm import tqdm
import numpy as np
from pdb import set_trace as ste
from refile import smart_open, smart_exists, smart_path_join, smart_listdir
import argparse

def merge_video(pic_dir, mp4name, save_folder="s3://zqt/mtracker/expvis-videos/"):
    pic_list = [osp.join(pic_dir, tmp) for tmp in os.listdir(pic_dir)]

    # 重构pic_group
    pic_group_dict = {}
    for pic_path in tqdm(pic_list):
        tmp_group_id = int(pic_path.split("/")[-1].split("_token")[0])
        cam_id = int(pic_path.split("/")[-1].split("_")[-3])
        if tmp_group_id not in pic_group_dict:
            pic_group_dict[tmp_group_id] = {}
        mode = pic_path.split("_")[-1].split(".")[0]  # dt/gt
        view = pic_path.split("_")[-2]  # bev/rv
        if cam_id not in pic_group_dict[tmp_group_id]:
            pic_group_dict[tmp_group_id][cam_id] = {}  
        pic_group_dict[tmp_group_id][cam_id][mode+view] = pic_path
    roi_group_num = len(pic_group_dict)

    key_list = list(pic_group_dict.keys())
    max_key = np.array(key_list).max()

    h, w = 1080, 3394
    # video infos
    out_size = [h, w]
    fps = 10
    fourcc = cv2.VideoWriter_fourcc("m", "p", "4", "v")
    out_video = cv2.VideoWriter()
    out_video_path = mp4name + ".mp4"
    out_video.open(out_video_path, fourcc, fps, (int(out_size[1]), int(out_size[0])), True)

    for group_id in tqdm(range(0, max_key, 1)):
        if group_id not in pic_group_dict:
            continue
        group_val = pic_group_dict[group_id]

        # 检查完整性
        valid = True
        for cam_id in range(4):
            if cam_id not in group_val:
                valid = False
                continue
            if "gtrv" not in group_val[cam_id] or "dtrv" not in group_val[cam_id]:
                valid = False
        if "gtbev" not in group_val[0] or "dtbev" not in group_val[0]:
            valid = False
            continue
                
        if not valid:
            continue

        dt_rv_img_list = []
        gt_rv_img_list = []
        for cam_id in range(2,3):
            tmp_dt_img = cv2.imread(group_val[cam_id]["dtrv"])
            tmp_gt_img = cv2.imread(group_val[cam_id]["gtrv"])
            dt_rv_img_list.append(tmp_dt_img)
            gt_rv_img_list.append(tmp_gt_img)
        
        for cam_id in range(0, 1):
            tmp_dt_img = cv2.imread(group_val[cam_id]["dtrv"])
            tmp_gt_img = cv2.imread(group_val[cam_id]["gtrv"])
            dt_rv_img_list.append(tmp_dt_img)
            gt_rv_img_list.append(tmp_gt_img)
        
        for cam_id in range(3, 4):
            tmp_dt_img = cv2.imread(group_val[cam_id]["dtrv"])
            tmp_gt_img = cv2.imread(group_val[cam_id]["gtrv"])
            dt_rv_img_list.append(tmp_dt_img)
            gt_rv_img_list.append(tmp_gt_img)

        dt_bev_img = cv2.imread(group_val[0]["dtbev"])  # (400, 210)
        gt_bev_img = cv2.imread(group_val[0]["gtbev"])

        dt_rv_cat = np.concatenate(dt_rv_img_list, axis=1)
        gt_rv_cat = np.concatenate(gt_rv_img_list, axis=1)

        dt_cat = np.concatenate((dt_rv_cat, cv2.resize(dt_bev_img, (1029, 1080))), axis=1)
        gt_cat = np.concatenate((gt_rv_cat, cv2.resize(gt_bev_img, (1029, 1080))), axis=1)

        all_cat = np.concatenate((dt_cat, gt_cat), axis=0)

        out_frame = all_cat
        out_frame = cv2.resize(out_frame, (3394, 1080))
        # write to video
        out_video.write(np.uint8(out_frame))
    out_video.release()

    trans_cmd = r"ffmpeg -i {} -pix_fmt yuv420p -vcodec libx264 -r 10 {} -hide_banner -y".format(
        out_video_path, out_video_path.replace(".mp4", "_x264.mp4")
    )
    os.system(trans_cmd)
    local_path = (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4")
    s3_path = smart_path_join(save_folder, (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4"))

    cmd = "aws --endpoint-url=http://oss.i.machdrive.cn s3 cp {} {}".format(local_path, s3_path)
    os.system(cmd)

    rm_cmd = f"rm {local_path}"
    os.system(rm_cmd)



    
if __name__ == "__main__":
    '''
    '''
    parser = argparse.ArgumentParser()
    parser.add_argument("--pic_dir", type=str, default=None)
    parser.add_argument("--save_s3_path", type=str, default="s3://zqt/mtracker/expvis-videos/")
    args = parser.parse_args()
    pic_dir = args.pic_dir
    mp4name = pic_dir.split("/")[-3] + "-" + pic_dir.split("/")[-2] + "-" + pic_dir.split("/")[-1]
    mp4name = mp4name.replace(":", "-")
    merge_video(pic_dir, mp4name, args.save_s3_path)
