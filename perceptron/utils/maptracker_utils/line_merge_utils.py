import numpy as np
import copy
from pdb import set_trace as ste

def is_connect(pt1, pt2, thre):
    dist = np.linalg.norm(np.array(pt1) - np.array(pt2))
    if dist < thre:
        return True
    return False

def build_graph(lines, thre=0.05):
    graph = {}
    for i, segment in enumerate(lines):
        graph[i] = []
        # 检查当前线段的起点或终点是否与其他线段的终点或起点相连
        for j, other_segment in enumerate(lines):
            if i != j and (is_connect(segment[0], other_segment[0], thre)  or 
                           is_connect(segment[-1], other_segment[0], thre) or 
                           is_connect(segment[-1], other_segment[-1], thre) or 
                           is_connect(segment[0], other_segment[-1], thre)):
                graph[i].append(j)
    return graph

def cos_between_vectors(v1, v2):
    # 计算两个向量之间的夹角cos, cos 越小越平滑
    dot_product = np.dot(v1, v2)
    magnitude_product = np.linalg.norm(v1) * np.linalg.norm(v2)
    return dot_product / (magnitude_product + 1e-5)

def angle_between_vectors(v1, v2):
    cos_theta = cos_between_vectors(v1, v2)
    angle_radians = np.arccos(cos_theta)
    angle_degrees = np.degrees(angle_radians)
    return angle_degrees

def build_ordered_graph(lines, thre=0.05):
    '''
    只考虑首尾相连
    '''
    graph = {}
    for i, segment in enumerate(lines):
        graph[i] = []
        # 检查当前线段的终点是否与其他线段的起点相连
        for j, other_segment in enumerate(lines):
            if i != j and (is_connect(segment[-1], other_segment[0], thre)):
                x1, y1 = segment[-2][0], segment[-2][1]
                x2, y2 = segment[-1][0], segment[-1][1]
                x3, y3 = other_segment[0][0], other_segment[0][1]
                x4, y4 = other_segment[1][0], other_segment[1][1]
                # # 若夹角在60度以上, 拒绝联结.
                # v1 = np.array([x2-x1, y2-y1])
                # v2 = np.array([x4-x3, y4-y3])
                # vec_angle = angle_between_vectors(v1, v2)
                # if vec_angle < 60:
                #     graph[i].append(j)
                graph[i].append(j)  # update 05.25, 有向路沿也直接连接, 由切向向量完成直角切割
    return graph

def build_ordered_graph_laneline(lines, thre=0.05):
    '''
    只考虑首尾相连
    '''
    graph = {}
    for i, segment in enumerate(lines):
        graph[i] = []
        # 检查当前线段的终点是否与其他线段的起点相连
        for j, other_segment in enumerate(lines):
            if i != j and (is_connect(segment[-1], other_segment[0], thre)):
                x1, y1 = segment[-2][0], segment[-2][1]
                x2, y2 = segment[-1][0], segment[-1][1]
                x3, y3 = other_segment[0][0], other_segment[0][1]
                x4, y4 = other_segment[1][0], other_segment[1][1]
                graph[i].append(j)  # fix 05.02, connect bug fix..
    return graph

def dfs(graph, node, visited, component):
    visited.add(node)
    component.append(node)
    for neighbor in graph[node]:
        if neighbor not in visited:
            dfs(graph, neighbor, visited, component)

def find_connected_components(graph):
    visited = set()
    components = []
    for node in list(graph.keys()):
        if node not in visited:
            component = []
            dfs(graph, node, visited, component)
            components.append(component)
    return components

def cos_between_vectors(v1, v2):
    # 计算两个向量之间的夹角cos, cos 越小越平滑
    dot_product = np.dot(v1, v2)
    magnitude_product = np.linalg.norm(v1) * np.linalg.norm(v2)
    return dot_product / (magnitude_product+1e-5)

def find_best_merge_Opportunity(merge_info, thre):
    merge_info = copy.deepcopy(merge_info)
    # 找到最佳的合并机会（夹角最小）
    best_cos = 0   # 对应 90 度, cos 小于 0 才能 merge
    best_pair = None
    for idx, segment_info in merge_info.items():
        # 检查当前线段的起点或终点是否与其他线段的终点或起点相连
        for other_idx, other_segment_info in merge_info.items():
            if idx == other_idx:
                continue
                  
            segment = segment_info['points']
            other_segment = other_segment_info['points']

            # update 06.01, 使用5个点的差分作为判断
            segment_idx = min(5, len(segment)-1)
            other_segment_idx = min(5, len(other_segment)-1)

            # if is_connect(segment[0], other_segment[0], thre):
            #     v1 = np.array(segment[0]) - np.array(segment[1])
            #     v2 = np.array(other_segment[0]) - np.array(other_segment[1])
                
            # elif is_connect(segment[-1], other_segment[0], thre):
            #     v1 = np.array(segment[-1]) - np.array(segment[-2])
            #     v2 = np.array(other_segment[0]) - np.array(other_segment[1])
                
            # elif is_connect(segment[-1], other_segment[-1], thre):
            #     v1 = np.array(segment[-1]) - np.array(segment[-2])
            #     v2 = np.array(other_segment[-1]) - np.array(other_segment[-2])
                
            # elif is_connect(segment[0], other_segment[-1], thre):
            #     v1 = np.array(segment[0]) - np.array(segment[1])
            #     v2 = np.array(other_segment[-1]) - np.array(other_segment[-2])
            # else:
            #     continue  # 不相连
            
            # update 05.25, 必须首尾相连
            if is_connect(segment[-1], other_segment[0], thre):
                v1 = np.array(segment[-1]) - np.array(segment[-segment_idx])
                v2 = np.array(other_segment[0]) - np.array(other_segment[other_segment_idx])
            elif is_connect(segment[0], other_segment[-1], thre):
                v1 = np.array(segment[0]) - np.array(segment[segment_idx])
                v2 = np.array(other_segment[-1]) - np.array(other_segment[-other_segment_idx])
            else:
                continue  # 不相连

            cos = cos_between_vectors(v1, v2)    
            # print("idx other_idx: {} {}  cos: {}".format(idx, other_idx, cos))    
            if cos < best_cos:
                best_cos = cos
                best_pair = (idx, other_idx)   
    # print("best pair: ", best_pair)
    return best_pair

def unordered_find_best_merge_Opportunity(merge_info, thre):
    merge_info = copy.deepcopy(merge_info)
    # 找到最佳的合并机会（夹角最小）
    best_cos = 0   # 对应 90 度, cos 小于 0 才能 merge
    best_pair = None
    for idx, segment_info in merge_info.items():
        # 检查当前线段的起点或终点是否与其他线段的终点或起点相连
        for other_idx, other_segment_info in merge_info.items():
            if idx == other_idx:
                continue
                  
            segment = segment_info['points']
            other_segment = other_segment_info['points']

            # update 06.01, 使用5个点的差分作为判断
            segment_idx = min(5, len(segment)-1)
            other_segment_idx = min(5, len(other_segment)-1)

            if is_connect(segment[0], other_segment[0], thre):
                v1 = np.array(segment[0]) - np.array(segment[segment_idx])
                v2 = np.array(other_segment[0]) - np.array(other_segment[other_segment_idx])
                
            elif is_connect(segment[-1], other_segment[0], thre):
                v1 = np.array(segment[-1]) - np.array(segment[-segment_idx])
                v2 = np.array(other_segment[0]) - np.array(other_segment[other_segment_idx])
                
            elif is_connect(segment[-1], other_segment[-1], thre):
                v1 = np.array(segment[-1]) - np.array(segment[-segment_idx])
                v2 = np.array(other_segment[-1]) - np.array(other_segment[-other_segment_idx])
                
            elif is_connect(segment[0], other_segment[-1], thre):
                v1 = np.array(segment[0]) - np.array(segment[segment_idx])
                v2 = np.array(other_segment[-1]) - np.array(other_segment[-other_segment_idx])
            else:
                continue  # 不相连
    
            cos = cos_between_vectors(v1, v2)   
            if cos < best_cos:
                best_cos = cos
                best_pair = (idx, other_idx)   
    return best_pair

def merge_segments(line_info1, line_info2, thre):
    line1, attr1 = line_info1['points'], line_info1['attribute']
    line2, attr2 = line_info2['points'], line_info2['attribute']
    
    # 把 line attr 赋值给点
    if isinstance(attr1, dict):
        attr1 = [attr1] * len(line1)
    if isinstance(attr2, dict):
        attr2 = [attr2] * len(line2)
    
    if is_connect(line1[0], line2[0], thre):
        # 只有高精可能出现这种共起点/终点的合并
        line1 = list(line1)[::-1]
        attr1 = attr1[::-1]
    elif is_connect(line1[-1], line2[0], thre):
        # 啥都不用改
        pass
    elif is_connect(line1[-1], line2[-1], thre):
        # 只有高精可能出现这种共起点/终点的合并
        line2 = list(line2)[::-1]
        attr2 = attr2[::-1]
    elif is_connect(line1[0], line2[-1], thre):
        # line2[0] -> line2[-1] -> line1[0] -> line1[-1]
        # line1 = list(line1)[::-1]
        # attr1 = attr1[::-1]
        # line2 = list(line2)[::-1]
        # attr2 = attr2[::-1]
        line1, line2 = line2, line1  # 交换顺序, 保持合并前后方向一致
        attr1, attr2 = attr2, attr1  # 交换顺序, 保持合并前后方向一致
    else:
        raise NotImplementedError  # 这种情况不应该出现，出现了说明前面的代码有 bug
    
    new_line = list(line1)[:-1] + list(line2)
    new_attr = attr1[:-1] + attr2  # 如果前期没有对 line 做方向限制, 连接点的属性是任选一条来给的
    
    merged_info = {
        'points': new_line, 
        'attribute': new_attr,
    }

    return merged_info

def merge_line_attr(to_merge_info, thre):
    merge_info = copy.deepcopy(to_merge_info)
    while True:
        best_pair = find_best_merge_Opportunity(merge_info, thre)
        if best_pair is None:  # 已经没有能合并的了
            break
        i, j = best_pair
        new_segment = merge_segments(merge_info[i], merge_info[j], thre)
        merge_info[i] = new_segment
        del merge_info[j]
    return merge_info

def unordered_merge_line_attr(to_merge_info, thre):
    merge_info = copy.deepcopy(to_merge_info)
    while True:
        best_pair = unordered_find_best_merge_Opportunity(merge_info, thre)
        if best_pair is None:  # 已经没有能合并的了
            break
        i, j = best_pair
        new_segment = merge_segments(merge_info[i], merge_info[j], thre)
        merge_info[i] = new_segment
        del merge_info[j]
    return merge_info

def get_sharp_turn_indices(points, threshold_deg=90):
    """
    使用相邻点方向向量的 arctan2 来判断累计角度变化是否大于阈值
    
    参数:
        points (np.ndarray): 形状为 (N, 2) 的二维点集
        threshold_deg (float): 累计角度变化阈值，默认 90 度
        
    返回:
        List[int]: 需要打断的位置下标
    """
    n = len(points)
    if n < 2:
        return []

    sharp_turn_indices = []
    prev_angle = None
    total_angle = 0.0

    for i in range(n - 1):
        # 获取当前段的方向向量
        dx = points[i + 1][0] - points[i][0]
        dy = points[i + 1][1] - points[i][1]
        # 如果向量是零向量，跳过
        if dx == 0 and dy == 0:
            continue

        # 当前方向角度（弧度制）
        curr_angle = np.arctan2(dy, dx)

        if prev_angle is not None:
            # 计算与上一段的角度差（考虑角度环绕）
            angle_diff = np.degrees(np.abs(curr_angle - prev_angle))
            # 角度差取最小方向（比如 350° 变化等价于 10°）
            angle_diff = min(angle_diff, 360 - angle_diff)

            # 累加角度
            total_angle += angle_diff
            # print(total_angle)
            # 判断是否超过阈值
            if total_angle > threshold_deg:
                sharp_turn_indices.append(i + 1)  # 打断位置在下一个点
                total_angle = 0.0  # 重置累计角度

        # 更新前一个方向角度
        prev_angle = curr_angle

    return sharp_turn_indices


import numpy as np
from scipy.signal import savgol_filter

def preprocess_points_with_savgol(points, window_length=5, polyorder=2):
    """
    使用 Savitzky-Golay 滤波器对三维点序列进行平滑处理
    
    参数:
        points (np.ndarray): 形状为 (N, 3) 的原始三维点集
        window_length (int): 滑动窗口长度（必须是奇数）
        polyorder (int): 多项式拟合阶数
        
    返回:
        np.ndarray: 平滑后的三维点集，形状为 (N, 3)
    """
    if len(points) < window_length:
        # 如果点数不足窗口长度，则直接返回原数据（无法应用滤波）
        return points

    # 分别提取 x, y, z 坐标
    x = points[:, 0]
    y = points[:, 1]
    z = points[:, 2]

    # 对每个维度单独进行滤波
    smoothed_x = savgol_filter(x, window_length=window_length, polyorder=polyorder)
    smoothed_y = savgol_filter(y, window_length=window_length, polyorder=polyorder)
    # smoothed_z = savgol_filter(z, window_length=window_length, polyorder=polyorder)
    smoothed_z = z

    # 合并成新的平滑点集
    smoothed_points = np.vstack((smoothed_x, smoothed_y, smoothed_z)).T
    return smoothed_points

def cut_new_info(new_info, class_name):
    '''
    打断U型
    '''
    copy_new_info = dict()
    for info_key, info_item in new_info.items():
        if isinstance(info_item["attribute"], dict):
            copy_new_info[info_key] = info_item
            continue
        elif isinstance(info_item["attribute"], list):
            # 检查切向量
            points = np.array(info_item["points"])
            attributes = info_item["attribute"]

            # 提前平滑
            smoothed_points = preprocess_points_with_savgol(points, window_length=5, polyorder=2)
            points = smoothed_points
            
            # print(points, "over")
            sharp_turn_indices = get_sharp_turn_indices(points, threshold_deg=90)
            # if class_name == "curb":
            #     ste()
            if len(sharp_turn_indices) == 0:
                # 没有需要打断的地方，直接保留
                # print(f"{class_name} has no sharp turns")
                info_item["points"] = points.tolist()
                copy_new_info[info_key] = info_item
                continue
            
            # update 06.02, 只选择最靠近中间的进行打断
            center_idx = len(points) // 2
            # 找出离中心最近的拐点
            closest_idx = min(sharp_turn_indices, key=lambda x: abs(x - center_idx))
            sharp_turn_indices = [closest_idx]
            # 如果打断后sharp_turn_indices小于5(意味着小于1m),禁止打断..
            if closest_idx < 5:
                info_item["points"] = points.tolist()
                copy_new_info[info_key] = info_item
                continue

            # print(f"class {class_name} Line {info_key} has sharp turns at indices: {sharp_turn_indices}")

            segments = []
            attrs = []
            start_idx = 0

            for idx in sharp_turn_indices:
                if idx - start_idx <= 1:
                    continue  # 至少两个点才能形成一段
                segments.append(points[start_idx:idx])
                attrs.append(attributes[start_idx:idx])
                start_idx = idx
            # 添加最后一段
            if start_idx < len(points):
                segments.append(points[start_idx:])
                attrs.append(attributes[start_idx:])

            # 构建新的 info 字段
            for i, (seg_points, seg_attrs) in enumerate(zip(segments, attrs)):
                new_key = f"{info_key}_part{i + 1}"
                copy_new_info[new_key] = {
                    "points": seg_points.tolist(),
                    "attribute": seg_attrs
                }
            # print(copy_new_info.keys())
        else:
            raise NotImplementedError
        # print("finally", copy_new_info.keys())
    return copy_new_info