
import sys
sys.path.append('.')
import argparse
import torch
import torch.nn as nn
import onnx
import copy
from onnxsim import simplify

import modelopt.torch.opt as mto
import modelopt.torch.quantization as mtq
from mmdet.models.backbones.resnet import Custom_Add
from mmcv.cnn.bricks import wrappers
from modelopt.torch.opt.conversion import apply_mode
from perceptron.utils.map_deploy_utils.map_calib_model import (
    QConvTranspose2d, QuantCustomAdd, QuantMultiheadAttention, 
    get_int8_quant_config, get_fp8_quant_config,
)
# from perceptron.layers.blocks_2d.mmdet2d.fasternet import Custom_Split

from perceptron.utils.map_deploy_utils.deploy_utils import MapDeployConfig
from perceptron.models.end2end.perceptron.perceptron_maptrv2_onnx import MapDeployModel, configure_vision_encoder
from loguru import logger
# from perceptron.layers.blocks_2d.mmdet2d.repvgg import repvgg_model_convert


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--onnx-path", type=str, default=None, help="path to save onnx model")
    parser.add_argument("--quant-model", type=str, default=None, help="load calib model pth and export quant model")
    args = parser.parse_args()
    if args.onnx_path is None:
        onnx_out_path = MapDeployConfig.default_onnx_path
    else:
        onnx_out_path = args.onnx_path

    logger.info(f'MapDeployConfig: {vars(MapDeployConfig)}')
    torch_model = configure_vision_encoder()
    if args.quant_model is not None:
        mtq.register(original_cls=Custom_Add, quantized_cls=QuantCustomAdd)
        mtq.register(original_cls=wrappers.ConvTranspose2d, quantized_cls=QConvTranspose2d)
        # mtq.register(original_cls=Custom_Split, quantized_cls=QuantCustomSplit)
        # mtq.register(original_cls=nn.MultiheadAttention, quantized_cls=QuantMultiheadAttention)
        config = get_int8_quant_config()
        apply_mode(torch_model, mode=[("quantize", config)])
        logger.info(f"retore calibed model: {args.quant_model}")
        mto.restore(torch_model.camera_encoder, args.quant_model)

    deploy_model = MapDeployModel(torch_model)
    deploy_model.eval()

    input_names = ['imgs', 'lidar2imgs', 'ida_mats']
    output_names = ['line_scores',
                    'line_labels',
                    'lines',
                    'attrs_lines',]
    inputs_max_shape = {
        'imgs': (1, 1, 4, 3, 512, 960),
        'lidar2imgs': (1, 1, 4, 4, 4),
        'ida_mats': (1, 1, 4, 4, 4),
    }
    imgs = torch.zeros(inputs_max_shape['imgs']).to(torch.float32)
    lidar2imgs = torch.load('params/lidar2imgs_z02.pt').to(imgs.device)
    ida_mats = torch.load('params/ida_mats_z02.pt').to(imgs.device)
    with torch.no_grad():
        deploy_model.cpu()
        if args.quant_model is not None:
            deploy_model.map_head.half()
        torch.onnx.export(deploy_model, 
                            args=(imgs, lidar2imgs, ida_mats), 
                            f=onnx_out_path, 
                            input_names=input_names, 
                            output_names=output_names,
                            opset_version=17,
                            do_constant_folding=True,
                            keep_initializers_as_inputs=True,
                            verbose=True,)
    onnx_model = onnx.load(onnx_out_path)
    sim_model, _ = simplify(onnx_model)
    # 保存优化后的模型
    onnx.save(sim_model, onnx_out_path)