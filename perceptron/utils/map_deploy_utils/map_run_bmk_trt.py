import sys
sys.path.append('.')
import argparse
import torch
import numpy as np
import bisect
from loguru import logger

from perceptron.engine.cli import Det3DCli
from perceptron.utils import DictAction
from perceptron.utils.map_deploy_utils.deploy_utils import MapDeployConfig, get_current_exp_class, cosine_similarity
from perceptron.utils.map_deploy_utils.trt_model_infer.model_inference import TensorRTInference

from remote_test.client import TensorRTClient

logger.remove()
logger.add(
    sink=sys.stdout,
    level="INFO",
    filter=lambda record: record["level"].name == "INFO"
)

BaseExp = get_current_exp_class()

class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch, **kwargs)
        logger.info(f"MapDeployConfig: {vars(MapDeployConfig)}")

        self.maplane_outputs = MapDeployConfig.maplane_outputs

        if "thor" in kwargs and kwargs["thor"] is not None:
            self.remote_model_path = kwargs["thor"]
            ip = '***********'
            self.trt_model = TensorRTClient(f"{ip}:50051")
            # Load model
            if not self.trt_model.load_model(self.remote_model_path, "sample_model"):
                print("Load model failed")
                exit(1)
        else:
            self.engine_path = kwargs["engine_path"] if "engine_path" in kwargs else MapDeployConfig.default_engine_path
            self.trt_model = TensorRTInference(self.engine_path)
            self.trt_model.initialize()

    @classmethod
    def add_argparse_args(cls, parser):  # pragma: no-cover
        parser.add_argument(
            "--exp_options",
            nargs="+",
            action=DictAction,
            help="override some settings in the exp, the key-value pair in xxx=yyy format will be merged into exp. "
            'If the value to be overwritten is a list, it should be like key="[a,b]" or key=a,b '
            'It also allows nested list/tuple values, e.g. key="[(a,b),(c,d)]" '
            "Note that the quotation marks are necessary and that no white space is allowed.",
        )
        parser.add_argument("-b", "--batch-size-per-device", type=int, default=None)
        parser.add_argument("-e", "--max-epoch", type=int, default=None)
        parser.add_argument("--engine-path", type=str, default=None, help="trt model path to load")
        parser.add_argument("--thor", type=str, default=None, help="run bmk in thor and trt model path to load")
        return parser

    def format_img_meta(self, img_metas, device):
        img_metas_single_frame = {}
        for key, item in img_metas[0].items():
            if (
                key == "lidar2imgs"
                or key == "ida_mats"
                or key == "lidar2ego"
                or key == "ego2global"
                or key == "scene_name"
            ):
                img_metas_single_frame[key] = (
                    torch.tensor(np.stack([img_metas[batch_idx][key] for batch_idx in range(len(img_metas))]))
                    .unsqueeze(1)
                    .to(device)
                )
            elif key == "bda_mat":
                img_metas_single_frame[key] = torch.tensor(
                    np.stack([img_metas[batch_idx][key] for batch_idx in range(len(img_metas))])
                ).to(device)
            elif key == "box_type_3d":
                img_metas_single_frame[key] = [img_metas[batch_idx][key] for batch_idx in range(len(img_metas))]
            else:
                pass
        return img_metas_single_frame

    def format_infos(self, img_shape, frame_idx, img_metas, device):
        batch_size, num_frame, N, C, H, W = img_shape
        img_metas_single_frame = list()
        for batch_idx in range(batch_size):
            img_metas_single_sample = {
                key: img_metas[batch_idx][key][frame_idx]
                for key in img_metas[0].keys()
                if key not in ("imgs_nori_id", "map_tgt")
            }
            img_metas_single_frame.append(img_metas_single_sample)
        labels_single_frame = None
        input_info = {
            "box_type_3d": None,
            "ff_gt_bboxes_list": None,
            "ff_gt_labels_list": None,
            "pad_shape": torch.tensor([H, W], dtype=torch.int32).to(device),
        }
        img_metas_single_frame = self.format_img_meta(img_metas_single_frame, device)
        img_metas_single_frame.update(input_info)  # for prepare_for_dn function

        return img_metas_single_frame, labels_single_frame


    @torch.no_grad()
    def test_step(self, batch):
        batch_size = 1
        pred_dicts_det = None
        pred_maps = None
        imgs = batch['imgs']
        assert imgs.shape[0] == batch_size
        img_metas, _ = self.format_infos(
            imgs.shape,
            0,
            batch['img_metas'],
            imgs.device,
        )
        decode_inputs_np = {
            'imgs': imgs.contiguous().detach().cpu().numpy(),
            'lidar2imgs': img_metas['lidar2imgs'].contiguous().detach().cpu().numpy(),
            'ida_mats': img_metas['ida_mats'].contiguous().detach().cpu().numpy(),
        }
        trt_outputs = self.trt_model.inference(decode_inputs_np)
        line_scores = trt_outputs["line_scores"].reshape(self.maplane_outputs["line_scores"]["shape"])
        line_labels = trt_outputs["line_labels"].reshape(self.maplane_outputs["line_labels"]["shape"])
        lines = trt_outputs["lines"].reshape(self.maplane_outputs["lines"]["shape"])
        attrs_lines = trt_outputs["attrs_lines"].reshape(self.maplane_outputs["attrs_lines"]["shape"])

        # _, torch_outputs = self.model(**batch)
        # torch_line_scores = torch_outputs[0]["line_scores"].reshape(self.maplane_outputs["line_scores"]["shape"])
        # torch_line_labels = torch_outputs[0]["line_labels"].reshape(self.maplane_outputs["line_labels"]["shape"])
        # torch_lines = torch_outputs[0]["lines"].reshape(self.maplane_outputs["lines"]["shape"])
        # torch_attrs_lines = torch_outputs[0]["attrs_lines"].reshape(self.maplane_outputs["attrs_lines"]["shape"])
        # lines_cossim = cosine_similarity(lines, torch_lines)
        # line_scores_cossim = cosine_similarity(line_scores, torch_line_scores)
        # attrs_lines_cossim = cosine_similarity(attrs_lines, torch_attrs_lines)
        # print(f'lines_cossim:{lines_cossim}, line_scores_cossim:{line_scores_cossim}, attrs_lines_cossim:{attrs_lines_cossim}')

        # # save io datas
        # import ipdb; ipdb.set_trace()
        # import os
        # io_save_path = 'io_tensors'
        # if not os.path.exists(io_save_path):
        #     os.makedirs(io_save_path)
        # imgs.contiguous().detach().cpu().numpy().tofile(f'{io_save_path}/imgs.bin')
        # img_metas['lidar2imgs'].contiguous().detach().cpu().numpy().tofile(f'{io_save_path}/lidar2imgs.bin')
        # img_metas['ida_mats'].contiguous().detach().cpu().numpy().tofile(f'{io_save_path}/ida_mats.bin')
        # lines.tofile(f'{io_save_path}/lines.bin')
        # line_scores.tofile(f'{io_save_path}/line_scores.bin')
        # line_labels.tofile(f'{io_save_path}/line_labels.bin')
        # attrs_lines.tofile(f'{io_save_path}/attrs_lines.bin')

        bs = 1
        tokens = ['token_init' for _ in range(bs)]
        bs_i = 0
        single_result = {
                'line_scores': line_scores[bs_i],
                'line_labels': line_labels[bs_i],
                'lines': lines[bs_i],
                'token': tokens[bs_i],
                'attrs_lines': attrs_lines[bs_i],
            }

        pred_maps = [single_result]

        remap_pred_dicts = self.process_det_outs(pred_dicts_det)

        # fake items
        pred_maps[0]["stop_scores"] = np.zeros((50,))
        pred_maps[0]["stop_labels"] = (np.ones((50,)) * 2).astype(np.int64)
        pred_maps[0]["box_scores"] = np.zeros((100,))
        pred_maps[0]["box_labels"] = (np.ones((100,)) * 5).astype(np.int64)
        pred_maps[0]["entrance_scores"] = np.zeros((50,))
        pred_maps[0]["entrance_labels"] = (np.ones((50,)) * 6).astype(np.int64)
        pred_maps[0]["stops"] = np.zeros((50, 4, 3))
        pred_maps[0]["boxes"] = np.zeros((100, 4, 3))
        pred_maps[0]["entrances"] = np.zeros((50, 4, 3)).astype(np.int32)
        # pred_maps[0]["attrs_lines"] = np.zeros((50, 20, 6)).astype(np.int32)  # hardcode: 50
        pred_maps[0]["attrs_boxes"] = np.zeros((100, 4, 6)).astype(np.int32)

        index = batch["index_in_dataset"][0][0]
        if "s3_path" in self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_120"]:
            cam120_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_120"]["s3_path"].split("/")[-1].split(".jpg")[0]
            cam30_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_30"]["s3_path"].split("/")[-1].split(".jpg")[0]
            camleft100_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_left_100"]["s3_path"].split("/")[-1].split(".jpg")[0]
            camright100_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_right_100"]["s3_path"].split("/")[-1].split(".jpg")[0]
        else:
            cam120_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_120"]["timestamp"]
            cam30_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_30"]["timestamp"]
            camleft100_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_left_100"]["timestamp"]
            camright100_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_right_100"]["timestamp"]
        json_collections = self.val_dataloader.dataset.loader_output["json_collection"]
        json_idx = bisect.bisect_right(self.val_dataloader.dataset.loader_output["frame_data_list"].cumulative_sizes, index)
        json_path = self.val_dataloader.dataset.loader_output["json_collection"][json_idx]

        line_scores = pred_maps[0]["line_scores"]
        lines = pred_maps[0]["lines"]
        line_roi_index = np.where(line_scores > 0.3)
        line_roi_scores = line_scores[line_roi_index]  # (N, )
        lines_roi = lines[line_roi_index]  # (N, 20, 3)

        json_res = dict(
            cam120_ts=cam120_ts,
            cam30_ts=cam30_ts,
            camleft100_ts=camleft100_ts,
            camright100_ts=camright100_ts,
            line_roi_index=line_roi_index[0].tolist(),
            line_roi_scores=line_roi_scores.tolist(),
            line_roi=lines_roi.tolist(),
            json_path=json_path
        )

        # save_path = json_path.replace("s3://zqt/", "s3://zqt/debug/jira13541/").replace(".json", "_{}.json".format(cam120_ts))
        # json.dump(json_res, smart_open(save_path, "w"))

        pred_maps[0]["json_res"] = json_res


        return dict(pred_dicts=remap_pred_dicts, pred_maps=pred_maps, loss_dict=None)  # remap

if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    cli = Det3DCli(Exp)
    if cli.args.ckpt is None:
        cli.args.ckpt = MapDeployConfig.ckpt_pth
    cli.run()