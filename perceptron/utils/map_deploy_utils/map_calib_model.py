import sys
sys.path.append('.')
import torch
import torch.nn as nn
import copy

from perceptron.engine.cli import Det3DCli
from perceptron.utils import DictAction
from perceptron.utils.map_deploy_utils.deploy_utils import get_current_exp_class, MapDeployConfig
from perceptron.models.end2end.perceptron.perceptron_maptrv2_onnx import configure_vision_encoder
# from perceptron.layers.blocks_2d.mmdet2d.fasternet import Custom_Split, FasterNet_Residual_Add

from mmdet.models.backbones.resnet import Custom_Add
from mmcv.cnn.bricks import wrappers
from torch.nn.modules.conv import _size_2_t
import modelopt.torch.quantization as mtq
from modelopt.torch.quantization.nn import TensorQuantizer
from modelopt.torch.quantization.calib import HistogramCalibrator
from modelopt.torch.quantization.model_calib import enable_stats_collection
from modelopt.torch.opt.conversion import apply_mode
import modelopt.torch.opt as mto
from typing import Optional
from tqdm import tqdm
from loguru import logger
from typing import Optional, Tuple
from torch import Tensor
import torch.nn.functional as F
from einops import rearrange

class QuantCustomAdd(nn.Module):
    def __init__(self):
        super().__init__()
        self._setup()
    
    def _setup(self):
        # Method to setup the quantizers
        self.input_quantizer = TensorQuantizer()

    def forward(self, x,y):
        return x + self.input_quantizer(y)

class QuantCustomSplit(nn.Module):
    def __init__(self):
        super().__init__()
        self._setup()
    
    def _setup(self):
        # Method to setup the quantizers
        self.input_quantizer = TensorQuantizer()

    def forward(self, x, split_size_or_sections, dim=0):
        return torch.split(self.input_quantizer(x), split_size_or_sections, dim=dim)

class QConvTranspose2d(nn.ConvTranspose2d):
    def __init__( 
            self,
            in_channels: int,
            out_channels: int,
            kernel_size: _size_2_t,
            stride: _size_2_t = 1,
            padding: _size_2_t = 0,
            output_padding: _size_2_t = 0,
            groups: int = 1,
            bias: bool = True,
            dilation: _size_2_t = 1,
            padding_mode: str = 'zeros',
            device=None,
            dtype=None):
            super(QConvTranspose2d,self).__init__( 
                in_channels,
                out_channels,
                kernel_size,
                stride,
                padding,
                output_padding,
                groups,
                bias,
                dilation,
                padding_mode,
                device,
                dtype)
            self._setup()

    def _setup(self):
        # Method to setup the quantizers
        self.input_quantizer = TensorQuantizer()
        self.weight_quantizer = TensorQuantizer()

    def forward(self, input: torch.Tensor) -> torch.Tensor:
        return torch.nn.functional.conv_transpose2d(self.input_quantizer(input), self.weight_quantizer(self.weight), 
                                                    bias=self.bias, stride=self.stride, padding=self.padding, 
                                                    output_padding=self.output_padding, groups=self.groups, 
                                                    dilation=self.dilation)


class QuantMultiheadAttention(nn.MultiheadAttention):
    def __init__(
        self,
        embed_dim,
        num_heads,
        dropout=0.0,
        bias=True,
        add_bias_kv=False,
        add_zero_attn=False,
        kdim=None,
        vdim=None,
        batch_first=False,
        device=None,
        dtype=None,
    ) -> None:
        super().__init__(embed_dim,
                        num_heads,
                        dropout,
                        bias,
                        add_bias_kv,
                        add_zero_attn,
                        kdim,
                        vdim,
                        batch_first,
                        device,
                        dtype)
        self._setup()

    def _setup(self):
        self.input_quantizer = TensorQuantizer()
        self.weight_quantizer = TensorQuantizer()

    def forward(
        self,
        query: Tensor,
        key: Tensor,
        value: Tensor,
        key_padding_mask: Optional[Tensor] = None,
        need_weights: bool = True,
        attn_mask: Optional[Tensor] = None,
        average_attn_weights: bool = True,
        is_causal: bool = False,
    ) -> Tuple[Tensor, Optional[Tensor]]:

        if self.batch_first:
            query = query.permute(1, 0, 2).contiguous()
            key = key.permute(1, 0, 2).contiguous()
            value = value.permute(1, 0, 2).contiguous()

        if not self._qkv_same_embed_dim:
            w_q, w_k, w_v = self.q_proj_weight, self.k_proj_weight, self.v_proj_weight
        else:
            w_q, w_k, w_v = self.in_proj_weight.chunk(3)

        if self.in_proj_bias is None:
            b_q = b_k = b_v = None
        else:
            b_q, b_k, b_v = self.in_proj_bias.chunk(3)

        q = F.linear(self.input_quantizer(query), self.weight_quantizer(w_q), b_q)
        k = F.linear(self.input_quantizer(key), self.weight_quantizer(w_k), b_k)
        v = F.linear(self.input_quantizer(value), self.weight_quantizer(w_v), b_v)

        q, k, v = map(lambda t: rearrange(t, "s b (h d) -> b h s d", h=self.num_heads), (q, k, v))
        scores = torch.matmul(self.input_quantizer(q.contiguous()), self.input_quantizer(k.transpose(-2, -1).contiguous())) * (self.head_dim**-0.5)
        scores = F.softmax(scores, -1)
        result = torch.matmul(self.input_quantizer(scores), self.input_quantizer(v.contiguous()))

        out = F.linear(self.input_quantizer(rearrange(result, "b h s d -> s b (h d)", h=self.num_heads).contiguous()),            
                        self.weight_quantizer(self.out_proj.weight), self.out_proj.bias)
        if self.batch_first:
            out = out.permute(1, 0, 2).contiguous()

        return out, None



def get_int8_quant_config():
    config = copy.deepcopy(mtq.INT8_DEFAULT_CFG)
    config['quant_cfg']['*input_quantizer'] = {'num_bits': 8,"axis": None,"calibrator":"histogram"}
    config['quant_cfg']['map_head*input_quantizer'] = {"enable": False}
    config['quant_cfg']['map_head*weight_quantizer'] = {"enable": False}
    return config

def get_fp8_quant_config():
    config = copy.deepcopy(mtq.FP8_DEFAULT_CFG)
    config['quant_cfg']['*camera_encoder*'] = {"enable": False}
    return config

BaseExp = get_current_exp_class()

class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch, **kwargs)

    @classmethod
    def add_argparse_args(cls, parser):  # pragma: no-cover
        parser.add_argument(
            "--exp_options",
            nargs="+",
            action=DictAction,
            help="override some settings in the exp, the key-value pair in xxx=yyy format will be merged into exp. "
            'If the value to be overwritten is a list, it should be like key="[a,b]" or key=a,b '
            'It also allows nested list/tuple values, e.g. key="[(a,b),(c,d)]" '
            "Note that the quotation marks are necessary and that no white space is allowed.",
        )
        parser.add_argument("-b", "--batch-size-per-device", type=int, default=None)
        parser.add_argument("-e", "--max-epoch", type=int, default=None)
        return parser


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True

    cli = Det3DCli(Exp)
    if cli.args.ckpt is None:
        cli.args.ckpt = MapDeployConfig.ckpt_pth
    evaluator = cli.get_evaluator()
    model = configure_vision_encoder()
    val_dataloader = evaluator.val_dataloader

    def forward_loop(model, val_dataloader):
        val_iter = iter(val_dataloader) 
        dataset = val_dataloader.dataset
        for step in tqdm(range(900)): # disable=(local_rank > 0)
            data = next(val_iter)
            if hasattr(dataset, "batch_postcollate_fn"):
                dataset.batch_postcollate_fn(data)
            if hasattr(dataset, "batch_preforward_fn"):
                dataset.batch_preforward_fn(data)
            pred_item = model(**data, need_format_metas=True) # eval with loss

    def finish_stats_collection(model: nn.Module, method: Optional[str] = None):
        """Finish stats collection for all quantizers in the model."""
        for name, module in model.named_modules():
            if isinstance(module, TensorQuantizer) and not module._disabled:
                if module._calibrator is not None and not module._dynamic:
                    if isinstance(module._calibrator, HistogramCalibrator):
                        if method in ["mse", "entropy"]:
                            if module._calibrator.compute_amax(method) is not None:
                                module.load_calib_amax(method)
                        elif method=="percentile":
                            if module._calibrator.compute_amax(method) is not None:
                                module.load_calib_amax(method)
    
                    elif module._calibrator.compute_amax() is not None:
                        module.load_calib_amax()
                module.enable_quant()
                module.disable_calib()
    mtq.register(original_cls=Custom_Add, quantized_cls=QuantCustomAdd)
    mtq.register(original_cls=wrappers.ConvTranspose2d, quantized_cls=QConvTranspose2d)
    # mtq.register(original_cls=Custom_Split, quantized_cls=QuantCustomSplit)
    # mtq.register(original_cls=nn.MultiheadAttention, quantized_cls=QuantMultiheadAttention)
    config = get_int8_quant_config()
    apply_mode(model, mode=[("quantize", config)])
    logger.info("starting quant calib")
    enable_stats_collection(model)
    forward_loop(model, val_dataloader)
    finish_stats_collection(model, method='mse')
    mtq.print_quant_summary(model)
    mto.save(model.camera_encoder, MapDeployConfig.calib_model_path)