import tensorrt as trt
import numpy as np

from .trt_common import allocate_buffers, free_buffers, do_inference
from loguru import logger

from refile import smart_open

class TensorRTInference:
    def __init__(self, engine_file_path):
        """Initialize TensorRT inference engine.
        
        Args:
            engine_file_path (str): Path to the TensorRT engine file
        """
        self.TRT_LOGGER = trt.Logger(trt.Logger.INFO)
        trt.init_libnvinfer_plugins(self.TRT_LOGGER, "")
        self.engine_file_path = engine_file_path
        self.engine = None
        self.context = None
        self.inputs = None
        self.outputs = None
        self.bindings = None
        self.stream = None
        
    def initialize(self):
        """Initialize the TensorRT engine and allocate buffers."""
        logger.info(f"Reading engine from file {self.engine_file_path}")
        try:
            with smart_open(self.engine_file_path, "rb") as f, trt.Runtime(self.TRT_LOGGER) as runtime:
                self.engine = runtime.deserialize_cuda_engine(f.read())
                logger.info("Engine deserialized successfully")
        except Exception as e:
            logger.error(f"Error deserializing engine: {e}")
            raise e
        
        try:
            self.inputs, self.outputs, self.bindings, self.stream = allocate_buffers(self.engine)
            self.context = self.engine.create_execution_context()
            logger.info("Buffers allocated and execution context created")
        except Exception as e:
            logger.error(f"Error allocating buffers: {e}")
            raise e
        
    def inference(self, input_data):
        """Run inference with the provided input data.
        
        Args:
            input_data (dict): Dictionary mapping input names to numpy arrays
                Example: {
                    'input1': np.array(...),
                    'input2': np.array(...)
                }
            
        Returns:
            numpy.ndarray: Inference results
        """
        if not isinstance(input_data, dict):
            logger.error("Input data must be a dictionary")
            raise TypeError("Input data must be a dictionary mapping input names to numpy arrays")
        
        # Get input names from the engine
        input_names = [self.engine.get_tensor_name(i) for i in range(self.engine.num_io_tensors) 
                      if self.engine.get_tensor_mode(self.engine.get_tensor_name(i)) == trt.TensorIOMode.INPUT]
        logger.debug(f"Required input names: {input_names}")
        
        # Check if all required inputs are provided
        missing_inputs = set(input_names) - set(input_data.keys())
        if missing_inputs:
            logger.error(f"Missing required inputs: {missing_inputs}")
            raise ValueError(f"Missing required inputs: {missing_inputs}")
        
        # Set input data for each input tensor in the order of self.inputs
        for i in range(len(self.inputs)):
            name = self.inputs[i].name
            if not isinstance(input_data[name], np.ndarray):
                logger.error(f"Input {name} must be a numpy array")
                raise TypeError(f"Input {name} must be a numpy array")
            self.inputs[i].host = np.ascontiguousarray(input_data[name])
        
        try:
            results = do_inference(
                self.context, 
                self.engine, 
                bindings=self.bindings, 
                inputs=self.inputs, 
                outputs=self.outputs, 
                stream=self.stream
            )
            return results
        except Exception as e:
            logger.error(f"Error during inference: {e}")
            raise e
    
    def destroy(self):
        """Clean up resources when the object is destroyed."""
        if all([self.inputs, self.outputs, self.stream]):
            free_buffers(self.inputs, self.outputs, self.stream)
        del self.engine

# Example usage:
if __name__ == "__main__":
    # Initialize the inference engine
    engine_file = "resnet50-v1-12.engine"
    inferencer = TensorRTInference(engine_file)
    inferencer.initialize()
    
    # Run inference
    input_data = np.random.rand(1, 3, 224, 224).astype(np.float32)
    results = inferencer.inference({"data": input_data})
    print(results)
    inferencer.destroy()
