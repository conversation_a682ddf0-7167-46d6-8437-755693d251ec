import sys
import os
import importlib.util
import torch
import numpy as np


class MapDeployConfig:
    exp_path = "perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_r50_nowarp1500w_warp0504data.py"
    ckpt_pth = "s3://zqt/release_map/20250507/e2e_map_lane_20250507_r50.pth"
    default_onnx_path = "deploy_models/maplane_0425.onnx"
    default_engine_path = "s3://model-deploy-opt/maplane/0425/4090/maplane_0425_4090_fp32.engine"
    calib_model_path = "deploy_models/maplane_0507_resnet50_q.pth"

    maplane = True
    maplane_inputs = {
        
    }
    maplane_outputs = {
        "line_scores": {
            "shape": (1, 50),
            "dtype": torch.float32,
        },
        "line_labels": {
            "shape": (1, 50),
            "dtype": torch.int32,
        },
        "lines": {
            "shape": (1, 50, 20, 3),
            "dtype": torch.float32,
        },
        "attrs_lines": {
            "shape": (1, 50, 20, 3),
            "dtype": torch.int32,
        }
    }
    maplane_io_save_path = "io_tensors"

    mapdis = False
    mapdis_inputs = {

    }
    mapdis_outputs = {

    }


def import_class_from_file(module_name, file_path, class_name, alias_name):
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)

    class_ = getattr(module, class_name)
    globals()[alias_name] = class_
    return class_


def get_current_exp_class():
    file_path = MapDeployConfig.exp_path
    module_name = os.path.basename(MapDeployConfig.exp_path).split(".")[0]
    class_name = "Exp"
    alias_name = "BaseExp"

    BaseExp = import_class_from_file(module_name, file_path, class_name, alias_name)
    return BaseExp

def cosine_similarity(y_pred, y_real, reduction: str = 'mean'):

    if y_pred.shape != y_real.shape:
        raise ValueError(
            f'Can not compute mse loss for tensors with different shape. '
            f'({y_pred.shape} and {y_real.shape})')
    reduction = str(reduction).lower()

    y_pred = y_pred.reshape(1, -1)
    y_real = y_real.reshape(1, -1)


    y_pred_norm = np.sqrt(np.sum(y_pred * y_pred, axis=-1))
    y_real_norm = np.sqrt(np.sum(y_real * y_real, axis=-1))
    cosine_sim = (np.sum(y_pred * y_real, axis=-1) + 1e-7) / (
        (y_pred_norm * y_real_norm) + 1e-7)  # eps

    if reduction == 'mean':
        return np.mean(cosine_sim)
    elif reduction == 'sum':
        return np.sum(cosine_sim)
    elif reduction == 'none':
        return cosine_sim
    else:
        raise ValueError(f'Unsupported reduction method.')