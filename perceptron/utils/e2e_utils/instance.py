# ------------------------------------------------------------------------
# Copyright (c) 2023 toyota research instutute.
# ------------------------------------------------------------------------
# Modified from MOTR (https://github.com/megvii-model/MOTR/)
# ------------------------------------------------------------------------
# Modified from Detectron2 (https://github.com/facebookresearch/detectron2)
# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
# ------------------------------------------------------------------------

import itertools
from typing import Any, Dict, List, Tuple, Union
import torch
import torch.nn as nn
from torch.cuda.amp import autocast as autocast
from perceptron.utils.e2e_utils.utils import pos2posemb3d
from mmdet.models.utils.transformer import inverse_sigmoid
import math


def pos2embed(pos, num_pos_feats=128, temperature=10000):
    scale = 2 * math.pi
    pos = pos * scale
    dim_t = torch.arange(num_pos_feats, dtype=torch.float32, device=pos.device)
    dim_t = temperature ** (2 * (dim_t // 2) / num_pos_feats)
    pos_x = pos[..., 0, None] / dim_t
    pos_y = pos[..., 1, None] / dim_t
    # pos_z = pos[..., 2, None] / dim_t
    pos_x = torch.stack((pos_x[..., 0::2].sin(), pos_x[..., 1::2].cos()), dim=-1).flatten(-2)
    pos_y = torch.stack((pos_y[..., 0::2].sin(), pos_y[..., 1::2].cos()), dim=-1).flatten(-2)
    # pos_z = torch.stack((pos_z[..., 0::2].sin(), pos_z[..., 1::2].cos()), dim=-1).flatten(-2)
    posemb = torch.cat((pos_y, pos_x), dim=-1)
    return posemb


class Instances:
    """
    This class represents a list of instances in an image.
    It stores the attributes of instances (e.g., boxes, masks, labels, scores) as "fields".
    All fields must have the same ``__len__`` which is the number of instances.
    All other (non-field) attributes of this class are considered private:
    they must start with '_' and are not modifiable by a user.
    Some basic usage:
    1. Set/get/check a field:
       .. code-block:: python
          instances.gt_boxes = Boxes(...)
          print(instances.pred_masks)  # a tensor of shape (N, H, W)
          print('gt_masks' in instances)
    2. ``len(instances)`` returns the number of instances
    3. Indexing: ``instances[indices]`` will apply the indexing on all the fields
       and returns a new :class:`Instances`.
       Typically, ``indices`` is a integer vector of indices,
       or a binary mask of length ``num_instances``
       .. code-block:: python
          category_3_detections = instances[instances.pred_classes == 3]
          confident_detections = instances[instances.scores > 0.9]
    """

    def __init__(self, image_size: Tuple[int, int], **kwargs: Any):
        """
        Args:
            image_size (height, width): the spatial size of the image.
            kwargs: fields to add to this `Instances`.
        """
        self._image_size = image_size
        self._fields: Dict[str, Any] = {}
        for k, v in kwargs.items():
            self.set(k, v)

    @property
    def image_size(self) -> Tuple[int, int]:
        """
        Returns:
            tuple: height, width
        """
        return self._image_size

    def __setattr__(self, name: str, val: Any) -> None:
        if name.startswith("_"):
            super().__setattr__(name, val)
        else:
            self.set(name, val)

    def __getattr__(self, name: str) -> Any:
        if name == "_fields" or name not in self._fields:
            raise AttributeError("Cannot find field '{}' in the given Instances!".format(name))
        return self._fields[name]

    def set(self, name: str, value: Any) -> None:
        """
        Set the field named `name` to `value`.
        The length of `value` must be the number of instances,
        and must agree with other existing fields in this object.
        """
        data_len = len(value)
        if len(self._fields):
            assert len(self) == data_len, "Adding a field of length {} to a Instances of length {}".format(
                data_len, len(self)
            )
        self._fields[name] = value

    def has(self, name: str) -> bool:
        """
        Returns:
            bool: whether the field called `name` exists.
        """
        return name in self._fields

    def remove(self, name: str) -> None:
        """
        Remove the field called `name`.
        """
        del self._fields[name]

    def get(self, name: str) -> Any:
        """
        Returns the field called `name`.
        """
        return self._fields[name]

    def get_fields(self) -> Dict[str, Any]:
        """
        Returns:
            dict: a dict which maps names (str) to data of the fields
        Modifying the returned dict will modify this instance.
        """
        return self._fields

    # Tensor-like methods
    def to(self, *args: Any, **kwargs: Any) -> "Instances":
        """
        Returns:
            Instances: all fields are called with a `to(device)`, if the field has this method.
        """
        ret = Instances(self._image_size)
        for k, v in self._fields.items():
            if hasattr(v, "to"):
                v = v.to(*args, **kwargs)
            ret.set(k, v)
        return ret

    def numpy(self):
        ret = Instances(self._image_size)
        for k, v in self._fields.items():
            if hasattr(v, "numpy"):
                v = v.numpy()
            ret.set(k, v)
        return ret

    def __getitem__(self, item: Union[int, slice, torch.BoolTensor]) -> "Instances":
        """
        Args:
            item: an index-like object and will be used to index all the fields.
        Returns:
            If `item` is a string, return the data in the corresponding field.
            Otherwise, returns an `Instances` where all fields are indexed by `item`.
        """
        if type(item) == int:
            if item >= len(self) or item < -len(self):
                raise IndexError("Instances index out of range!")
            else:
                item = slice(item, None, len(self))

        ret = Instances(self._image_size)
        for k, v in self._fields.items():
            # print(k, type(item), 'getitem', item.type(), item.dtype)
            # if index by torch.BoolTensor
            if k == "kalman_models" and isinstance(item, torch.Tensor):
                # print(item.shape, 'in get item')
                ret_list = []
                for i, if_true in enumerate(item):
                    if if_true:
                        ret_list.append(self.kalman_models[i])
                ret.set(k, ret_list)

            else:
                ret.set(k, v[item])
        return ret

    def __len__(self) -> int:
        for v in self._fields.values():
            # use __len__ because len() has to be int and is not friendly to tracing
            return v.__len__()
        raise NotImplementedError("Empty Instances does not support __len__!")

    def __iter__(self):
        raise NotImplementedError("`Instances` object is not iterable!")

    @staticmethod
    def cat(instance_lists: List["Instances"]) -> "Instances":
        """
        Args:
            instance_lists (list[Instances])
        Returns:
            Instances
        """
        assert all(isinstance(i, Instances) for i in instance_lists)
        assert len(instance_lists) > 0
        if len(instance_lists) == 1:
            return instance_lists[0]

        image_size = instance_lists[0].image_size
        for i in instance_lists[1:]:
            assert i.image_size == image_size
        ret = Instances(image_size)
        for k in instance_lists[0]._fields.keys():
            values = [i.get(k) for i in instance_lists]
            v0 = values[0]
            if isinstance(v0, torch.Tensor):
                values = torch.cat(values, dim=0)
            elif isinstance(v0, list):
                values = list(itertools.chain(*values))
            elif hasattr(type(v0), "cat"):
                values = type(v0).cat(values)
            else:
                raise ValueError("Unsupported type {} for concatenation".format(type(v0)))
            ret.set(k, values)
        return ret

    def clone(self):
        ret = Instances(self._image_size)
        for k, v in self._fields.items():
            if hasattr(v, "clone"):
                v = v.clone()
            ret.set(k, v)
        return ret

    def detach(self):
        ret = Instances(self._image_size)
        for k, v in self._fields.items():
            if hasattr(v, "detach"):
                v = v.detach()
            ret.set(k, v)
        return ret

    def __str__(self) -> str:
        s = self.__class__.__name__ + "("
        s += "num_instances={}, ".format(len(self))
        s += "image_height={}, ".format(self._image_size[0])
        s += "image_width={}, ".format(self._image_size[1])
        s += "fields=[{}])".format(", ".join((f"{k}: {v}" for k, v in self._fields.items())))
        return s

    __repr__ = __str__


class TrackInstance(Instances):
    def __init__(self, image_size: Tuple[int, int], **kwargs: Any) -> Any:
        super(TrackInstance, self).__init__(image_size, **kwargs)

    @staticmethod
    @autocast(False)
    def generate_empty_instance(
        reference_points: nn.Embedding,
        query_embedding: nn.Sequential,
        num_classes: int,
        tracking: bool,
        tracking_cfg: tuple,
        prediction: bool,
        prediction_cfg: tuple,
        embed_dims: int = 256,
        query_feat_embedding: nn.Embedding = None,
        radar_points: torch.tensor = None,
        bev_query_embed: nn.Sequential = None,
        hidden_dim: int = 256,
    ) -> "TrackInstance":
        """Generate empty instance slots at the beginning of tracking"""
        track_instances = TrackInstance((1, 1))
        device = reference_points.weight.device

        """Detection queries"""
        # reference points, query embeds, and query targets (features)
        reference_points = reference_points.weight
        reference_points = reference_points

        if radar_points is not None:
            # 直接定义在
            # init_radar_query = get_radar_pos_init(radar_points, pc_range)
            reference_points = torch.cat([radar_points, reference_points], dim=0)

        reference_points = inverse_sigmoid(reference_points.clone()).sigmoid()
        query_embeds = get_query_embeds(reference_points, radar_points, query_embedding, bev_query_embed, hidden_dim)
        # origin 要写在完全的外面
        # 考虑一下传什么
        track_instances.reference_points = reference_points.clone()
        track_instances.query_embeds = query_embeds.clone()

        if tracking:
            if radar_points is not None:
                radar_query_feats = torch.zeros(
                    (radar_points.shape[0], query_embeds.shape[1]), dtype=torch.float, device=device
                )
                track_instances.query_feats = torch.cat([radar_query_feats, query_feat_embedding.weight.clone()], dim=0)
            else:
                track_instances.query_feats = query_feat_embedding.weight.clone()
        else:
            track_instances.query_feats = torch.zeros_like(query_embeds)

        """Current frame information"""
        # classification scores
        track_instances.logits = torch.zeros((len(track_instances), num_classes), dtype=torch.float, device=device)
        # bounding boxes
        track_instances.bboxes = torch.zeros((len(track_instances), 10), dtype=torch.float, device=device)
        # track scores, normally the scores for the highest class
        track_instances.scores = torch.zeros((len(track_instances)), dtype=torch.float, device=device)

        """Cache for current frame information, loading temporary data for spatial-temporal reasoining"""
        track_instances.cache_logits = torch.zeros(
            (len(track_instances), num_classes), dtype=torch.float, device=device
        )
        track_instances.cache_bboxes = torch.zeros((len(track_instances), 10), dtype=torch.float, device=device)
        track_instances.cache_scores = torch.zeros((len(track_instances),), dtype=torch.float, device=device)
        track_instances.cache_reference_points = reference_points.clone()
        track_instances.cache_query_embeds = query_embeds.clone()
        if tracking:
            if radar_points is not None:
                radar_query_feats = torch.zeros(
                    (radar_points.shape[0], query_embeds.shape[1]), dtype=torch.float, device=device
                )
                track_instances.cache_query_feats = torch.cat(
                    [radar_query_feats, query_feat_embedding.weight.clone()], dim=0
                )
            else:
                track_instances.cache_query_feats = query_feat_embedding.weight.clone()
        else:
            track_instances.cache_query_feats = torch.zeros_like(query_embeds)

        if tracking:
            (hist_len, fut_len) = tracking_cfg

            """Tracking information"""
            # id for the tracks
            track_instances.obj_idxes = torch.full((len(track_instances),), -1, dtype=torch.long, device=device)
            # matched gt indexes, for loss computation
            track_instances.matched_gt_idxes = torch.full((len(track_instances),), -1, dtype=torch.long, device=device)
            # life cycle management
            track_instances.disappear_time = torch.zeros((len(track_instances),), dtype=torch.long, device=device)
            track_instances.track_query_mask = torch.zeros((len(track_instances),), dtype=torch.bool, device=device)

            """History Reasoning"""
            # embeddings
            track_instances.hist_embeds = torch.zeros(
                (len(track_instances), hist_len, embed_dims), dtype=torch.float32, device=device
            )
            track_instances.hist_embeds_transform = torch.zeros(
                (len(track_instances), hist_len, embed_dims), dtype=torch.float32, device=device
            )
            # padding mask, follow MultiHeadAttention, 1 indicates padded
            track_instances.hist_padding_masks = torch.ones(
                (len(track_instances), hist_len), dtype=torch.bool, device=device
            )
            # timstamp
            track_instances.hist_timestamp = torch.zeros(
                (len(track_instances), hist_len), dtype=torch.float32, device=device
            )
            track_instances.cache_hist_timestamp = torch.zeros(
                (len(track_instances), 1), dtype=torch.float32, device=device
            )
            # positions
            track_instances.hist_xyz = torch.zeros(
                (len(track_instances), hist_len, 3), dtype=torch.float, device=device
            )
            # positional embeds
            track_instances.hist_position_embeds = torch.zeros(
                (len(track_instances), hist_len, embed_dims), dtype=torch.float32, device=device
            )
            # bboxes
            track_instances.hist_bboxes = torch.zeros(
                (len(track_instances), hist_len, 10), dtype=torch.float, device=device
            )
            # logits
            track_instances.hist_logits = torch.zeros(
                (len(track_instances), hist_len, num_classes), dtype=torch.float, device=device
            )
            # scores
            track_instances.hist_scores = torch.zeros(
                (len(track_instances), hist_len), dtype=torch.float, device=device
            )

            """Future Reasoning"""
            # embeddings
            track_instances.fut_embeds = torch.zeros(
                (len(track_instances), fut_len, embed_dims), dtype=torch.float32, device=device
            )
            # padding mask, follow MultiHeadAttention, 1 indicates padded
            track_instances.fut_padding_masks = torch.ones(
                (len(track_instances), fut_len), dtype=torch.bool, device=device
            )
            # positions
            track_instances.fut_xyz = torch.zeros((len(track_instances), fut_len, 3), dtype=torch.float, device=device)
            # positional embeds
            track_instances.fut_position_embeds = torch.zeros(
                (len(track_instances), fut_len, embed_dims), dtype=torch.float32, device=device
            )
            # bboxes
            track_instances.fut_bboxes = torch.zeros(
                (len(track_instances), fut_len, 10), dtype=torch.float, device=device
            )
            # logits
            track_instances.fut_logits = torch.zeros(
                (len(track_instances), fut_len, num_classes), dtype=torch.float, device=device
            )
            # scores
            track_instances.fut_scores = torch.zeros((len(track_instances), fut_len), dtype=torch.float, device=device)

            """fut predictions for Track Extension"""
            # fut predictions, not normalized
            track_instances.fut_predictions = torch.zeros(
                (len(track_instances), fut_len, 3), dtype=torch.float, device=device
            )
            # cache for fut predictions
            track_instances.cache_fut_predictions = torch.zeros_like(track_instances.fut_predictions)

        if prediction:
            (pred_len,) = prediction_cfg

            """Traj Prediction"""
            track_instances.traj_embeds = torch.zeros(
                (len(track_instances), pred_len, embed_dims), dtype=torch.float32, device=device
            )
            track_instances.history_locs = torch.zeros((len(track_instances), 20, 3), dtype=torch.float, device=device)
            track_instances.history_masks = torch.zeros((len(track_instances), 20), dtype=torch.float, device=device)
            # timstamp
            track_instances.history_timestamp = torch.zeros(
                (len(track_instances), 20), dtype=torch.float32, device=device
            )
            track_instances.cache_history_timestamp = torch.zeros(
                (len(track_instances), 1), dtype=torch.float32, device=device
            )
            track_instances.traj_predictions = torch.zeros(
                (len(track_instances), pred_len, 3), dtype=torch.float, device=device
            )
            # cache for traj prediction
            track_instances.cache_traj_predictions = torch.zeros_like(track_instances.traj_predictions)

        return track_instances

    @staticmethod
    @autocast(False)
    def generate_empty_predition_instance(
        track_instance: "TrackInstance",
        prediction: bool,
        prediction_cfg: tuple,
        embed_dims: int = 256,
    ) -> "TrackInstance":
        """Generate empty instance slots at the beginning of tracking"""
        prediction_instances = TrackInstance((1, 1))
        device = track_instance.reference_points.device

        if prediction:
            (pred_len,) = prediction_cfg

            """Traj Prediction"""
            prediction_instances.traj_embeds = torch.zeros(
                (len(prediction_instances), pred_len, embed_dims), dtype=torch.float32, device=device
            )
            prediction_instances.history_locs = torch.zeros(
                (len(track_instance), 20, 3), dtype=torch.float, device=device
            )
            prediction_instances.history_masks = torch.zeros(
                (len(track_instance), 20), dtype=torch.float, device=device
            )
            # timstamp
            prediction_instances.history_timestamp = torch.zeros(
                (len(prediction_instances), 20), dtype=torch.float32, device=device
            )
            prediction_instances.cache_history_timestamp = torch.zeros(
                (len(prediction_instances), 1), dtype=torch.float32, device=device
            )
            prediction_instances.traj_predictions = torch.zeros(
                (len(prediction_instances), pred_len, 3), dtype=torch.float, device=device
            )
            # cache for traj prediction
            prediction_instances.cache_traj_predictions = torch.zeros_like(prediction_instances.traj_predictions)

        return prediction_instances

    @staticmethod
    @autocast(False)
    def generate_empty_track_instance(
        reference_points: nn.Embedding,
        query_embedding: nn.Sequential,
        num_classes: int,
        tracking: bool,
        tracking_cfg: tuple,
        embed_dims: int = 256,
        query_feat_embedding: nn.Embedding = None,
        radar_points: torch.tensor = None,
        bev_query_embed: nn.Sequential = None,
        hidden_dim: int = 256,
    ) -> "TrackInstance":
        """Generate empty instance slots at the beginning of tracking"""
        track_instances = TrackInstance((1, 1))
        device = reference_points.weight.device

        """Detection queries"""
        # reference points, query embeds, and query targets (features)
        reference_points = reference_points.weight
        reference_points = reference_points

        if radar_points is not None:
            # 直接定义在
            # init_radar_query = get_radar_pos_init(radar_points, pc_range)
            reference_points = torch.cat([radar_points, reference_points], dim=0)

        reference_points = inverse_sigmoid(reference_points.clone()).sigmoid()
        query_embeds = get_query_embeds(reference_points, radar_points, query_embedding, bev_query_embed, hidden_dim)
        # origin 要写在完全的外面
        # 考虑一下传什么
        track_instances.reference_points = reference_points.clone()
        track_instances.query_embeds = query_embeds.clone()

        if tracking:
            if radar_points is not None:
                radar_query_feats = torch.zeros(
                    (radar_points.shape[0], query_embeds.shape[1]), dtype=torch.float, device=device
                )
                track_instances.query_feats = torch.cat([radar_query_feats, query_feat_embedding.weight.clone()], dim=0)
            else:
                track_instances.query_feats = query_feat_embedding.weight.clone()
        else:
            track_instances.query_feats = torch.zeros_like(query_embeds)

        """Current frame information"""
        # classification scores
        track_instances.logits = torch.zeros((len(track_instances), num_classes), dtype=torch.float, device=device)
        # bounding boxes
        track_instances.bboxes = torch.zeros((len(track_instances), 10), dtype=torch.float, device=device)
        # track scores, normally the scores for the highest class
        track_instances.scores = torch.zeros((len(track_instances)), dtype=torch.float, device=device)

        """Cache for current frame information, loading temporary data for spatial-temporal reasoining"""
        track_instances.cache_logits = torch.zeros(
            (len(track_instances), num_classes), dtype=torch.float, device=device
        )
        track_instances.cache_bboxes = torch.zeros((len(track_instances), 10), dtype=torch.float, device=device)
        track_instances.cache_scores = torch.zeros((len(track_instances),), dtype=torch.float, device=device)
        track_instances.cache_reference_points = reference_points.clone()
        track_instances.cache_query_embeds = query_embeds.clone()
        if tracking:
            if radar_points is not None:
                radar_query_feats = torch.zeros(
                    (radar_points.shape[0], query_embeds.shape[1]), dtype=torch.float, device=device
                )
                track_instances.cache_query_feats = torch.cat(
                    [radar_query_feats, query_feat_embedding.weight.clone()], dim=0
                )
            else:
                track_instances.cache_query_feats = query_feat_embedding.weight.clone()
        else:
            track_instances.cache_query_feats = torch.zeros_like(query_embeds)

        if tracking:
            (hist_len, fut_len) = tracking_cfg

            """Tracking information"""
            # id for the tracks
            track_instances.obj_idxes = torch.full((len(track_instances),), -1, dtype=torch.long, device=device)
            # matched gt indexes, for loss computation
            track_instances.matched_gt_idxes = torch.full((len(track_instances),), -1, dtype=torch.long, device=device)
            # life cycle management
            track_instances.disappear_time = torch.zeros((len(track_instances),), dtype=torch.long, device=device)
            track_instances.track_query_mask = torch.zeros((len(track_instances),), dtype=torch.bool, device=device)

            """History Reasoning"""
            # embeddings
            track_instances.hist_embeds = torch.zeros(
                (len(track_instances), hist_len, embed_dims), dtype=torch.float32, device=device
            )
            track_instances.hist_embeds_transform = torch.zeros(
                (len(track_instances), hist_len, embed_dims), dtype=torch.float32, device=device
            )
            # padding mask, follow MultiHeadAttention, 1 indicates padded
            track_instances.hist_padding_masks = torch.ones(
                (len(track_instances), hist_len), dtype=torch.bool, device=device
            )
            # timstamp
            track_instances.hist_timestamp = torch.zeros(
                (len(track_instances), hist_len), dtype=torch.float32, device=device
            )
            track_instances.cache_hist_timestamp = torch.zeros(
                (len(track_instances), 1), dtype=torch.float32, device=device
            )
            # positions
            track_instances.hist_xyz = torch.zeros(
                (len(track_instances), hist_len, 3), dtype=torch.float, device=device
            )
            # positional embeds
            track_instances.hist_position_embeds = torch.zeros(
                (len(track_instances), hist_len, embed_dims), dtype=torch.float32, device=device
            )
            # bboxes
            track_instances.hist_bboxes = torch.zeros(
                (len(track_instances), hist_len, 10), dtype=torch.float, device=device
            )
            # logits
            track_instances.hist_logits = torch.zeros(
                (len(track_instances), hist_len, num_classes), dtype=torch.float, device=device
            )
            # scores
            track_instances.hist_scores = torch.zeros(
                (len(track_instances), hist_len), dtype=torch.float, device=device
            )

            """Future Reasoning"""
            # embeddings
            track_instances.fut_embeds = torch.zeros(
                (len(track_instances), fut_len, embed_dims), dtype=torch.float32, device=device
            )
            # padding mask, follow MultiHeadAttention, 1 indicates padded
            track_instances.fut_padding_masks = torch.ones(
                (len(track_instances), fut_len), dtype=torch.bool, device=device
            )
            # positions
            track_instances.fut_xyz = torch.zeros((len(track_instances), fut_len, 3), dtype=torch.float, device=device)
            # positional embeds
            track_instances.fut_position_embeds = torch.zeros(
                (len(track_instances), fut_len, embed_dims), dtype=torch.float32, device=device
            )
            # bboxes
            track_instances.fut_bboxes = torch.zeros(
                (len(track_instances), fut_len, 10), dtype=torch.float, device=device
            )
            # logits
            track_instances.fut_logits = torch.zeros(
                (len(track_instances), fut_len, num_classes), dtype=torch.float, device=device
            )
            # scores
            track_instances.fut_scores = torch.zeros((len(track_instances), fut_len), dtype=torch.float, device=device)

            """fut predictions for Track Extension"""
            # fut predictions, not normalized
            track_instances.fut_predictions = torch.zeros(
                (len(track_instances), fut_len, 3), dtype=torch.float, device=device
            )
            track_instances.fut_velocity = torch.zeros(
                (len(track_instances), fut_len, 3), dtype=torch.float, device=device
            )
            # cache for fut predictions
            track_instances.cache_fut_predictions = torch.zeros_like(track_instances.fut_predictions)
            # cache for fut velocity
            track_instances.cache_fut_velocity = torch.zeros_like(track_instances.fut_velocity)

        return track_instances

    @staticmethod
    @autocast(False)
    def load_detection_output_into_cache(
        query_embedding: nn.Sequential,
        track_instances: "TrackInstance",
        out: dict,
        bev_query_embed: nn.Sequential = None,
        radar_points=None,
        timestamp=0,
        use_relative_ts=False,
    ) -> "TrackInstance":
        """Load output of the detection head into the track_instances cache (inplace)"""
        query_feats = out.pop("query_feats")
        query_reference_points = out.pop("reference_points")
        with torch.no_grad():
            track_scores = out["all_cls_scores"][-1, 0, :].sigmoid().max(dim=-1).values
        track_instances.cache_scores = track_scores.clone()
        track_instances.cache_logits = out["all_cls_scores"][-1, 0].clone()
        track_instances.cache_query_feats = query_feats[0].clone()
        track_instances.cache_reference_points = query_reference_points[0].clone()
        track_instances.cache_bboxes = out["all_bbox_preds"][-1, 0].clone()
        # 这个更新是咋来的？
        track_instances.cache_query_embeds = get_query_embeds(
            track_instances.cache_reference_points, radar_points, query_embedding, bev_query_embed
        )
        if use_relative_ts:
            timestamp = [timestamp[0] % 1e10 / 1e6]
            ts = (
                torch.tensor(timestamp, dtype=torch.float64, device=query_feats[0].device)
                .repeat(query_feats[0].shape[0])
                .unsqueeze(-1)
            )
            track_instances.cache_hist_timestamp = ts.float()
            track_instances.cache_history_timestamp = ts.float()

        return track_instances

    @staticmethod
    @autocast(False)
    def tracking_summarization(
        record_threshold: int,
        track_instances: "TrackInstance",
        future_reasoning: bool = False,
        fut_len: int = 0,
        pc_range: list = [-75.0, -75.0, -5.0, 75.0, 75.0, 3.0],
        is_infer: bool = False,
    ) -> "TrackInstance":
        """Load the results after spatial-temporal reasoning into track instances"""
        # inference mode
        if is_infer:
            active_mask = track_instances.cache_scores >= record_threshold
        # training mode
        else:
            track_instances.bboxes = track_instances.cache_bboxes.clone()
            track_instances.logits = track_instances.cache_logits.clone()
            track_instances.scores = track_instances.cache_scores.clone()
            active_mask = track_instances.cache_scores >= 0.0

        track_instances.query_feats[active_mask] = track_instances.cache_query_feats[active_mask]
        track_instances.query_embeds[active_mask] = track_instances.cache_query_embeds[active_mask]
        track_instances.bboxes[active_mask] = track_instances.cache_bboxes[active_mask]
        track_instances.logits[active_mask] = track_instances.cache_logits[active_mask]
        track_instances.scores[active_mask] = track_instances.cache_scores[active_mask]
        track_instances.fut_predictions[active_mask] = track_instances.cache_fut_predictions[active_mask]
        track_instances.fut_velocity[active_mask] = track_instances.cache_fut_velocity[active_mask]
        track_instances.reference_points[active_mask] = track_instances.cache_reference_points[active_mask]

        # TODO: generate future bounding boxes, reference points, scores
        if future_reasoning:
            fut_predictions = track_instances.fut_predictions[active_mask]
            motion_add = torch.cumsum(fut_predictions.clone().detach(), dim=1)
            motion_add_normalized = motion_add.clone()
            motion_add_normalized[..., 0] /= pc_range[3] - pc_range[0]
            motion_add_normalized[..., 1] /= pc_range[4] - pc_range[1]

            track_instances.fut_xyz[active_mask] = (
                track_instances.reference_points[active_mask].clone()[:, None, :].repeat(1, fut_len, 1)
            )
            track_instances.fut_xyz[active_mask, :, 0] += motion_add_normalized[..., 0]
            track_instances.fut_xyz[active_mask, :, 1] += motion_add_normalized[..., 1]

            track_instances.fut_bboxes[active_mask] = (
                track_instances.bboxes[active_mask].clone()[:, None, :].repeat(1, fut_len, 1)
            )
            track_instances.fut_bboxes[active_mask, :, 0] += motion_add[..., 0]
            track_instances.fut_bboxes[active_mask, :, 1] += motion_add[..., 1]
        return track_instances


def get_query_embeds(reference_points, radar_points, query_embedding, bev_query_embed, hidden_dim=256):
    query_embeds = None
    if radar_points is not None and bev_query_embed is not None:
        # radar_bev_query_embeds = bev_query_embed(pos2embed(reference_points, num_pos_feats=hidden_dim))
        radar_bev_query_embeds = bev_query_embed(pos2posemb3d(reference_points))

        if query_embeds is not None:
            query_embeds = query_embeds + radar_bev_query_embeds
        else:
            query_embeds = radar_bev_query_embeds

    # if img_metas_single_frame is not None:
    if reference_points is not None:

        # rv_query_embeds = query_embedding(pos2posemb3d(reference_points))
        rv_query_embeds = query_embedding(pos2posemb3d(reference_points, num_pos_feats=hidden_dim // 2))
        # img_metas_single_frame['ida_mats'] = img_metas_single_frame['ida_mats'].unsqueeze(1)
        # rv_query_embeds = self.det_head.dense_head._rv_query_embed(ref_points, img_metas_single_frame) #结果是完全一样的
        if query_embeds is not None:
            query_embeds += rv_query_embeds
        else:
            query_embeds = rv_query_embeds
    return query_embeds
