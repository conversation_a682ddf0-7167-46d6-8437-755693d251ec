# ------------------------------------------------------------------------
# Copyright (c) 2023 toyota research instutute.
# ------------------------------------------------------------------------
import torch
from perceptron.utils.e2e_utils.instance import Instances
import random


class RunTimeTracker:
    def __init__(
        self,
        output_threshold=0.2,
        score_threshold=0.4,
        record_threshold=0.4,
        max_age_since_update=1,
        drop_probability=0.0,
        frag_probability=0.0,
        fp_ratio=0.0,
        hist_fp_num=0,
        hist_fp_prob=0.2,
        hist_fp_memory_len=600,
    ):
        self.current_id = 1
        self.current_seq = 0
        self.timestamp = None
        self.time_delta = None
        self.query_embeddings = None
        self.reference_points = None
        self.frame_index = 0

        self.track_instances = None
        self.timestamp = None
        self.first_frame = None

        self.threshold = score_threshold
        self.output_threshold = output_threshold
        self.record_threshold = record_threshold
        self.max_age_since_update = max_age_since_update

        self.drop_probability = drop_probability
        self.frag_probability = frag_probability
        self.fp_ratio = fp_ratio
        self.hist_fp_prob = hist_fp_prob
        self.hist_fp_num = hist_fp_num

        self.history_track_memory = None
        self.hist_fp_memory_len = hist_fp_memory_len

    def update_active_tracks(self, track_instances, active_mask):
        # first frame
        if self.track_instances is None:
            self.track_instances = track_instances[active_mask]
            return

        live_mask = torch.zeros_like(track_instances.obj_idxes).bool().detach()
        for i in range(len(track_instances)):
            if active_mask[i]:
                track_instances.disappear_time[i] = 0
                live_mask[i] = True
            elif track_instances.track_query_mask[i]:
                track_instances.disappear_time[i] += 1
                if track_instances.disappear_time[i] < self.max_age_since_update:
                    live_mask[i] = True
        self.track_instances = track_instances[live_mask]
        return

    def insert_history_fp(self, track_instances):
        if len(track_instances) < self.hist_fp_num:
            return track_instances
        if self.hist_fp_num > 0:
            if self.history_track_memory is not None:
                if len(self.history_track_memory) >= self.hist_fp_memory_len:
                    if random.random() < 0.2:
                        # insert history track
                        # selected_index = random.choices(range(len(self.history_track_memory)), k=self.hist_fp_num)
                        fp_num = random.randint(1, self.hist_fp_num)
                        track_instances = Instances.cat([track_instances, self.history_track_memory[:fp_num].detach()])
                    # update self.history_track_memory
                    saved_index = random.sample(
                        range(len(track_instances) - self.hist_fp_num),
                        k=min(self.hist_fp_num, len(track_instances) - self.hist_fp_num),
                    )
                    self.history_track_memory = Instances.cat(
                        [
                            self.history_track_memory[range(len(self.history_track_memory))[self.hist_fp_num :]],
                            track_instances[saved_index].detach(),
                        ]
                    )
                    self.history_track_memory[
                        -min(self.hist_fp_num, len(track_instances) - self.hist_fp_num) :
                    ].obj_idxes += 10000
                else:
                    saved_index = random.sample(
                        range(len(track_instances)), k=min(self.hist_fp_num, len(track_instances))
                    )
                    self.history_track_memory = Instances.cat(
                        [self.history_track_memory, track_instances[saved_index].detach()]
                    )
                    self.history_track_memory[-self.hist_fp_num :].obj_idxes += 10000
            else:
                # update self.history_track_memory
                saved_index = random.choices(range(len(track_instances)), k=min(self.hist_fp_num, len(track_instances)))
                self.history_track_memory = track_instances[saved_index].detach()
                self.history_track_memory.obj_idxes += 10000
        return track_instances

    def get_active_mask(self, track_instances, training=True):
        if training:
            active_mask = track_instances.matched_gt_idxes >= 0
            if self.drop_probability > 0 and len(track_instances) > 0:
                active_mask *= torch.rand_like(track_instances.scores) > self.drop_probability

            if self.frag_probability > 0 and len(track_instances) > 0:
                device = track_instances.matched_gt_idxes.device
                hist_frag_mask = (
                    torch.rand_like(track_instances.hist_padding_masks[:, :-1], dtype=torch.float32)
                    < self.frag_probability
                )
                hist_frag_mask = torch.cat(
                    (hist_frag_mask, torch.zeros((len(track_instances), 1), dtype=torch.bool, device=device)), dim=1
                )
                track_instances.hist_padding_masks = hist_frag_mask | track_instances.hist_padding_masks

            if self.fp_ratio > 0 and active_mask.sum() > 0:
                inactive_mask = track_instances.obj_idxes < 0

                # add fp for each active track in a specific probability.
                fp_prob = torch.ones_like(track_instances.scores) * self.fp_ratio
                selected_mask = torch.bernoulli(fp_prob).bool() & active_mask
                if inactive_mask.sum() > 0 and selected_mask.sum() > 0:
                    num_fp = selected_mask.sum()
                    if num_fp >= inactive_mask.sum():
                        tgt_fp_index = torch.nonzero(inactive_mask).flatten()
                    else:
                        distance = torch.cdist(
                            track_instances[inactive_mask].bboxes[..., :2],
                            track_instances[selected_mask].bboxes[..., :2],
                        )
                        # select the fp with the minimum distance for each active track.
                        fp_indexes = distance.min(dim=0).indices
                        # remove duplicate fp.
                        fp_indexes = torch.unique(fp_indexes)

                        tgt_fp_index = torch.nonzero(inactive_mask).flatten()
                        tgt_fp_index = tgt_fp_index[fp_indexes]
                    active_mask[tgt_fp_index] = True

        return active_mask

    def empty(self):
        """Copy the historical buffer parts from the init"""
        self.current_id = 1
        self.current_seq = 0
        self.timestamp = None
        self.query_embeddings = None
        self.reference_points = None
        self.frame_index = 0

        self.track_instances = None
        self.timestamp = None
        self.first_frame = None
