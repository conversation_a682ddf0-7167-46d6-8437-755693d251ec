"""
-> train:
    python3 perceptron/exps/lane3d/bevcond3dlane_r50_896x512_exp.py -d 0-7 -b 2 -e 15 --sync_bn 8 --no-clearml
-> amp train:
    python3 perceptron/exps/lane3d/bevcond3dlane_r50_896x512_exp.py -d 0-7 -b 4 -e 15 --sync_bn 8 --no-clearml --amp
-> test:
    python3 perceptron/exps/lane3d/bevcond3dlane_r50_896x512_exp.py -d 0-7 --eval --ckpt outputs/.../checkpoint_epoch_xx.pth
-> eval (need gpu-machine):
    python3 perceptron/data/lane3d/metric/eval.py -p outputs/.../evaluations/results.pkl -data car_two
-> vis_lane2d (in IPython):
    ```
    from data3d.datasets.lane2d_demo_manager import Lane2DDemoManager
    lane2d_demo_manager = Lane2DDemoManager()
    lane2d_demo_manager.run_demo("/data/",  "PrivateLane", "validation", sample_size=300, pred_path="outputs/.../evaluations/results.pkl")
    ```

results:
baseline:
car_two: 15e
+---------+----------------+-------------+------------------+-------------+-------------+-------------+
|  F1(%)  |  Precision(%)  |  Recall(%)  |  CD(m)/nGT/nDT   |  CD(m)/nTP  |  CD(m)/nFP  |  CD(m)/nFN  |
+=========+================+=============+==================+=============+=============+=============+
|  70.4   |      73.9      |    67.2     | 0.76/20447/18608 | 0.16/13745  | 2.06/4863  | 2.59/6702  |
+---------+----------------+-------------+------------------+-------------+-------------+-------------+
lane2dto3dv2: 10e
+---------+----------------+-------------+------------------+-------------+-------------+-------------+
|  F1(%)  |  Precision(%)  |  Recall(%)  |  CD(m)/nGT/nDT   |  CD(m)/nTP  |  CD(m)/nFP  |  CD(m)/nFN  |
+=========+================+=============+==================+=============+=============+=============+
|  77.2   |      80.6      |    74.1     | 0.70/71645/65873 | 0.15/53081  | 2.55/12792  | 3.00/18564  |
+---------+----------------+-------------+------------------+-------------+-------------+-------------+

"""
import os
import pickle as pkl
from typing import Sequence

import torch
from torch.optim import AdamW
from torch.optim.lr_scheduler import MultiStepLR
from torch.utils.data.distributed import DistributedSampler
from torchvision.transforms import Compose
from tqdm import tqdm

from perceptron.data.lane3d.dataset import Lane3DDataset
from perceptron.data.lane3d.transforms import Normalize, ToTensor
from perceptron.engine.callbacks import Callback
from perceptron.engine.cli.base_cli import BaseCli
from perceptron.engine.executors.base_executor import BaseExecutor
from perceptron.exps.base_exp import BaseExp
from perceptron.models.lane3d import BEVCond3DLane
from perceptron.layers.lr_scheduler import TorchLRSchedulerWraper
from perceptron.utils import torch_dist as dist


def get_param_groups(model, optimizer_setup):
    def match_name_keywords(n, name_keywords):
        out = False
        for b in name_keywords:
            if b in n:
                out = True
                break
        return out

    for n, p in model.named_parameters():
        if match_name_keywords(n, optimizer_setup["freeze_names"]):
            p.requires_grad = False

    param_groups = [
        {
            "params": [
                p
                for n, p in model.named_parameters()
                if not match_name_keywords(n, optimizer_setup["backb_names"])
                and not match_name_keywords(n, optimizer_setup["extra_names"])
                and p.requires_grad
            ],
            "lr": optimizer_setup["base_lr"],
            "wd": optimizer_setup["wd"],
        },
        {
            "params": [
                p
                for n, p in model.named_parameters()
                if match_name_keywords(n, optimizer_setup["backb_names"]) and p.requires_grad
            ],
            "lr": optimizer_setup["backb_lr"],
            "wd": optimizer_setup["wd"],
        },
        {
            "params": [
                p
                for n, p in model.named_parameters()
                if match_name_keywords(n, optimizer_setup["extra_names"]) and p.requires_grad
            ],
            "lr": optimizer_setup["extra_lr"],
            "wd": optimizer_setup["wd"],
        },
    ]

    return param_groups


class Lane3dEvaluator(BaseExecutor):
    def __init__(self, exp: BaseExp, callbacks: Sequence["Callback"], logger=None) -> None:
        super(Lane3dEvaluator, self).__init__(exp, callbacks, logger)
        self.output_dir = os.path.split(logger._core.handlers[1]._name)[0][1:]

    def eval(self):

        exp = self.exp
        local_rank = dist.get_rank()
        val_iter = iter(self.val_dataloader)

        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()

        all_results = []
        for _ in tqdm(range(len(self.val_dataloader))):
            batch_data = next(val_iter)
            with torch.no_grad():
                results = exp.test_step(batch_data)
            all_results += results

        dist.synchronize()
        all_results = dist.all_gather_object(all_results)
        all_results = sum(map(list, zip(*all_results)), [])[: exp.val_dataset_size]

        if local_rank == 0:
            evaluation_save_dir = os.path.join(self.output_dir, "evaluations")
            if not os.path.exists(evaluation_save_dir):
                os.makedirs(evaluation_save_dir)
            pkl.dump(all_results, open(os.path.join(evaluation_save_dir, "results.pkl"), "wb"))

            self.logger.info("Done with inference, start evaluation later!")

        self._invoke_callback("after_eval")


class ExpConfig:

    CLASS_NAMES = ["lane"]  # 目前只考虑车道线
    INPUT_SIZE = (896, 512)  # (w, h)
    IMG_SHAPE = (1920, 1080)  # (w, h)
    MAP_REGION = (60, 0, 15, -15)  # 地图范围 以ego为中心 前后左右的距离 单位m
    MAP_RESOLUTION = 0.15  # 分辨率 单位 m/pixel
    MAP_SIZE = (400, 200)  # canvas-size  pixel  # (h, w)   # 为了避免OOM，模型在更小的size上计算损失
    ida_aug_conf = {
        "final_dim": INPUT_SIZE[::-1],  # need h w
        "rot_lim": (-0.01, 0.01),
        "H": IMG_SHAPE[1],
        "W": IMG_SHAPE[0],
        "rand_flip": True,
        "color_jitter": False,
        "up_crop": False,
    }
    bda_aug_conf = {"rot_lim": (-0.01, 0.01), "scale_lim": (0.999, 1.001), "flip_dx_ratio": 0.0, "flip_dy_ratio": 0.0}

    dataset_setup = dict(
        dataset_name="lane2dto3dv2",  # car_two
        ida_aug_conf=ida_aug_conf,
        bda_aug_conf=bda_aug_conf,
        img_norm_cfg=dict(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], to_rgb=True),
        flip_ratio=0.5,
        direction="horizontal",
        size_divisor=128,
        is_nori_read=True,
        img_key_list=("camera_15",),  # camera_0_6
        img_shape_list=((1080, 1920),),
        class_names=CLASS_NAMES,
        view_key_list=("bev",),
        mask_conf=dict(bev=dict(map_range=MAP_REGION, map_resolution=MAP_RESOLUTION, dilate_ratio=-1)),
        sample_ratio=1,
        num_attributes=5,
        do_sparse=True,
        extra_keys=(),
        use_redis=True,
    )

    model_setup = dict(
        # im_backbone
        img_backbone=dict(
            type="ResNet",
            depth=50,
            frozen_stages=0,
            out_indices=[0, 1, 2, 3],
            norm_eval=False,
            init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
        ),
        img_neck=dict(
            type="SECONDFPN",
            in_channels=[256, 512, 1024, 2048],
            upsample_strides=[0.25, 0.5, 1, 2],
            out_channels=[128, 128, 128, 128],
        ),
        bev_encoder=dict(
            lss_conf=dict(
                grid_conf=dict(
                    xbound=[0, 60.0, 0.5], ybound=[-15.0, 15.0, 0.5], zbound=[-5.0, 5.0, 10.0], dbound=[4.0, 60.0, 1.0]
                ),
                input_shape=INPUT_SIZE,  # w,h
                d_model=512,
                camC=512,  # Cam feat dimension
                downsample=16,  # Downsample rate from input shape
            ),
            res_conf=dict(
                num_blocks=[2, 2, 2, 2],
                in_channels=512,
                base_channel=64,
                strides=[1, 1, 1, 1],
            ),
        ),
        bev_decoder=dict(
            in_channels=512,
            src_shape=(1, 120, 60),
            tgt_shape=(1, 20, 1),
            d_model=256,
            n_heads=8,
            num_encoder_layers=0,
            num_decoder_layers=4,
            dim_feedforward=1024,
            dropout=0.1,
            activation="relu",
            normalize_before=False,
            return_intermediate_dec=True,
            src_pos_encode="sine",
            tgt_pos_encode="learned",
            src_cam_encode=False,
            tgt_cam_encode=False,
            use_fix_encode=False,
            use_checkpoint=False,
        ),
        # output_head
        output_head=dict(
            in_channels=(512, 256),
            num_attributes=5,
            head_layers=1,
            disable_coords=True,
            branch_channels=256,
            loss_weights=dict(obj_weight=10.0, cls_weight=1.0, loc_weight=1.0, reg_weight=1.0, rng_weight=20.0),
            target_shape=MAP_SIZE,
            min_points=5,
            line_width=30,
            score_thresh=0.5,
            use_offset=True,
            map_range=(60, 0, 15, -15),
            map_resolution=0.15,
        ),
    )

    optimizer_setup = dict(
        freeze_names=[],
        base_lr=2e-4,
        wd=2e-4,
        backb_names=["backbone"],
        backb_lr=2e-4,
        extra_names=[],
        extra_lr=2e-4,
    )

    scheduler_setup = dict(
        milestones=[0.8, 1.0],
        gamma=0.1,
    )


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=1, total_devices=8, max_epoch=60, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)

        self.exp_config = ExpConfig()
        self.data_loader_workers = batch_size_per_device
        self.print_interval = 10
        self.dump_interval = 1
        self.eval_interval = 1
        self.seed = 0
        self.num_keep_latest_ckpt = 30
        self.ckpt_oss_save_dir = None
        self.enable_tensorboard = True
        self.eval_executor_class = Lane3dEvaluator

        milestones = self.exp_config.scheduler_setup["milestones"]
        self.exp_config.scheduler_setup["milestones"] = [int(x * max_epoch) for x in milestones]

        lr_ratio_dict = {32: 2, 16: 1.5, 8: 1.0, 4: 0.75, 2: 0.5, 1: 0.25}
        batch_size_lr_ratio = batch_size_per_device / 2
        assert total_devices in lr_ratio_dict, "Please set normal devices!"
        for k in ["base_lr", "backb_lr", "extra_lr"]:
            self.exp_config.optimizer_setup[k] *= lr_ratio_dict[total_devices] * batch_size_lr_ratio

    def _configure_model(self):
        model = BEVCond3DLane(self.exp_config.model_setup)
        return model

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        dataset_setup = self.exp_config.dataset_setup

        transform = Compose(
            [
                Normalize(**dataset_setup["img_norm_cfg"]),
                ToTensor(),
            ]
        )

        train_set = Lane3DDataset(
            transforms=transform,
            data_split="training",
            ida_aug_conf=dataset_setup["ida_aug_conf"],
            bda_aug_conf=dataset_setup["bda_aug_conf"],
            img_key_list=dataset_setup["img_key_list"],
            img_shape_list=dataset_setup["img_shape_list"],
            class_names=dataset_setup["class_names"],
            view_key_list=dataset_setup["view_key_list"],
            sample_ratio=dataset_setup["sample_ratio"],
            mask_conf=dataset_setup["mask_conf"],
            dataset_name=dataset_setup["dataset_name"],
            do_sparse=dataset_setup["do_sparse"],
            extra_keys=dataset_setup["extra_keys"],
            num_attributes=dataset_setup["num_attributes"],
            use_redis=dataset_setup["use_redis"],
        )

        if dist.is_distributed():
            sampler = InfiniteSampler(len(train_set), seed=self.seed if self.seed else 0)
        else:
            sampler = None

        train_loader = torch.utils.data.DataLoader(
            train_set,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=self.data_loader_workers,
            shuffle=sampler is None,
            drop_last=True,
            sampler=sampler,
        )
        self.train_dataset_size = len(train_set)
        return train_loader

    def _configure_val_dataloader(self):

        dataset_setup = self.exp_config.dataset_setup

        transform = Compose(
            [
                Normalize(**dataset_setup["img_norm_cfg"]),
                ToTensor(),
            ]
        )

        val_set = Lane3DDataset(
            transforms=transform,
            data_split="validation",
            ida_aug_conf=dataset_setup["ida_aug_conf"],
            bda_aug_conf=dataset_setup["bda_aug_conf"],
            img_key_list=dataset_setup["img_key_list"],
            img_shape_list=dataset_setup["img_shape_list"],
            class_names=dataset_setup["class_names"],
            view_key_list=dataset_setup["view_key_list"],
            sample_ratio=dataset_setup["sample_ratio"],
            mask_conf=dataset_setup["mask_conf"],
            dataset_name=dataset_setup["dataset_name"],
            do_sparse=dataset_setup["do_sparse"],
            extra_keys=dataset_setup["extra_keys"],
            num_attributes=dataset_setup["num_attributes"],
            use_redis=dataset_setup["use_redis"],
        )

        if dist.is_distributed():
            sampler = DistributedSampler(val_set, shuffle=False)
        else:
            sampler = None

        val_loader = torch.utils.data.DataLoader(
            val_set,
            batch_size=1,
            pin_memory=True,
            num_workers=self.data_loader_workers,
            shuffle=False,
            drop_last=False,
            sampler=sampler,
        )

        self.val_dataset_size = len(val_set)
        return val_loader

    def _configure_test_dataloader(self):
        pass

    def _configure_optimizer(self):
        optimizer_setup = self.exp_config.optimizer_setup
        optimizer = AdamW(get_param_groups(self.model, optimizer_setup))
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler_setup = self.exp_config.scheduler_setup
        torch_lr_scheduler = MultiStepLR(
            optimizer=self.optimizer,
            gamma=scheduler_setup["gamma"],
            milestones=scheduler_setup["milestones"],
        )
        scheduler = TorchLRSchedulerWraper(torch_lr_scheduler, len(self.train_dataloader), self.max_epoch)
        return scheduler

    def training_step(self, batch):
        for k in ["images", "ida_mats", "post_rot_bda"]:
            batch[k] = batch[k].float().cuda()
        losses_dict = self.model(batch)
        return losses_dict["losses"]

    def test_step(self, batch):
        with torch.no_grad():
            for k in ["images", "ida_mats", "post_rot_bda"]:
                batch[k] = batch[k].float().cuda()
            results = self.model(batch)["results"]
        tokens = batch["extra_infos"]["nori_id"]
        predictions = [{"nid": token, "bev_lanes": lane} for token, lane in zip(tokens, results["pts_pred"])]
        return predictions


if __name__ == "__main__":
    BaseCli(Exp).run()
