"""
-> train:
    python3 perceptron/exps/lane3d/bevcond3dlane_r50_896x512_idabda_exp.py -d 0-7 -b 2 -e 15 --sync_bn 8 --no-clearml
-> amp train:
    python3 perceptron/exps/lane3d/bevcond3dlane_r50_896x512_idabda_exp.py -d 0-7 -b 4 -e 15 --sync_bn 8 --no-clearml --amp
-> test:
    python3 perceptron/exps/lane3d/bevcond3dlane_r50_896x512_idabda_exp.py -d 0-7 --eval --ckpt outputs/.../checkpoint_epoch_xx.pth
-> eval (need gpu-machine):
    python3 perceptron/data/lane3d/metric/eval.py -p outputs/.../evaluations/results.pkl -data car_two
-> vis_lane2d (in IPython):
    ```
    from data3d.datasets.lane2d_demo_manager import Lane2DDemoManager
    lane2d_demo_manager = Lane2DDemoManager()
    lane2d_demo_manager.run_demo("/data/",  "PrivateLane", "validation", sample_size=300, pred_path=$(your_evaluation_dumped_vis_pickle))
    ```
"""

import torch
from perceptron.engine.cli.base_cli import BaseCli
from perceptron.exps.lane3d.bevcond3dlane_r50_896x512_exp import ExpConfig
from perceptron.exps.lane3d.bevcond3dlane_r50_896x512_exp import Exp as BaseExp


ExpConfig.IMG_SHAPE = (3840, 2160)
# 关闭flip, 打开rotate
ida_aug_conf = {
    "final_dim": ExpConfig.INPUT_SIZE[::-1],  # need h w
    "rot_lim": (-5.5, 5.5),
    "H": ExpConfig.IMG_SHAPE[1],
    "W": ExpConfig.IMG_SHAPE[0],
    "rand_flip": False,
    "color_jitter": False,
    "up_crop": False,
}

# 适配2号车
ExpConfig.MAP_RESOLUTION = (0.15, 0.15)
ExpConfig.MAP_SIZE = (400, 200)


ExpConfig.dataset_setup["dataset_name"] = "car_two_v2"
ExpConfig.dataset_setup["img_key_list"] = ("camera_0_6",)
ExpConfig.dataset_setup["img_shape_list"] = ((2160, 3840),)
ExpConfig.dataset_setup["ida_aug_conf"] = ida_aug_conf
ExpConfig.dataset_setup["do_sparse"] = False
ExpConfig.dataset_setup["view_key_list"] = ("bev", "rv")
ExpConfig.dataset_setup["ida_aug_conf"] = ida_aug_conf  #
ExpConfig.dataset_setup["mask_conf"] = dict(
    bev=dict(map_range=ExpConfig.MAP_REGION, map_resolution=ExpConfig.MAP_RESOLUTION, dilate_ratio=-1),
    rv=dict(dilate_ratio=3, down_scale=4),
)  #

ExpConfig.model_setup["rvseg_head"] = dict(feat_upsample=4, feat_dim=512, category_num=2)
ExpConfig.model_setup["output_head"]["loss_weights"] = dict(
    obj_weight=10.0, cls_weight=1.0, loc_weight=1.0, reg_weight=1.0, rng_weight=20.0, rv_seg_weights=0.0
)
ExpConfig.model_setup["output_head"]["target_shape"] = ExpConfig.MAP_SIZE
ExpConfig.model_setup["output_head"]["use_offset"] = False
ExpConfig.model_setup["output_head"]["map_resolution"] = ExpConfig.MAP_RESOLUTION


class Exp(BaseExp):
    def __init__(self, *args, **kwargs):
        super(Exp, self).__init__(*args, **kwargs)
        self.data_loader_workers = 4  # 至少需要300G内存

    def test_step(self, batch):
        with torch.no_grad():
            for k in ["images", "ida_mats", "post_rot_bda"]:
                batch[k] = batch[k].float().cuda()
            results = self.model(batch)["results"]
        tokens = batch["extra_infos"]["nori_id"]
        predictions = [
            {"nid": token, "bev_lanes": lane, "lane_scores": score}
            for token, lane, score in zip(tokens, results["pts_pred"], results["lane_scores"])
        ]

        return predictions


if __name__ == "__main__":
    BaseCli(Exp).run()
