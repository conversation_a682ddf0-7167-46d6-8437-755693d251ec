# This is an abstract cmt exp, which is not runnable.

import torch
import mmcv
import torch.nn as nn
from functools import partial
import torch.optim as optim
from perceptron.engine.cli import Det3DCli
from perceptron.exps.base_exp import BaseExp
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.engine.executors import Det3DEvaluator, Det3DInfer
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.utils.det3d_utils.common_utils import _load_data_to_gpu
from perceptron.utils.det3d_utils.initialize_utils import model_named_layers


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 1e-3
        self.print_interval = 50
        self.num_keep_latest_ckpt = 5
        self.dump_interval = 2
        self.eval_executor_class = Det3DEvaluator
        self.infer_executor_class = Det3DInfer
        self.lr_scale_factor = {"camera_encoder.img_backbone": 0.01, "camera_encoder.img_neck": 0.1}
        self.grad_clip_value = 35

        # 是否使用dpflow
        self.use_dpflow = True

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """

    def _configure_train_dataloader(self):
        raise NotImplementedError

    def _configure_val_dataloader(self):
        raise NotImplementedError

    def _configure_test_dataloader(self):
        raise NotImplementedError

    def _configure_model(self):
        # 是否要生成一个默认的、共享的model cfg，否则每次新增内容，都需要同步修改所有车的 model cfg.
        raise NotImplementedError

        # model = CMTPrivate(
        #     model_cfg=self.model_cfg,
        # )
        # return model

    def training_step(self, batch):
        # 支持dpflow的话，dataloader返回值必须为numpy
        for key, value in batch.items():
            if key in ["imgs", "points", "gt_boxes", "gt_labels", "radar_points", "roi_mask", "frame_id"]:
                batch[key] = value if isinstance(value, torch.Tensor) else torch.from_numpy(value).float()
            elif isinstance(value, dict):
                sub_dict = {}
                for subkey, subvalue in value.items():
                    sub_dict[subkey] = (
                        subvalue if isinstance(subvalue, torch.Tensor) else torch.from_numpy(subvalue).float()
                    )
                batch[key] = sub_dict
            elif key in ["runner_id", "worker_id", "radar_mode"]:  # dpflow 存储的属性
                pass

        if torch.cuda.is_available():
            _load_data_to_gpu(batch)

        if "points" in batch:
            points = [frame_point for frame_point in batch["points"]]
        else:
            points = None

        radar_points = batch.get("radar_points", None)
        imgs = batch.get("imgs", None)
        metas = batch.get("mats_dict", dict())
        roi_mask = batch.get("roi_mask", None)
        gt_boxes = batch["gt_boxes"]
        gt_labels = batch["gt_labels"]

        if "ida_mats" in metas:
            metas["ida_mats"] = metas["ida_mats"].unsqueeze(1)

        gt_labels += 1
        gt_boxes = torch.cat([gt_boxes, gt_labels.unsqueeze(dim=2)], dim=2)
        ret_dict, loss_dict, _ = self.model(
            lidar_points=points,
            cameras_imgs=imgs,
            radar_points=radar_points,
            roi_mask=roi_mask,
            metas=metas,
            gt_boxes=gt_boxes,
        )

        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)

        return loss, loss_dict

    @torch.no_grad()
    def test_step(self, batch):
        # 支持dpflow的话，dataloader返回值必须为numpy
        for key, value in batch.items():
            if key in ["imgs", "points", "gt_boxes", "gt_labels", "radar_points", "roi_mask", "frame_id"]:
                batch[key] = value if isinstance(value, torch.Tensor) else torch.from_numpy(value).float()
            elif isinstance(value, dict):
                sub_dict = {}
                for subkey, subvalue in value.items():
                    sub_dict[subkey] = (
                        subvalue if isinstance(subvalue, torch.Tensor) else torch.from_numpy(subvalue).float()
                    )
                batch[key] = sub_dict
            elif key in ["runner_id", "worker_id", "radar_mode"]:  # dpflow 存储的属性
                pass

        if torch.cuda.is_available():
            _load_data_to_gpu(batch)
        if "points" in batch:
            points = [frame_point for frame_point in batch["points"]]
        else:
            points = None
        imgs = batch.get("imgs", None)
        radar_points = batch.get("radar_points", None)
        metas = batch.get("mats_dict", dict())

        if "ida_mats" in metas:
            metas["ida_mats"] = metas["ida_mats"].unsqueeze(1)

        ret_dict = self.model(
            lidar_points=points,
            cameras_imgs=imgs,
            radar_points=radar_points,
            roi_mask=None,
            metas=metas,
            gt_boxes=None,
        )
        if getattr(self.model, "module", None):
            pred_dicts = self.model.module.det_head.dense_head.bbox_coder.decode(ret_dict)
        else:
            pred_dicts = self.model.det_head.dense_head.bbox_coder.decode(ret_dict)

        remap_pred_dicts = []
        for pred_dict in pred_dicts:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "bboxes":
                    remap_pred_dict["pred_boxes"] = v
                elif k == "labels":
                    remap_pred_dict["pred_" + k] = v
                else:
                    remap_pred_dict["pred_" + k] = v
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    def _configure_optimizer(self):
        layers_dict = model_named_layers(self.model)
        layer_groups = {name: [] for name, v in self.lr_scale_factor.items()}
        layer_groups.update({"others": []})
        for name, layer in layers_dict.items():
            exist = False
            for gallery_name in self.lr_scale_factor.keys():
                if gallery_name in name:
                    exist = True
                    break
            k = gallery_name if exist else "others"
            layer_groups[k].append(layer)

        lr_list = [v for k, v in self.lr_scale_factor.items()] + [1.0]
        lr_list = [self.lr * x for x in lr_list]

        optimizer_func = partial(optim.AdamW, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func,
            lr_list,
            [nn.Sequential(*v) for _, v in layer_groups.items()],
            wd=0.01,
            true_wd=True,
            bn_wd=True,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler

    def get_cfg_as_str(self) -> str:
        import functools
        from tabulate import tabulate
        import numpy as np

        config_table = []
        for c, v in self.__dict__.items():
            if isinstance(v, mmcv.Config):
                v = dict(v)
            if not isinstance(v, (int, float, str, list, tuple, dict, np.ndarray)):
                if hasattr(v, "__name__"):
                    v = v.__name__
                elif hasattr(v, "__class__"):
                    v = v.__class__
                elif type(v) == functools.partial:
                    v = v.func.__name__
            if c[0] == "_":
                c = c[1:]
            config_table.append((str(c), str(v)))

        headers = ["config key", "value"]
        config_table = tabulate(config_table, headers, tablefmt="plain")
        return config_table


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
