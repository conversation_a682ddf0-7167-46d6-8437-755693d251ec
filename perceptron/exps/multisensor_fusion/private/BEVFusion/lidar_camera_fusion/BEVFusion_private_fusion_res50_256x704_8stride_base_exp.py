import mmcv
import torch
import torch.optim as optim
import torch.nn as nn
from typing import Dict, Any
from torch.utils.data import DistributedSampler
from functools import partial
from abc import abstractmethod

from perceptron.exps.base_exp import BaseExp
from perceptron.utils import torch_dist as dist
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.engine.executors import Det3DEvaluator, Det3DInfer
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.utils.det3d_utils.common_utils import _load_data_to_gpu, fill_batch_tensor
from perceptron.layers.blocks_3d.mmdet3d.base_lss_fpn import LSSFPNPrivate
from perceptron.exps.multisensor_fusion.nuscenes.BEVFusion.BEVFusion_base import CameraEncoder
from perceptron.exps.multisensor_fusion.nuscenes.BEVFusion.BEVFusion_fusion_wo_iou import (
    BEVFusion as BaseBEVFusion,
)

from perceptron.data.det3d.private.private_multimodal import PrivateMultiModalData
from perceptron.exps.multisensor_fusion.private.data_base_cfg.base_pivate_fusion_cfg_wm34 import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)
from perceptron.exps.multisensor_fusion.private.BEVFusion.model_base_cfg.fusion_base_256x704_res50_8stride_woiou_cfg import (
    MODEL_CFG,
)


class CameraEncoderAdjustChannel(CameraEncoder):
    def __init__(self, camera_encoder_cfg: mmcv.Config, **kwargs) -> Any:
        super(CameraEncoder, self).__init__()
        if "extra_channel_adjust" in camera_encoder_cfg:
            extra_channel_adjust = camera_encoder_cfg.pop("extra_channel_adjust")
            self.adjust_channel = nn.Sequential(
                nn.Conv2d(extra_channel_adjust["in_channels"], extra_channel_adjust["out_channels"], 1, bias=False),
                nn.BatchNorm2d(extra_channel_adjust["out_channels"]),
                nn.ReLU(True),
            )
        self.backbone = LSSFPNPrivate(**camera_encoder_cfg)

    def forward(
        self,
        imgs: torch.tensor,
        mats_dict: Dict[str, torch.tensor],
        is_return_depth=False,
    ):
        feature_map = self.backbone(
            imgs,
            mats_dict,
            is_return_depth,
        )
        if hasattr(self, "adjust_channel"):
            feature_map = self.adjust_channel(feature_map)
        return feature_map


class BEVFusionCenterHead(BaseBEVFusion):
    def __init__(self, model_cfg):
        super().__init__(model_cfg)

    def _configure_camera_encoder(self):
        return CameraEncoderAdjustChannel(self.cfg.camera_encoder)


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 1e-3
        self.print_interval = 50
        self.num_keep_latest_ckpt = 20
        self.dump_interval = 1
        self.eval_executor_class = Det3DEvaluator
        self.infer_executor_class = Det3DInfer
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)
        self._change_cfg_params()

    @abstractmethod
    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        raise NotImplementedError("Must be rewrite by yourself!")

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_dataset = PrivateMultiModalData(
            **self.data_train_cfg,
        )
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            num_workers=8,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateMultiModalData.collate_fn,
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=True,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateMultiModalData(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateMultiModalData.collate_fn,
            num_workers=4,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        test_dataset = PrivateMultiModalData(
            **self.data_val_cfg,
        )
        test_loader = torch.utils.data.DataLoader(
            test_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateMultiModalData.collate_fn,
            num_workers=2,
            sampler=DistributedSampler(test_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return test_loader

    def _configure_model(self):
        model = BEVFusionCenterHead(
            model_cfg=self.model_cfg,
        )
        return model

    def training_step(self, batch):
        if torch.cuda.is_available():
            _load_data_to_gpu(batch)
        if "points" in batch:
            points = [frame_point for frame_point in batch["points"]]
        else:
            points = None
        images = batch.get("imgs", None)
        radar_points = batch.get("radar_points", None)
        metas = batch.get("mats_dict", None)
        gt_boxes = fill_batch_tensor(batch["gt_boxes"])
        gt_labels = fill_batch_tensor(batch["gt_labels"])

        gt_labels += 1
        gt_boxes = torch.cat([gt_boxes, gt_labels.unsqueeze(dim=2)], dim=2)
        ret_dict, tf_dict, _ = self.model(points, images, radar_points, metas, gt_boxes)
        loss = ret_dict["loss"].mean()
        if hasattr(self, "lr_dict"):
            lr_ext_dict = {pg["name"] + "_lr": pg["lr"] for pg in self.optimizer.param_groups}
            tf_dict.update(lr_ext_dict)

        if torch.isnan(loss) or torch.isinf(loss):  # for finding unknown nan loss problem
            torch.save(batch, "./outputs/model_nan_loss_input.pth")
            raise RuntimeError("Error: Nan or Inf loss while training, please check input!!!")

        return loss, tf_dict

    @torch.no_grad()
    def test_step(self, batch):
        if torch.cuda.is_available():
            _load_data_to_gpu(batch)
        if "points" in batch:
            points = [frame_point for frame_point in batch["points"]]
        else:
            points = None
        images = batch.get("imgs", None)
        radar_points = batch.get("radar_points", None)
        metas = batch.get("mats_dict", None)
        ret_dict = self.model(points, images, radar_points, metas, None)
        for result in ret_dict["pred_dicts"]:
            result["pred_labels"] -= 1
        return dict(pred_dicts=ret_dict["pred_dicts"])

    def _configure_optimizer(self):
        def children(m: torch.nn.Module):
            return list(m.children())

        def num_children(m: torch.nn.Module) -> int:
            return len(children(m))

        flatten_model = lambda m: sum(map(flatten_model, m.children()), []) if num_children(m) else [m]  # noqa
        get_layer_groups = lambda m: [torch.nn.Sequential(*flatten_model(m))]  # noqa

        optimizer_func = partial(optim.Adam, betas=(0.9, 0.99), eps=1e-3)
        optimizer = OptimWrapper.create(
            optimizer_func, self.lr, get_layer_groups(self.model), wd=0.01, true_wd=True, bn_wd=True
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler

    def get_cfg_as_str(self) -> str:
        import functools
        from tabulate import tabulate
        import numpy as np

        config_table = []
        for c, v in self.__dict__.items():
            if isinstance(v, mmcv.Config):
                v = dict(v)
            if not isinstance(v, (int, float, str, list, tuple, dict, np.ndarray)):
                if hasattr(v, "__name__"):
                    v = v.__name__
                elif hasattr(v, "__class__"):
                    v = v.__class__
                elif type(v) == functools.partial:
                    v = v.func.__name__
            if c[0] == "_":
                c = c[1:]
            config_table.append((str(c), str(v)))

        headers = ["config key", "value"]
        config_table = tabulate(config_table, headers, tablefmt="plain")
        return config_table
