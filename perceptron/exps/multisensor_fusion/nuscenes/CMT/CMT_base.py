import mmcv
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DistributedSampler
from functools import partial
import numpy as np

from perceptron.exps.base_exp import BaseExp
from perceptron.utils import torch_dist as dist
from perceptron.utils.det3d_utils.initialize_utils import model_named_layers
from perceptron.engine.executors import Det3DEvaluator
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.data.det3d.public import NuScenesDataset
from perceptron.exps.multisensor_fusion.nuscenes.CMT.__base__.base_nuscenes_cfg import DATA_CFG, CLASS_NAMES
from perceptron.exps.multisensor_fusion.nuscenes.CMT.__base__.base_model_cfg import MODEL_CFG
from perceptron.utils.det3d_utils.common_utils import _load_data_to_gpu


from perceptron.models.multisensor_fusion.cmt.base import CMT


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 1e-3
        self.print_interval = 50
        self.num_keep_latest_ckpt = 20
        self.dump_interval = 1
        self.eval_executor_class = Det3DEvaluator
        self.lr_scale_factor = {"camera_encoder.img_backbone": 0.01, "camera_encoder.img_neck": 0.1}
        self.grad_clip_value = 35
        self.data_cfg = mmcv.Config(DATA_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)
        self.class_names = CLASS_NAMES

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg temporarily. For those should be inherited, please change them in __init__
        """

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_dataset = NuScenesDataset(
            **self.data_cfg["train"],
        )
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            num_workers=4,
            drop_last=False,
            shuffle=False,
            collate_fn=NuScenesDataset.collate_fn,
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = NuScenesDataset(
            **self.data_cfg["val"],
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=NuScenesDataset.collate_fn,
            num_workers=4,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        raise NotImplementedError

    def _configure_model(self):
        model = CMT(
            model_cfg=self.model_cfg,
        )
        return model

    def training_step(self, batch):
        if torch.cuda.is_available():
            _load_data_to_gpu(batch)
        if "points" in batch:
            points = [frame_point for frame_point in batch["points"]]
        else:
            points = None
        imgs = batch.get("imgs", None)
        metas = batch.get("mats_dict", dict())
        gt_boxes = batch["gt_boxes"]
        gt_labels = batch["gt_labels"]
        if "ida_mats" in metas:
            metas["ida_mats"] = metas["ida_mats"].unsqueeze(1)
        gt_labels += 1
        gt_boxes = torch.cat([gt_boxes, gt_labels.unsqueeze(dim=2)], dim=2)
        ret_dict, loss_dict, _ = self.model(points, imgs, None, metas, gt_boxes)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)

        return loss, loss_dict

    @torch.no_grad()
    def test_step(self, batch):
        if torch.cuda.is_available():
            _load_data_to_gpu(batch)
        if "points" in batch:
            points = [frame_point for frame_point in batch["points"]]
        else:
            points = None
        imgs = batch.get("imgs", None)
        metas = batch.get("mats_dict", dict())
        if "ida_mats" in metas:
            metas["ida_mats"] = metas["ida_mats"].unsqueeze(1)
        pred_dicts = self.model(points, imgs, None, metas, None)
        remap_pred_dicts = []
        for pred_dict in pred_dicts:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "bboxes":
                    remap_pred_dict["pred_boxes"] = v
                elif k == "labels":
                    remap_pred_dict["pred_" + k] = v
                else:
                    remap_pred_dict["pred_" + k] = v
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    def _configure_optimizer(self):
        layers_dict = model_named_layers(self.model)
        layer_groups = {name: [] for name, v in self.lr_scale_factor.items()}
        layer_groups.update({"others": []})
        for name, layer in layers_dict.items():
            exist = False
            for gallery_name in self.lr_scale_factor.keys():
                if gallery_name in name:
                    exist = True
                    break
            k = gallery_name if exist else "others"
            layer_groups[k].append(layer)

        lr_list = [v for k, v in self.lr_scale_factor.items()] + [1.0]
        lr_list = [self.lr * x for x in lr_list]

        optimizer_func = partial(optim.AdamW, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func,
            lr_list,
            [nn.Sequential(*v) for _, v in layer_groups.items()],
            wd=0.01,
            true_wd=True,
            bn_wd=True,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler

    def get_cfg_as_str(self) -> str:
        import functools
        from tabulate import tabulate

        config_table = []
        for c, v in self.__dict__.items():
            if isinstance(v, mmcv.Config):
                v = dict(v)
            if not isinstance(v, (int, float, str, list, tuple, dict, np.ndarray)):
                if hasattr(v, "__name__"):
                    v = v.__name__
                elif hasattr(v, "__class__"):
                    v = v.__class__
                elif type(v) == functools.partial:
                    v = v.func.__name__
            if c[0] == "_":
                c = c[1:]
            config_table.append((str(c), str(v)))

        headers = ["config key", "value"]
        config_table = tabulate(config_table, headers, tablefmt="plain")
        return config_table
