"""
changelog:
copied from perceptron/exps/end2end/private/model_cfg/det_map_model_cfg_7v5r_wmap_200m_2necks.py (branch map_city_20240912), modified map_head config
"""

from perceptron.exps.end2end.private.maptracker.data_cfg.maptracker_annos_hf import class_names
from perceptron.exps.end2end.private.maptracker.data_cfg.maptracker_annos_hf import arrow_name_id_map

CLASS_NAMES = class_names
_POINT_CLOUD_RANGE = [-15.2, -80.0, -5.0, 15.2, 204.8, 3.0]
_VOXEL02_VOXEL_SIZE = [0.2, 0.2, 0.2]
_VOXEL02_GRID_SIZE = [
    (_POINT_CLOUD_RANGE[3] - _POINT_CLOUD_RANGE[0]) / _VOXEL02_VOXEL_SIZE[0],
    (_POINT_CLOUD_RANGE[4] - _POINT_CLOUD_RANGE[1]) / _VOXEL02_VOXEL_SIZE[1],
    (_POINT_CLOUD_RANGE[5] - _POINT_CLOUD_RANGE[2]) / _VOXEL02_VOXEL_SIZE[2],
]
_VOXEL02_OUT_SIZE_FACTOR = 8
_POST_CENTER_RANGE = [
    _POINT_CLOUD_RANGE[0] - 10,
    _POINT_CLOUD_RANGE[1] - 10,
    -10,
    _POINT_CLOUD_RANGE[3] + 10,
    _POINT_CLOUD_RANGE[4] + 10,
    10,
]

VOXEL02_DET_HEAD = dict(
    type="CMTE2EHead",
    in_channels=512,
    num_query=900,
    modal=["Camera", "Radar"],
    depth_num=64,
    hidden_dim=256,
    downsample_scale=_VOXEL02_OUT_SIZE_FACTOR,
    grid_size=_VOXEL02_GRID_SIZE,
    # check
    use_dn=False,
    common_heads=dict(center=(2, 2), height=(1, 2), dim=(3, 2), rot=(2, 2), vel=(2, 2)),
    tasks=[
        dict(
            num_class=len(CLASS_NAMES),
            class_names=CLASS_NAMES,
        ),
    ],
    bbox_coder=dict(
        type="MultiTaskBBoxCoder",
        post_center_range=_POST_CENTER_RANGE,
        pc_range=_POINT_CLOUD_RANGE,
        max_num=300,
        voxel_size=_VOXEL02_VOXEL_SIZE,
        num_classes=len(CLASS_NAMES),
        score_threshold=0.1,
    ),
    separate_head=dict(type="SeparateTaskHead", init_bias=-2.19, final_kernel=3),
    transformer=dict(
        type="CMTTransformer",
        view_num=7,  # 这个参数务必需要与实际view数目保持一致 check
        decoder=dict(
            type="PETRTransformerDecoder",
            return_intermediate=True,
            num_layers=6,
            transformerlayers=dict(
                type="PETRTransformerDecoderLayer",
                with_cp=False,
                attn_cfgs=[
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                    dict(type="PETRMultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                ],
                ffn_cfgs=dict(
                    type="FFN",
                    embed_dims=256,
                    feedforward_channels=1024,
                    num_fcs=2,
                    ffn_drop=0.0,
                    act_cfg=dict(type="ReLU", inplace=True),
                ),
                feedforward_channels=1024,  # unused
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
    loss_cls=dict(type="FocalLoss", use_sigmoid=True, gamma=2, alpha=0.25, reduction="mean", loss_weight=2.0),
    loss_bbox=dict(type="L1Loss", reduction="mean", loss_weight=0.25),
    loss_heatmap=dict(type="GaussianFocalLoss", reduction="mean", loss_weight=1.0),
    use_roi_mask=False,
)

VOXEL02_TRAIN_CFG = dict(
    pts=dict(
        dataset="Private",
        assigner=dict(
            type="HungarianAssigner3D",
            cls_cost=dict(type="FocalLossCost", weight=2.0),
            reg_cost=dict(type="BBox3DL1Cost", weight=0.25),
            iou_cost=dict(type="IoUCost", weight=0.0),  # Fake cost. This is just to make it compatible with DETR head.
            pc_range=_POINT_CLOUD_RANGE,
        ),
        pos_weight=-1,
        gaussian_overlap=0.1,
        min_radius=2,
        grid_size=_VOXEL02_GRID_SIZE,  # [x_len, y_len, 1]
        voxel_size=_VOXEL02_VOXEL_SIZE,
        out_size_factor=_VOXEL02_OUT_SIZE_FACTOR,
        code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2],
        point_cloud_range=_POINT_CLOUD_RANGE,
    )
)

VOXEL01_TEST_CFG = dict(
    pts=dict(
        dataset="Private",
        grid_size=_VOXEL02_GRID_SIZE,
        out_size_factor=_VOXEL02_OUT_SIZE_FACTOR,
        pc_range=_POINT_CLOUD_RANGE[0:2],
        voxel_size=_VOXEL02_VOXEL_SIZE[:2],
        nms_type=None,
    )
)

CAMERA_ENCODER_CFG = dict(
    img_backbone=dict(
        type="RepVGGplus",
        num_blocks=[8, 14, 24, 1],
        width_multiplier=[2.5, 2.5, 2.5, 5],
        num_classes=1000,  # ignore
        # override_groups_map=None,
        use_post_se=True,
        use_checkpoint=False,
        pretrained="s3://zqt/pretrained/RepVGGplus-L2pse-train256-acc84.06.pth"
    ),
    img_neck=dict(
        type="SECONDFPN",
        in_channels=[160, 320, 640, 2560],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[32, 32, 64, 128],
    ),
    img_neck_map=dict(
        type="SECONDFPN",
        in_channels=[160, 320, 640, 2560],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[32, 32, 64, 128],
    ),
)

bev_h = 100
bev_w = 60
num_cams= 4

# maptrv2
fixed_ptsnum_per_pred_line = 20
fixed_ptsnum_per_gt_line = 20
line_num_points = 20
_dim_ = 256
aux_seg_cfg = dict(
    use_aux_seg=True,
    bev_seg=True,
    pv_seg=False,
    seg_classes=1,
    feat_down_sample=32,
    pv_thickness=1,
)
z_cfg = dict(
    pred_z_flag=True,
    gt_z_flag=True,
)
num_vec = 50
_pos_dim_ = _dim_//2
_ffn_dim_ = _dim_*2
voxel_size = 0.5
_num_levels_ = 1

permute = True
# num_classes = 7 # 第一版分为 7 类，laneline / curb / stopline / crosswalk / arrow / nomarking / entrance
num_classes = 3  # maptrv2
in_channels = 256  # neck 出来的 feat dim
pc_range = [-30., 0., -6., 30., 100, 6] # 和 dataset 的 map_lidar_range 一致，影响前后帧转换时 norm/denorm
MAP_HEAD = dict(
    type='MapTRv2',
    bev_h=bev_h,
    bev_w=bev_w,
    pc_range=pc_range,
    use_memory=False,
    head_cfg=dict(
        type='MapDetectorMapTRv2Head',
        bev_h=bev_h,
        bev_w=bev_w,
        num_query=900,
        num_vec_one2one=50,
        num_vec_one2many=300,
        k_one2many=6,
        num_classes=num_classes,
        attr_nums = 3,
        num_lane_color_attr=3,
        num_lane_dash_attr=7,
        num_curb_attr=3,
        line_num_points=line_num_points,
        num_pts_per_vec=fixed_ptsnum_per_pred_line, # one bbox
        num_pts_per_gt_vec=fixed_ptsnum_per_gt_line,
        dir_interval=1,
        query_embed_type='instance_pts',
        transform_method='minmax',
        gt_shift_pts_pattern='v2',

        in_channels=_dim_,
        sync_cls_avg_factor=True,
        with_box_refine=True,
        as_two_stage=False,
        code_size=2,
        code_weights=[1.0, 1.0, 1.0, 1.0],
        aux_seg=aux_seg_cfg,
        z_cfg=z_cfg,
        transformer=dict(
            type='MapTRPerceptionTransformer',
            num_cams=num_cams,  # fix: custom
            z_cfg=z_cfg,  # fix: 3d
            rotate_prev_bev=True,
            use_shift=True,
            use_can_bus=True,
            embed_dims=_dim_,
            encoder=dict(
                type='BEVFormerEncoder',
                num_layers=2,
                pc_range=pc_range,  # custom
                num_points_in_pillar=4,
                return_intermediate=False,
                transformerlayers=dict(
                    type='BEVFormerLayer',
                    attn_cfgs=[
                        dict(
                            type='TemporalSelfAttention',
                            embed_dims=_dim_,
                            num_levels=1),
                        dict(
                            type='SpatialCrossAttention',
                            pc_range=pc_range,  # custom
                            deformable_attention=dict(
                                type='MSDeformableAttention3D',
                                embed_dims=_dim_,
                                num_points=8,
                                num_levels=_num_levels_),
                            embed_dims=_dim_,
                            num_cams=num_cams,  # fix for private
                        )
                    ],
                    feedforward_channels=_ffn_dim_,
                    ffn_dropout=0.1,
                    operation_order=('self_attn', 'norm', 'cross_attn', 'norm',
                                     'ffn', 'norm'))),
            decoder=dict(
                type='MapTRDecoder',
                num_layers=6,
                return_intermediate=True,
                transformerlayers=dict(
                    type='DecoupledDetrTransformerDecoderLayer',
                    num_vec=num_vec,
                    num_pts_per_vec=fixed_ptsnum_per_pred_line,
                    attn_cfgs=[
                        dict(
                            type='MultiheadAttention',
                            embed_dims=_dim_,
                            num_heads=8,
                            dropout=0.1),
                        dict(
                            type='MultiheadAttention',
                            embed_dims=_dim_,
                            num_heads=8,
                            dropout=0.1),
                         dict(
                            type='CustomMSDeformableAttention',
                            embed_dims=_dim_,
                            num_levels=1),
                    ],

                    feedforward_channels=_ffn_dim_,
                    ffn_dropout=0.1,
                    operation_order=('self_attn', 'norm', 'self_attn', 'norm','cross_attn', 'norm',
                                     'ffn', 'norm')))),
        bbox_coder=dict(
            type='MapTRNMSFreeCoder',
            z_cfg=z_cfg,
            # post_center_range=[-61.2, -61.2, -10.0, 61.2, 61.2, 10.0],
            post_center_range=[-20, -35, -20, -35, 20, 35, 20, 35],
            pc_range=pc_range,  # maptrv2
            max_num=50,
            voxel_size=voxel_size,
            num_classes=num_classes),
            
        positional_encoding=dict(
            type='LearnedPositionalEncoding',
            num_feats=_pos_dim_,
            row_num_embed=bev_h,  # maptrv2
            col_num_embed=bev_w,  # maptrv2
            ),
        loss_cls=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=2.0),
        loss_line_reg=dict(
            type='LinesL1Loss',
            loss_weight=100.0,
            beta=0.0,
        ),
        loss_lane_color_attr=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=0.1
        ),
        loss_lane_dash_attr=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=0.1
        ),
        loss_curb_attr=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=0.1
        ),
        loss_bbox=dict(type='L1Loss', loss_weight=0.0),
        loss_iou=dict(type='GIoULoss', loss_weight=0.0),
        loss_pts=dict(type='PtsL1Loss', 
                      loss_weight=5.0),
        loss_dir=dict(type='PtsDirCosLoss', loss_weight=0.005),
        loss_seg=dict(type='SimpleLoss', 
            pos_weight=4.0,
            loss_weight=1.0),
        loss_pv_seg=dict(type='SimpleLoss', 
                    pos_weight=1.0,
                    loss_weight=2.0),
        assigner = dict(
            type='HungarianLinesAssigner',
            cost=dict(
                type='MapQueriesCost',
                cls_cost=dict(type='FocalLossCost', weight=2.0),
                reg_cost=dict(type='LinesL1Cost', weight=100.0, beta=0.01, permute=True),
                ),
        ),
        # model training and testing settings
        train_cfg=dict(
            grid_size=[512, 512, 1],
            voxel_size=voxel_size,
            point_cloud_range=pc_range,
            out_size_factor=4,
            assigner=dict(
                type='MapTRAssigner',
                z_cfg=z_cfg,
                cls_cost=dict(type='FocalLossCost', weight=2.0),
                reg_cost=dict(type='BBoxL1Cost', weight=0.0, box_format='xywh'),
                # reg_cost=dict(type='BBox3DL1Cost', weight=0.25),
                # iou_cost=dict(type='IoUCost', weight=1.0), # Fake cost. This is just to make it compatible with DETR head.
                iou_cost=dict(type='IoUCost', iou_mode='giou', weight=0.0),
                pts_cost=dict(type='OrderedPtsL1Cost', 
                        weight=5),
                pc_range=pc_range))
    ),
)

MODEL_CFG = dict(
    num_query=900,
    num_classes=len(CLASS_NAMES),
    tracking=False,
    grid_mask=True,
    train_backbone=True,
    class_names=CLASS_NAMES,
    camera_encoder=CAMERA_ENCODER_CFG,
    det_head=VOXEL02_DET_HEAD,
    train_cfg=VOXEL02_TRAIN_CFG,
    test_cfg=VOXEL01_TEST_CFG,
    map_head=MAP_HEAD,
)