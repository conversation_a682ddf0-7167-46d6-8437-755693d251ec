""" 
# H20多机训练
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 2 --cpu=100 --gpu=8 --memory=600000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
 --custom-resources rdma/mlnx_shared=8 --positive-tags=H20 \
  --mount=gpfs://gpfs1/acceldata:/mnt/acceldata --mount=gpfs://gpfs1/static:/mnt/static \
 -n mach-static --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- \
python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse.py --no-clearml -b 10 --sync_bn 1 -e 40 --find_unused_parameters \
    --pretrained_model s3://zqt/exp-bak/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_nowarp_pretrain_30ep_fasterm/2025-03-13T19:23:28/dump_model/checkpoint_epoch_27.pth


# A800多机训练
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 8 --cpu=80 --gpu=8 --memory=600000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
 --custom-resources=rdma/mlnx_shared=0 --positive-tags=A800 \
 --mount=gpfs://gpfs1/acceldata:/mnt/acceldata --mount=gpfs://gpfs1/static:/mnt/static \
 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp --mount=gpfs://gpfs1/caiqianxi:/mnt/caiqianxi -- \
python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse.py --no-clearml -b 10 --sync_bn 1 -e 24 --find_unused_parameters \
    --pretrained_model s3://zqt/exp-bak/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_nowarp_pretrain_30ep_fasterm/2025-03-13T19:23:28/dump_model/checkpoint_epoch_27.pth

# 单机推理
rlaunch --gpu=8 --cpu=80 --memory=400000 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp --positive-tags=H20 -- \
python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse.py --no-clearml -b 1 \
 --ckpt xxx.pth --eval_map

python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse.py --no-clearml -b 1 \
 --ckpt /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_ft_swan/2025-04-08T12:02:07/dump_model/checkpoint_epoch_3.pth --eval_map \
  --valdataset jira-15396


python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix.py --no-clearml -b 1 \
 --ckpt /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse/2025-04-11T23:25:37/dump_model/checkpoint_epoch_15.pth --eval_map \
  --valdataset jira-15396



# 左右转bmk
python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse.py --no-clearml -b 1 \
 --ckpt /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan/2025-04-09T01:56:17/dump_model/checkpoint_epoch_15.pth --eval_map \
 --valdataset valset-v20250321-left

python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse.py --no-clearml -b 1 \
 --ckpt /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan/2025-04-09T01:56:17/dump_model/checkpoint_epoch_15.pth --eval_map \
 --valdataset valset-v20250321-right

python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse.py --no-clearml -b 1 \
 --ckpt /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan/2025-04-09T01:56:17/dump_model/checkpoint_epoch_15.pth --eval_map \
 --valdataset valset-v20250321-lanechange

python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse.py --no-clearml -b 1 \
 --ckpt /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan/2025-04-09T01:56:17/dump_model/checkpoint_epoch_15.pth --eval_map \
 --valdataset valset-v20250321-forward



# 左右转bmk
python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse.py  --no-clearml -b 1 \
 --ckpt /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse/2025-04-11T23:25:37/dump_model/checkpoint_epoch_15.pth --eval_map \
 --valdataset valset-v20250321-left

python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse.py  --no-clearml -b 1 \
 --ckpt /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse/2025-04-11T23:25:37/dump_model/checkpoint_epoch_15.pth --eval_map \
 --valdataset valset-v20250321-right

python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse.py  --no-clearml -b 1 \
 --ckpt /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse/2025-04-11T23:25:37/dump_model/checkpoint_epoch_15.pth --eval_map \
 --valdataset valset-v20250321-lanechange

python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse.py  --no-clearml -b 1 \
 --ckpt /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_noreverse/2025-04-11T23:25:37/dump_model/checkpoint_epoch_15.pth --eval_map \
 --valdataset valset-v20250321-forward





"""
import os
import sys
import refile
import torch
import mmcv
import copy
import numpy as np
import torch.nn as nn
import torch.optim as optim
from functools import partial
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.layers.lr_scheduler import OnecycleLRScheduler, WarmCosineLRScheduler
from perceptron.utils.det3d_utils.initialize_utils import model_named_layers
from perceptron.layers.lr_scheduler import WarmCosineLRScheduler
from perceptron.exps.end2end.private.maptrv2.model_cfg.det_map_model_cfg_7v5r_wmap_100m_2necks_maptrv2_fasternetm import (
    MODEL_CFG,
)
from perceptron.exps.base_exp import BaseExp
from perceptron.exps.end2end.private.maptrv2.data_cfg.maptrv2_annos_z10_4v0r import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
    val_dataset_cfg_map as DATA_VAL_CFG_MAP,
)

from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset

from perceptron.data.sampler import InfiniteIntervalSampler, DistributedSampler, InfiniteSampler, ProportionalDatasetSampler
from perceptron.data.det3d.modules.radar.radar_hf_virtual_aug import HFRadarVirtualAug
from perceptron.models.end2end.perceptron.perceptron_maptrv2 import VisionEncoder

from perceptron.engine.executors.inference.e2e_inference import End2endVisualizator
from perceptron.data.det3d.modules import (
    MultiFrameImageAffineTransformationWarp,
)
import copy
from torch.utils.data import DistributedSampler
import bisect
import json
from refile import smart_path_join, smart_listdir, smart_exists, smart_open

class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]

        # 1. Training setting
        self.lr = 6e-4  # official maptr
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 5
        self.dump_interval = 2
        self.lr_scale_factor = {}  # 当加载pretrain的时候，可以将lr_scale_factor设置为该形式，training from scratch的时候，设置为{}.
        self.grad_clip_value = 35
        self.infer_executor_class = End2endVisualizator

        # 2. Dataset and model configuration
        map_ego_range = [0, -30, -6, 100, 30, 6]        # ego [xmin, ymin, zmin, xmax, ymax, zmax]
        map_lidar_range = [-30., 0., -6., 30., 100, 6]

        DATA_TRAIN_CFG['annotation']['maptracker']['map_point_cloud_range'] = map_ego_range
        DATA_TRAIN_CFG['annotation']['maptracker']['map_lidar_range'] = map_lidar_range
        DATA_VAL_CFG['annotation']['maptracker']['map_point_cloud_range'] = map_ego_range
        DATA_VAL_CFG['annotation']['maptracker']['map_lidar_range'] = map_lidar_range
        DATA_VAL_CFG['evaluator']['map_range'] = map_lidar_range
        
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)
    
        # 2.1 change
        self.data_train_cfg["loader"]["datasets_names"] = ["new-v20250408"]
        self.data_train_cfg["image"][
            "cam120_scale"
        ] = 1.3  # codebase去畸变..
        self.data_train_cfg["gpfs_prefix"] = "/mnt/caiqianxi/"

        self.data_train_cfg["annotation"]["box"]["label_key"] = "pre_labels"
        self.data_train_cfg["annotation"]["box"]["occlusion_threshold"] = -1
        self.data_train_cfg["annotation"]["maptracker"]["warp_tgt_json"] = None
        self.data_train_cfg["annotation"]["maptracker"]["remove_reverse"] = True  # update 04.11, 过滤逆向车道线!
        self.data_train_cfg["loader"]["only_key_frame"] = False
        
        # change lidar / radar cfg, we do not use them
        self.data_train_cfg["radar"]["with_virtual_radar"] = False  # 设成 True 的时候，需要 "gt_boxes", map 里没有
        self.data_train_cfg["lidar"]["lidar_names"] = []  # 不用 lidar 点云 
        self.data_train_cfg['sensor_names']["lidar_names"] = []   # 不能直接 pop, 因为有关 lidar 的标定是在 get_lidar 时读进来的
        self.data_train_cfg['sensor_names'].pop("radar_names")

        self.data_train_cfg["image"][
            "img_warp_maps_dict"
        ] = None
        self.data_train_cfg["loader"]["interval"] = 1

        
        # 2.1 map val config
        self.data_val_cfg_map = copy.deepcopy(self.data_train_cfg)
        self.data_val_cfg_map.mode = "val"
        self.data_val_cfg_map["loader"]["datasets_names"] = ["map_z02_bmk"]
        self.data_val_cfg_map["evaluator"] = DATA_VAL_CFG_MAP["evaluator"]
        self.data_val_cfg_map["evaluator"]["warp_tgt_json"] = None
        self.data_val_cfg_map["evaluator"]["tgt_resolution"] = [1920, 1080]
        
        self.vis_interval = -1
        self.vis_maptrv2_interval = 500 # 500

        # 3. other configuration change in this function
        if "ckpt" in kwargs:
            self.ckpt_path = kwargs["ckpt"]
        else:
            self.ckpt_path = None
        if "valdataset" in kwargs:
            self.valdataset = kwargs["valdataset"]
        else:
            self.valdataset = None
        self._change_cfg_params()
        self.val_vis_cfg()


    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        # model
        self.model_cfg.pop("det_head")
        self.model_cfg["map_head"]["head_cfg"]["num_curb_attr"] = 4  # 增加导流区线属性

        # 去除filter 逻辑
        self.data_train_cfg["annotation"]["maptracker"]["filter_start"] = 80  # 80m内作为起点的车道线全部保留, 为了提前检出大路口线
        self.data_train_cfg["annotation"]["maptracker"]["except_dist"] = 30  # 60m内的短线全部保留, 为了提前检出
        self.data_val_cfg_map["annotation"]["maptracker"]["filter_start"] = 80  # 80m内作为起点的车道线全部保留
        self.data_val_cfg_map["annotation"]["maptracker"]["except_dist"] = 30  # 60m内的短线全部保留

        # val
        if self.valdataset is not None:
            self.data_val_cfg_map["loader"]["datasets_names"] = [self.valdataset]
        
        # self.data_val_cfg_map["loader"]["datasets_names"] = ["jira0318-z18-part2"]
        # self.data_val_cfg_map["loader"]["datasets_names"] = ["jira0321"]

        # val
        self.data_val_cfg_map["image"][
            "cam120_scale"
        ] = 1.3  # codebase去畸变..
        self.data_val_cfg_map["gpfs_prefix"] = "/mnt/caiqianxi/"


        # # old-val
        # self.data_val_cfg_map["image"][
        #     "cam120_scale"
        # ] = 1.0  # codebase去畸变..
        # self.data_val_cfg_map["gpfs_prefix"] = "/mnt/tf-rhea-data-bpp/"


        # # val-jira
        # self.data_val_cfg_map["image"][
        #     "cam120_scale"
        # ] = 1.0  # codebase去畸变..
        # self.data_val_cfg_map["gpfs_prefix"] = "/mnt/tf-rhea-data-bpp/"
        # self.data_val_cfg_map["data_mode"] = "jpg"
        # self.data_val_cfg_map["save_rgb"] = False
    
    def val_vis_cfg(self):
        if self.ckpt_path:
            # ckpt_dir = os.path.dirname(self.ckpt_path)
            # ckpt_name = os.path.basename(self.ckpt_path)
            # self.data_val_cfg_map["evaluator"]["val_output_dir"] = "{}/{}_{}".format(ckpt_dir, ckpt_name, self.data_val_cfg_map["loader"]["datasets_names"][0])

            raw_model_name = self.ckpt_path.split("/")[-1].split(".")[0]
            self.data_val_cfg_map["evaluator"]["val_output_dir"] = "{}-{}-thr{}".format(raw_model_name, self.data_val_cfg_map["loader"]["datasets_names"][0], self.data_val_cfg_map["annotation"]["maptracker"]["map_class_dict"]["laneline"]["dt_scores_th"])
    

    def _configure_model(self):
        model = VisionEncoder(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):
        train_dataset1 = PrivateE2EDataset(
            **self.data_train_cfg,
        )

        train_dataset2_cfg = copy.deepcopy(self.data_train_cfg)
        train_dataset2_cfg["loader"]["datasets_names"] = ["new-qy-v20250408"]
        train_dataset2_cfg["loader"]["interval"] = 1
        train_dataset2_cfg["gpfs_prefix"] = "/mnt/acceldata/static/"
        train_dataset2_cfg["data_mode"] = "qyjpg"  # 后续理论上qy所有数据都是这种形式
        train_dataset2 = PrivateE2EDataset(
            **train_dataset2_cfg,
        )

        vip_left_cfg = copy.deepcopy(self.data_train_cfg)
        vip_left_cfg["loader"]["datasets_names"] = ["left-v20250408-nori"]
        vip_left_cfg["loader"]["interval"] = 1
        train_dataset_vip_left = PrivateE2EDataset(
            **vip_left_cfg,
        )
        
        vip_right_cfg = copy.deepcopy(self.data_train_cfg)
        vip_right_cfg["loader"]["datasets_names"] = ["right-v20250408-nori"]
        vip_right_cfg["loader"]["interval"] = 1
        train_dataset_vip_right = PrivateE2EDataset(
            **vip_right_cfg,
        )

        jpg_vip_left_cfg = copy.deepcopy(self.data_train_cfg)
        jpg_vip_left_cfg["loader"]["datasets_names"] = ["left-v20250408-jpg"]
        jpg_vip_left_cfg["loader"]["interval"] = 1
        jpg_vip_left_cfg["gpfs_prefix"] = "/mnt/acceldata/static/"
        jpg_vip_left_cfg["data_mode"] = "qyjpg"  # 后续理论上qy所有数据都是这种形式
        train_dataset_jpg_vip_left = PrivateE2EDataset(
            **jpg_vip_left_cfg,
        )
        
        jpg_vip_right_cfg = copy.deepcopy(self.data_train_cfg)
        jpg_vip_right_cfg["loader"]["datasets_names"] = ["right-v20250408-jpg"]
        jpg_vip_right_cfg["loader"]["interval"] = 1
        jpg_vip_right_cfg["gpfs_prefix"] = "/mnt/acceldata/static/"
        jpg_vip_right_cfg["data_mode"] = "qyjpg"  # 后续理论上qy所有数据都是这种形式
        train_dataset_jpg_vip_right = PrivateE2EDataset(
            **jpg_vip_right_cfg,
        )

        vip_swan_cfg = copy.deepcopy(self.data_train_cfg)
        vip_swan_cfg["loader"]["datasets_names"] = ["swan-301"]
        vip_swan_cfg["loader"]["interval"] = 1
        vip_swan_cfg["gpfs_prefix"] = "/mnt/acceldata/static/"
        vip_swan_cfg["data_mode"] = "qyjpg"  # 后续理论上qy所有数据都是这种形式
        train_dataset_vip_swan = PrivateE2EDataset(
            **vip_swan_cfg,
        )
        vip_divline_cfg = copy.deepcopy(self.data_train_cfg)
        vip_divline_cfg["loader"]["datasets_names"] = ["divline-v20250410"]
        vip_divline_cfg["loader"]["interval"] = 1
        vip_divline_cfg["gpfs_prefix"] = "/mnt/acceldata/static/"
        vip_divline_cfg["data_mode"] = "qyjpg"  # 后续理论上qy所有数据都是这种形式
        train_dataset_vip_divline = PrivateE2EDataset(
            **vip_divline_cfg,
        )


        train_dataset = torch.utils.data.ConcatDataset([train_dataset1, train_dataset2, train_dataset_vip_left, train_dataset_vip_right, train_dataset_jpg_vip_left, train_dataset_jpg_vip_right, train_dataset_vip_swan, train_dataset_vip_divline])
        # train_dataset = train_dataset1
        train_dataset.batch_postcollate_fn = train_dataset1.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset1.batch_preforward_fn
        
        # define resampler
        sampler = ProportionalDatasetSampler(
            datasets=[train_dataset1, train_dataset2, train_dataset_vip_left, train_dataset_vip_right, train_dataset_jpg_vip_left, train_dataset_jpg_vip_right, train_dataset_vip_swan, train_dataset_vip_divline],
            ratios=[0.3, 0.1, 0.15, 0.15, 0.05, 0.05, 0.15, 0.05],
            total_samples=200000,  # 每个epoch采样20w个, 对应双机2000步
            shuffle=True,
            seed=42
        )
        
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            # sampler=InfiniteIntervalSampler(len(train_dataset), shuffle=True, interval=10) if dist.is_distributed() else None, 
            # sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0),
            sampler=sampler,
            pin_memory=True,
            num_workers=6,    # set to 0 when debug
        )
        return train_loader

    def _configure_val_dataloader(self):
        from torch.utils.data import DistributedSampler
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg_map,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=5,
            # sampler=InfiniteIntervalSampler(len(val_dataset), shuffle=False, interval=5) if dist.is_distributed() else None, 
            sampler=DistributedSampler(val_dataset, shuffle=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg_map,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=2,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None
        ret_dict, loss_dict, _ = self.model(**batch)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)
        return loss, loss_dict, ret_dict

    @torch.no_grad()
    def test_step(self, batch):
        pred_dicts_det, pred_maps = self.model(**batch)
        remap_pred_dicts = self.process_det_outs(pred_dicts_det)

        # fake items
        pred_maps[0]["stop_scores"] = np.zeros((50,))
        pred_maps[0]["stop_labels"] = (np.ones((50,)) * 2).astype(np.int64)
        pred_maps[0]["box_scores"] = np.zeros((100,))
        pred_maps[0]["box_labels"] = (np.ones((100,)) * 5).astype(np.int64)
        pred_maps[0]["entrance_scores"] = np.zeros((50,))
        pred_maps[0]["entrance_labels"] = (np.ones((50,)) * 6).astype(np.int64)
        pred_maps[0]["stops"] = np.zeros((50, 4, 3))
        pred_maps[0]["boxes"] = np.zeros((100, 4, 3))
        pred_maps[0]["entrances"] = np.zeros((50, 4, 3)).astype(np.int32)
        # pred_maps[0]["attrs_lines"] = np.zeros((50, 20, 6)).astype(np.int32)  # hardcode: 50
        pred_maps[0]["attrs_boxes"] = np.zeros((100, 4, 6)).astype(np.int32)

        index = batch["index_in_dataset"][0][0]
        if "s3_path" in self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_120"]:
            cam120_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_120"]["s3_path"].split("/")[-1].split(".jpg")[0]
            cam30_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_30"]["s3_path"].split("/")[-1].split(".jpg")[0]
            camleft100_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_left_100"]["s3_path"].split("/")[-1].split(".jpg")[0]
            camright100_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_right_100"]["s3_path"].split("/")[-1].split(".jpg")[0]
        else:
            cam120_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_120"]["timestamp"]
            cam30_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_30"]["timestamp"]
            camleft100_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_left_100"]["timestamp"]
            camright100_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_right_100"]["timestamp"]
        json_collections = self.val_dataloader.dataset.loader_output["json_collection"]
        json_idx = bisect.bisect_right(self.val_dataloader.dataset.loader_output["frame_data_list"].cumulative_sizes, index)
        json_path = self.val_dataloader.dataset.loader_output["json_collection"][json_idx]

        line_scores = pred_maps[0]["line_scores"]
        lines = pred_maps[0]["lines"]
        line_roi_index = np.where(line_scores > 0.3)
        line_roi_scores = line_scores[line_roi_index]  # (N, )
        lines_roi = lines[line_roi_index]  # (N, 20, 3)

        json_res = dict(
            cam120_ts=cam120_ts,
            cam30_ts=cam30_ts,
            camleft100_ts=camleft100_ts,
            camright100_ts=camright100_ts,
            line_roi_index=line_roi_index[0].tolist(),
            line_roi_scores=line_roi_scores.tolist(),
            line_roi=lines_roi.tolist(),
            json_path=json_path
        )

        # save_path = json_path.replace("s3://zqt/", "s3://zqt/debug/jira13541/").replace(".json", "_{}.json".format(cam120_ts))
        # json.dump(json_res, smart_open(save_path, "w"))

        pred_maps[0]["json_res"] = json_res

        return dict(pred_dicts=remap_pred_dicts, pred_maps=pred_maps)  # remap

    @torch.no_grad()
    def test_step_with_loss(self, batch):
        (pred_dicts_det, pred_maps), loss_dict = self.model(**batch)
        remap_pred_dicts = self.process_det_outs(pred_dicts_det)
        return dict(pred_dicts=remap_pred_dicts, pred_maps=pred_maps, loss_dict=loss_dict)  # remap
    
    def process_det_outs(self, pred_dicts_det):
        if pred_dicts_det is None:
            return []
        remap_pred_dicts = []
        for pred_dict in pred_dicts_det:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "bboxes":
                    remap_pred_dict["pred_boxes"] = v
                elif k == "labels":
                    remap_pred_dict["pred_" + k] = v
                else:
                    remap_pred_dict["pred_" + k] = v
            if True:  # nms
                from perceptron.data.det3d.modules.utils.post_process import StandardNMSPostProcess

                boxes3d = remap_pred_dict["pred_boxes"]
                top_scores = remap_pred_dict["pred_scores"]
                if top_scores.shape[0] != 0:
                    if not self.use_ray_nms:
                        selected = StandardNMSPostProcess._nms_gpu_3d(
                            boxes3d[:, :7],
                            top_scores,
                            thresh=0.8,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    else:
                        selected = StandardNMSPostProcess._ray_nms(
                            boxes3d[:, :7].cpu().numpy(),
                            top_scores.cpu().numpy(),
                            thresh=5.0,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    remap_pred_dict["pred_boxes"] = remap_pred_dict["pred_boxes"][selected]
                    remap_pred_dict["pred_scores"] = remap_pred_dict["pred_scores"][selected]
                    remap_pred_dict["pred_labels"] = remap_pred_dict["pred_labels"][selected]
            remap_pred_dicts.append(remap_pred_dict)
        return remap_pred_dicts

    def _configure_optimizer(self):
        from torch.optim import AdamW
        img_backbone_params = []
        other_params = []
        for name, param in self.model.named_parameters():
            if 'img_backbone' in name:
                img_backbone_params.append(param)
            else:
                other_params.append(param)

        optimizer = AdamW([
            {'params': img_backbone_params, 'lr': self.lr*0.1, 'weight_decay': 0.01},
            {'params': other_params, 'lr': self.lr, 'weight_decay': 0.01}
        ])
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = WarmCosineLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            warmup_epochs=0.5,
            warmup_lr_start=1.0/3*self.lr,
            end_lr=1e-6  # eta_min
        )
        return scheduler

if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
