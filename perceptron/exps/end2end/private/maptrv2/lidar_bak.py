""" HF9: 1v0r, maptracker train on test, single frame
Description:  Trained with bmk 6k dataset

brainpp Cmd: DATA3D_REDIS_URL="redis://mcd-mc-redis-web-main-d-r-p.mcd-mc:6666/0" DET3D_EXPID=20881 RLAUNCH_REPLICA_TOTAL=2 RLAUNCH_REPLICA=0 python3 perceptron/exps/end2end/private/maptracker/maptracker_exp_hf_1v0r_single_18-6w-8worker.py -b 8 -e 30 --no-clearml --sync_bn 1

volc Cmd:
sudo apt-get install ninja-build
cd /data/proj/dingwenjie/workspace_dwj/Perceptron-maptracker
export PYTHONPATH=$(pwd)
export TORCH_DISTRIBUTED_DEBUG=INFO
/data/proj/meijie/miniconda/envs/perceptron_poetry_torch23/bin/pip3 install -e . --user
mkdir /root/.aws && cp /data/proj/meijie/dotfile/credentials /root/.aws/credentials
/data/proj/meijie/miniconda/envs/perceptron_poetry_torch23/bin/python perceptron/exps/end2end/private/maptracker/maptracker_exp_hf_1v0r_single_trainontest_10ep.py --no-clearml -b 24 -e 10 --sync_bn 1 --env volc

Test Cmd: python3 perceptron/exps/end2end/private/admap/admap_private_base_exp_hf_1v0r_300w_sampler.py --no-clearml -b 4 -e 80 --sync_bn 1 --eval_map

cd /data/Perceptron-e2e/ && conda deactivate && conda activate perceptron_poetry_torch23

DET3D_EXPID=6675220 RLAUNCH_REPLICA_TOTAL=4 RLAUNCH_REPLICA=3 \
    python3 perceptron/exps/end2end/private/maptracker/maptracker_exp_hf_4v0r_single_cityhighway_plqs_g1_v6_br_vr_sesplit.py \
    --no-clearml -d 0-7 -b 1 --sync_bn 1 -e 6 --find_unused_parameters

***** stepmind ******
# 20250122发版模型 s3://zqt/admap/cjz-share/tmp_mt/0122model_vol_g1_v7_br_vr_ses_q400/checkpoint_epoch_29_q400_0121_jira.pth

# 单机1卡调试
rlaunch --cpu=70 --gpu=1 --memory=100000 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- zsh
# 单机8卡调试
rlaunch --cpu=170 --gpu=8 --memory=800000 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- zsh

# 单机8卡实验
rlaunch --cpu=170 --gpu=8 --memory=800000 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- \
python3 perceptron/exps/end2end/private/maptracker/maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain.py --no-clearml -b 6 --sync_bn 1 -e 60

rlaunch --cpu=70 --gpu=6 --memory=400000 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- \
python3 perceptron/exps/end2end/private/maptracker/maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain.py --no-clearml -b 6 --sync_bn 1 -e 60

rlaunch --cpu=40 --gpu=6 --memory=400000 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp --positive-tags=H20 -- \
python3 perceptron/exps/end2end/private/maptracker/maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain.py --no-clearml -b 6 --sync_bn 1 -e 60


# 多机多卡实验
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 2 --cpu=100 --gpu=8 --memory=600000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
 --custom-resources rdma/mlnx_shared=8 \
 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- \
python3 perceptron/exps/end2end/private/maptracker/maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain.py -b 10 -e 60 --sync_bn 1 --no-clearml

# 临时2机6卡
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 2 --cpu=40 --gpu=6 --memory=400000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
 --custom-resources rdma/mlnx_shared=8 --positive-tags=H20 \
 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- \
python3 perceptron/exps/end2end/private/maptracker/maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain.py -b 8 -e 60 --sync_bn 1 --no-clearml


# 单机8卡测试
rlaunch --cpu=70 --gpu=8 --memory=200000 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- \
  python3 perceptron/exps/end2end/private/maptracker/maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain.py --no-clearml -b 1 \
  --ckpt xxxx.pth --eval_map

# 测试发版模型
rlaunch --cpu=70 --gpu=1 --memory=100000 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- \
    python3 perceptron/exps/end2end/private/maptracker/maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain.py --no-clearml -b 1 \
    --ckpt s3://zqt/admap/cjz-share/tmp_mt/0122model_vol_g1_v7_br_vr_ses_q400/checkpoint_epoch_29_q400_0121_jira.pth --eval_map


# 测试lidar-tiny 0211
python3 perceptron/exps/end2end/private/maptracker/maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain.py --no-clearml -b 1 \
    --ckpt /data/Perceptron/outputs/maptracker__maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain/2025-02-10T23:02:52/dump_model/checkpoint_epoch_2.pth --eval_map

# prelabel lidar 700w预训练
rlaunch --cpu=150 --gpu=8 --memory=800000 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp \
 -- python3 perceptron/exps/end2end/private/maptracker/maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain.py -b 10 -e 60 --sync_bn 1 --no-clearml 



DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 2 --cpu=100 --gpu=8 --memory=600000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
 --custom-resources rdma/mlnx_shared=8 --positive-tags=H20 \
 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- \
python3 perceptron/exps/end2end/private/maptracker/maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain.py -b 10 -e 60 --sync_bn 1 --no-clearml \
 --pretrained_model /data/Perceptron/outputs/maptracker__maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain/2025-02-16T20:18:54/dump_model/checkpoint_epoch_2.pth


# 0218测试1l预标数据模型baseline
rlaunch --cpu=70 --gpu=1 --memory=100000 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp --positive-tags=H20 -- \
    python3 perceptron/exps/end2end/private/maptracker/maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain.py --no-clearml -b 1 \
    --ckpt /data/Perceptron/outputs/maptracker__maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_4w_pretrain/2025-02-17T15:36:07/dump_model/checkpoint_epoch_21.pth --eval_map



"""
import sys
import refile
import torch
import mmcv
import copy
import torch.nn as nn
import torch.optim as optim
from functools import partial
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.utils.det3d_utils.initialize_utils import model_named_layers

from perceptron.exps.end2end.private.maptracker.model_cfg.det_map_model_cfg_0v5r1l_wmap_200m_2necks_sq_sa_pl_qs_ses import (
    MODEL_CFG,
)
from perceptron.exps.base_exp import BaseExp
from perceptron.exps.end2end.private.maptracker.data_cfg.maptracker_annos_z10_0v0r1l_sq_sa_pl_qs_ses import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
    val_dataset_cfg_map as DATA_VAL_CFG_MAP,
)

from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset

from perceptron.data.sampler import InfiniteIntervalSampler
from perceptron.data.det3d.modules.radar.radar_hf_virtual_aug import HFRadarVirtualAug
from perceptron.models.end2end.perceptron.perceptron_maptracker import VisionEncoder

from perceptron.engine.executors.inference.e2e_inference import End2endVisualizator
from perceptron.data.det3d.modules import (
    MultiFrameImageAffineTransformationWarp,
)
import copy
from torch.utils.data import DistributedSampler

class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]

        # 1. Training setting
        self.lr = 1e-3  #
        self.init_scale = 512
        self.print_interval = 1
        self.num_keep_latest_ckpt = 3
        self.dump_interval = 1
        self.lr_scale_factor = {}  # 当加载pretrain的时候，可以将lr_scale_factor设置为该形式，training from scratch的时候，设置为{}.
        self.grad_clip_value = 35
        self.infer_executor_class = End2endVisualizator

        # 2. Dataset and model configuration
        map_ego_range = [0, -30, -6, 100, 30, 6]        # ego [xmin, ymin, zmin, xmax, ymax, zmax]
        map_lidar_range = [-30., 0., -6., 30., 100, 6]
        
        MODEL_CFG['map_head']['head_cfg']['query_group'] = 1
        MODEL_CFG['map_head']['head_cfg']['pc_range'] = map_lidar_range

        DATA_TRAIN_CFG['annotation']['maptracker']['map_point_cloud_range'] = map_ego_range
        DATA_TRAIN_CFG['annotation']['maptracker']['map_lidar_range'] = map_lidar_range
        DATA_VAL_CFG['annotation']['maptracker']['map_point_cloud_range'] = map_ego_range
        DATA_VAL_CFG['annotation']['maptracker']['map_lidar_range'] = map_lidar_range
        DATA_VAL_CFG['evaluator']['map_range'] = map_lidar_range
        
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)
    
        # 3. other configuration change in this function
        self._change_cfg_params()
        
    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        # model
        self.model_cfg.pop("det_head")
        self.model_cfg.pop("camera_encoder")

        # data-train
        self.data_train_cfg["loader"]["datasets_names"] = ["zqt_prelabel_4w1_filtered"]
        self.data_train_cfg["annotation"]["box"]["label_key"] = "pre_labels"
        self.data_train_cfg["annotation"]["box"]["occlusion_threshold"] = -1
        self.data_train_cfg["annotation"]["maptracker"]["warp_tgt_json"] = None  # "s3://tf-rhea-data-bpp/e2e_cdx_highway_test/prelabel-highway-test/car_15/ppl_bag_20241003_110641_det/v1_241015_220142/0027/clean_e2e_map_prelabel_result/0027.json"
        self.data_train_cfg["loader"]["only_key_frame"] = False
        
        # change lidar / radar cfg, we do not use them
        self.data_train_cfg["radar"]["with_virtual_radar"] = False  # 设成 True 的时候，需要 "gt_boxes", map 里没有
        # self.data_train_cfg["lidar"]["lidar_names"] = []  # 不用 lidar 点云 
        # self.data_train_cfg['sensor_names']["lidar_names"] = []   # 不能直接 pop, 因为有关 lidar 的标定是在 get_lidar 时读进来的
        # self.data_train_cfg['sensor_names'].pop("radar_names")
        
        # change warp matrix cfg
        # self.data_train_cfg["pipeline"]["ida_aug"]["type"] = MultiFrameImageAffineTransformationWarp
        # self.data_train_cfg["pipeline"]["ida_aug"][
        #     "target_extrinsic"
        # ] = "/data/Perceptron/outputs/dwj_ckpt/20241115_release/target_extrinsic_all.npz"
        # self.data_train_cfg["pipeline"]["ida_aug"][
        #     "map_aug"
        # ] = {
        #         "theta_x": [-0.0, 0.0],  # pitch
        #         "theta_y": [-0.0, 0.0], # roll
        #         "theta_z": [-3.0, 3.0], # yaw
        #         "px": [-0, 0.], # 模拟颠簸
        #         "py": [-0., 0.],
        #         "pz": [-0., 0.],
        #     }
        # self.data_train_cfg["pipeline"]["ida_aug"][
        #     "map_lidar_range"
        # ] = self.data_train_cfg["annotation"]["maptracker"]["map_lidar_range"]

        self.data_train_cfg["image"][
            "img_warp_maps_dict"
        ] = None # "1115_release/img_warp_maps_dict_all_15.npz"
        self.data_train_cfg["loader"]["interval"] = 1

        # data-eval
        self.data_val_cfg_map = copy.deepcopy(self.data_train_cfg)
        self.data_val_cfg_map.mode = "val"
        self.data_val_cfg_map["loader"]["datasets_names"] = ["z10-v2-0-train-lidar-clip15"] # ["testontrain_turn_21"]  # ["z10-v2-0-train-lidar-tiny"] # 1225_RL_BMK 1130_turn_rl 1129_city_bmk ["map20w_val_mf"] #["car15_tmp_test_top5"] # ["map_highway_rawimg"] # 
        self.data_val_cfg_map["evaluator"] = DATA_VAL_CFG_MAP["evaluator"]
        self.data_val_cfg_map["evaluator"]["warp_tgt_json"] = None  # "s3://tf-rhea-data-bpp/e2e_cdx_highway_test/prelabel-highway-test/car_15/ppl_bag_20241003_110641_det/v1_241015_220142/0027/clean_e2e_map_prelabel_result/0027.json"  # 需要是目标车辆对应的json, 只会用到 calibrated_sensors 字段，其它字段不用管
        self.data_val_cfg_map["evaluator"]["tgt_resolution"] = [1920, 1080]
        self.data_val_cfg_map["loader"]["interval"] = 10

        # vis-cfg
        self.vis_interval = 1000  # train阶段的可视化间隔

    def _configure_model(self):
        model = VisionEncoder(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):
        train_dataset1 = PrivateE2EDataset(
            **self.data_train_cfg,
        )

        # train_dataset1_1 = PrivateE2EDataset(
        #     **self.data_train_cfg_city40w,
        # )

        # train_dataset2 = PrivateE2EDataset(
        #     **self.data_train_cfg_cityturn,
        # )

        # train_dataset3 = PrivateE2EDataset(
        #     **self.data_train_cfg_increment,
        # )

        # train_dataset4 = PrivateE2EDataset(
        #     **self.data_train_cfg_safeisland,
        # )

        # train_dataset5 = PrivateE2EDataset(
        #     **self.data_train_cfg_realturn,
        # )

        # train_dataset6 = PrivateE2EDataset(
        #     **self.data_train_cfg_realturn1,
        # )

        # train_dataset7 = PrivateE2EDataset(
        #     **self.data_train_cfg_highway,
        # )
        
        # train_dataset = torch.utils.data.ConcatDataset([train_dataset1, train_dataset2, train_dataset3, train_dataset4, train_dataset5, train_dataset6, train_dataset7])
        train_dataset = train_dataset1
        # train_dataset = torch.utils.data.ConcatDataset([train_dataset1, train_dataset1_1])
        train_dataset.batch_postcollate_fn = train_dataset1.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset1.batch_preforward_fn
        
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            # sampler=DistributedSampler(train_dataset) if dist.is_distributed() else None,
            sampler=InfiniteIntervalSampler(len(train_dataset), shuffle=True, interval=10) if dist.is_distributed() else None, 
            pin_memory=True,
            num_workers=6    # set to 0 when debug
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg_map,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=5,
            # sampler=InfiniteIntervalSampler(len(val_dataset), shuffle=False, interval=10) if dist.is_distributed() else None, 
            sampler=DistributedSampler(val_dataset, shuffle=False) if dist.is_distributed() else None,
            # sampler=DistributedSampler(val_dataset, shuffle=True) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg_map,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=2,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None
        ret_dict, loss_dict, _ = self.model(**batch)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)
        return loss, loss_dict, ret_dict

    @torch.no_grad()
    def test_step(self, batch):
        pred_dicts_det, pred_maps = self.model(**batch)
        remap_pred_dicts = self.process_det_outs(pred_dicts_det)
        return dict(pred_dicts=remap_pred_dicts, pred_maps=pred_maps)  # remap

    @torch.no_grad()
    def test_step_with_loss(self, batch):
        (pred_dicts_det, pred_maps), loss_dict = self.model(**batch)
        remap_pred_dicts = self.process_det_outs(pred_dicts_det)
        return dict(pred_dicts=remap_pred_dicts, pred_maps=pred_maps, loss_dict=loss_dict)  # remap
    
    def process_det_outs(self, pred_dicts_det):
        if pred_dicts_det is None:
            return []
        remap_pred_dicts = []
        for pred_dict in pred_dicts_det:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "bboxes":
                    remap_pred_dict["pred_boxes"] = v
                elif k == "labels":
                    remap_pred_dict["pred_" + k] = v
                else:
                    remap_pred_dict["pred_" + k] = v
            if True:  # nms
                from perceptron.data.det3d.modules.utils.post_process import StandardNMSPostProcess

                boxes3d = remap_pred_dict["pred_boxes"]
                top_scores = remap_pred_dict["pred_scores"]
                if top_scores.shape[0] != 0:
                    if not self.use_ray_nms:
                        selected = StandardNMSPostProcess._nms_gpu_3d(
                            boxes3d[:, :7],
                            top_scores,
                            thresh=0.8,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    else:
                        selected = StandardNMSPostProcess._ray_nms(
                            boxes3d[:, :7].cpu().numpy(),
                            top_scores.cpu().numpy(),
                            thresh=5.0,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    remap_pred_dict["pred_boxes"] = remap_pred_dict["pred_boxes"][selected]
                    remap_pred_dict["pred_scores"] = remap_pred_dict["pred_scores"][selected]
                    remap_pred_dict["pred_labels"] = remap_pred_dict["pred_labels"][selected]
            remap_pred_dicts.append(remap_pred_dict)
        return remap_pred_dicts

    def _configure_optimizer(self):
        layers_dict = model_named_layers(self.model)
        layer_groups = {name: [] for name, v in self.lr_scale_factor.items()}
        layer_groups.update({"others": []})
        for name, layer in layers_dict.items():
            exist = False
            for gallery_name in self.lr_scale_factor.keys():
                if gallery_name in name:
                    exist = True
                    break
            k = gallery_name if exist else "others"
            layer_groups[k].append(layer)

        lr_list = [v for k, v in self.lr_scale_factor.items()] + [1.0]
        lr_list = [self.lr * x for x in lr_list]

        optimizer_func = partial(optim.AdamW, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func,
            lr_list,
            [nn.Sequential(*v) for _, v in layer_groups.items()],
            wd=0.01,
            true_wd=True,
            bn_wd=True,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.1,
        )
        return scheduler

if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
