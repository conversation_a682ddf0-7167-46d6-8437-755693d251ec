""" HF9: 7V5R
Description:  Trained with prelabeled data shared with map
Cmd: DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --max-wait-duration=72h -P 4  --cpu=48 --gpu=8 --memory=300000 --private-machine yes --preemptible no -- python3 perceptron/exps/end2end/private/track/track_private_base_exp_hf_7v5r.py --no-clearml -b 1 -e 24 --amp --sync_bn 4 --find_unused_parameters --pretrained_model s3://gongjiahao-share/end2end/HF/exp/det_private_base_exp_hf_7v5r_q300_40+40e/dump_model/checkpoint_epoch_39_new_codebase.pth
Log+Ckpt: s3://gongjiahao-share/end2end/HF/exp/e2e_private_base_exp_hf_7v5r_far_track11.5w_q300/
Author: GJH
Data: 2024-05-09
"""
import os
import sys
import refile
import torch
import mmcv
import copy
import torch.nn as nn
import torch.optim as optim
from functools import partial
from perceptron.engine.cli import End2endCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.utils.det3d_utils.initialize_utils import model_named_layers

from perceptron.exps.end2end.private.model_cfg.track_model_cfg_7v5r import MODEL_CFG
from perceptron.exps.base_exp import BaseExp
from perceptron.exps.end2end.private.data_cfg.track_annos_hf import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)

from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import GroupEachSampleInBatchSampler, InfiniteIntervalSampler
from perceptron.data.det3d.modules.radar.radar_hf_virtual_aug import HFRadarVirtualAug
from perceptron.models.end2end.perceptron import VisionEncoder


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.cfg_path = os.path.basename(os.path.abspath(__file__))
        self.lr = 1e-3 * 0.2
        self.init_scale = 2
        self.print_interval = 50
        self.num_keep_latest_ckpt = 5
        self.dump_interval = 2
        self.lr_scale_factor = {
            "camera_encoder.img_backbone": 0.01,
            "camera_encoder.img_neck": 0.1,
        }  # 当加载pretrain的时候，可以将lr_scale_factor设置为该形式，training from scratch的时候，设置为{}.
        self.grad_clip_value = 35

        # 2. Dataset and model configuration
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)

        # 3. other configuration change in this function
        self._change_cfg_params()
        # self.data_train_cfg["pipeline"]["bda_aug"]["aug_conf"]["flip_dy_ratio"] = 0.0
        self.data_train_cfg["loader"]["datasets_names"] = ["HF_e2e_labeled_861"]
        # self.data_train_cfg["annotation"]["box"]["label_key"] = "pre_labels"
        self.data_train_cfg["annotation"]["box"]["occlusion_threshold"] = -1

        self.data_train_cfg_bad_radar = copy.deepcopy(self.data_train_cfg)
        self.data_train_cfg_bad_radar["loader"]["datasets_names"] = ["HF_e2e_labeled_bad_radar"]
        # self.data_train_cfg["annotation"]["box"]["label_key"] = "pre_labels"
        self.data_train_cfg_bad_radar["radar"]["type"] = HFRadarVirtualAug
        self.data_train_cfg_bad_radar["radar"]["with_virtual_radar"] = True
        self.data_train_cfg_bad_radar["annotation"]["box"]["occlusion_threshold"] = -1

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        self.model_cfg.det_head.bbox_coder.score_threshold = 0.0
        self.model_cfg.num_query = 300
        self.model_cfg["det_head"]["init_radar_num_query"] = 0
        self.model_cfg["det_head"]["num_query"] = 0 + 300
        self.model_cfg["det_head"]["modal"] = ["Camera", "Radar"]

        self.model_cfg["radar_encoder"] = dict(out_channels=[11, 256, 256])
        self.model_cfg["embed_dims"] = 256

        self.model_cfg.det_head.transformer.decoder.transformerlayers.attn_cfgs[1]["type"] = "PETRMultiheadAttention"
        self.model_cfg.tracking_module.hist_temporal_transformer.decoder.transformerlayers.attn_cfgs[1][
            "type"
        ] = "PETRMultiheadAttention"
        self.model_cfg.tracking_module.spatial_transformer.decoder.transformerlayers.attn_cfgs[1][
            "type"
        ] = "PETRMultiheadAttention"
        self.model_cfg.tracking_module.fut_temporal_transformer.decoder.transformerlayers.attn_cfgs[1][
            "type"
        ] = "PETRMultiheadAttention"
        self.model_cfg["det_head"]["transformer"]["decoder"]["num_layers"] = 3
        self.model_cfg["det_head"]["separate_head"]["final_kernel"] = 1
        self.model_cfg.runtime_tracker = dict(
            output_threshold=0.1,
            score_threshold=0.4,
            record_threshold=0.4,
            max_age_since_update=7,
            drop_probability=0.2,
            fp_ratio=0.2,
            hist_fp_num=3,
        )
        self.data_train_cfg["annotation"]["box"]["occlusion_threshold"] = -1
        self.data_val_cfg["loader"]["datasets_names"] = ["bmk_new_withOCC"]

    def _configure_model(self):
        model = VisionEncoder(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):
        # train_dataset1 = PrivateE2EDataset(
        #     convert_fp32=False,
        #     **self.data_train_cfg,
        # )
        # train_dataset2 = PrivateE2EDataset(**self.data_train_cfg_bad_radar)
        # train_dataset = torch.utils.data.ConcatDataset([train_dataset1, train_dataset2])
        # train_dataset.batch_postcollate_fn = train_dataset1.batch_postcollate_fn
        # train_dataset.batch_preforward_fn = train_dataset1.batch_preforward_fn
        train_dataset = PrivateE2EDataset(
            convert_fp32=False,
            **self.data_train_cfg,
        )

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            sampler=InfiniteIntervalSampler(len(train_dataset), seed=self.seed if self.seed else 0, interval=20)
            if dist.is_distributed()
            else None,
            pin_memory=True,
            num_workers=5,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_dataset.scene_order = (
            True  # default as False, need to set True when using GroupEachSampleInBatchSampler and multi gpu eval
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=5,
            batch_sampler=GroupEachSampleInBatchSampler(val_dataset, shuffle=False, drop_last=False)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_dataset.scene_order = (
            True  # default as False, need to set True when using GroupEachSampleInBatchSampler and multi gpu eval
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=5,
            batch_sampler=GroupEachSampleInBatchSampler(val_dataset, shuffle=False, drop_last=False)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None
        ret_dict, loss_dict, _ = self.model(**batch)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)

        return loss, loss_dict

    @torch.no_grad()
    def test_step(self, batch):
        pred_dicts, _ = self.model(**batch)
        return pred_dicts

    def _configure_optimizer(self):
        layers_dict = model_named_layers(self.model)
        layer_groups = {name: [] for name, v in self.lr_scale_factor.items()}
        layer_groups.update({"others": []})
        for name, layer in layers_dict.items():
            exist = False
            for gallery_name in self.lr_scale_factor.keys():
                if gallery_name in name:
                    exist = True
                    break
            k = gallery_name if exist else "others"
            layer_groups[k].append(layer)

        lr_list = [v for k, v in self.lr_scale_factor.items()] + [1.0]
        lr_list = [self.lr * x for x in lr_list]

        optimizer_func = partial(optim.AdamW, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func,
            lr_list,
            [nn.Sequential(*v) for _, v in layer_groups.items()],
            wd=0.01,
            true_wd=True,
            bn_wd=True,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    End2endCli(Exp).run()
