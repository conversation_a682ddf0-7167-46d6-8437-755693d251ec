"""
changelog:
copied from perceptron/exps/end2end/private/model_cfg/det_map_model_cfg_7v5r_wmap_200m_2necks.py (branch map_city_20240912), modified map_head config
"""

from perceptron.exps.end2end.private.maptracker.data_cfg.maptracker_annos_hf import class_names
from perceptron.exps.end2end.private.maptracker.data_cfg.maptracker_annos_hf import arrow_name_id_map

CLASS_NAMES = class_names
_POINT_CLOUD_RANGE = [-15.2, -80.0, -5.0, 15.2, 204.8, 3.0]
_VOXEL02_VOXEL_SIZE = [0.2, 0.2, 0.2]
_VOXEL02_GRID_SIZE = [
    (_POINT_CLOUD_RANGE[3] - _POINT_CLOUD_RANGE[0]) / _VOXEL02_VOXEL_SIZE[0],
    (_POINT_CLOUD_RANGE[4] - _POINT_CLOUD_RANGE[1]) / _VOXEL02_VOXEL_SIZE[1],
    (_POINT_CLOUD_RANGE[5] - _POINT_CLOUD_RANGE[2]) / _VOXEL02_VOXEL_SIZE[2],
]
_VOXEL02_OUT_SIZE_FACTOR = 8
_POST_CENTER_RANGE = [
    _POINT_CLOUD_RANGE[0] - 10,
    _POINT_CLOUD_RANGE[1] - 10,
    -10,
    _POINT_CLOUD_RANGE[3] + 10,
    _POINT_CLOUD_RANGE[4] + 10,
    10,
]

VOXEL02_DET_HEAD = dict(
    type="CMTE2EHead",
    in_channels=512,
    num_query=900,
    modal=["Camera", "Radar"],
    depth_num=64,
    hidden_dim=256,
    downsample_scale=_VOXEL02_OUT_SIZE_FACTOR,
    grid_size=_VOXEL02_GRID_SIZE,
    # check
    use_dn=False,
    common_heads=dict(center=(2, 2), height=(1, 2), dim=(3, 2), rot=(2, 2), vel=(2, 2)),
    tasks=[
        dict(
            num_class=len(CLASS_NAMES),
            class_names=CLASS_NAMES,
        ),
    ],
    bbox_coder=dict(
        type="MultiTaskBBoxCoder",
        post_center_range=_POST_CENTER_RANGE,
        pc_range=_POINT_CLOUD_RANGE,
        max_num=300,
        voxel_size=_VOXEL02_VOXEL_SIZE,
        num_classes=len(CLASS_NAMES),
        score_threshold=0.1,
    ),
    separate_head=dict(type="SeparateTaskHead", init_bias=-2.19, final_kernel=3),
    transformer=dict(
        type="CMTTransformer",
        view_num=7,  # 这个参数务必需要与实际view数目保持一致 check
        decoder=dict(
            type="PETRTransformerDecoder",
            return_intermediate=True,
            num_layers=6,
            transformerlayers=dict(
                type="PETRTransformerDecoderLayer",
                with_cp=False,
                attn_cfgs=[
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                    dict(type="PETRMultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                ],
                ffn_cfgs=dict(
                    type="FFN",
                    embed_dims=256,
                    feedforward_channels=1024,
                    num_fcs=2,
                    ffn_drop=0.0,
                    act_cfg=dict(type="ReLU", inplace=True),
                ),
                feedforward_channels=1024,  # unused
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
    loss_cls=dict(type="FocalLoss", use_sigmoid=True, gamma=2, alpha=0.25, reduction="mean", loss_weight=2.0),
    loss_bbox=dict(type="L1Loss", reduction="mean", loss_weight=0.25),
    loss_heatmap=dict(type="GaussianFocalLoss", reduction="mean", loss_weight=1.0),
    use_roi_mask=False,
)

VOXEL02_TRAIN_CFG = dict(
    pts=dict(
        dataset="Private",
        assigner=dict(
            type="HungarianAssigner3D",
            cls_cost=dict(type="FocalLossCost", weight=2.0),
            reg_cost=dict(type="BBox3DL1Cost", weight=0.25),
            iou_cost=dict(type="IoUCost", weight=0.0),  # Fake cost. This is just to make it compatible with DETR head.
            pc_range=_POINT_CLOUD_RANGE,
        ),
        pos_weight=-1,
        gaussian_overlap=0.1,
        min_radius=2,
        grid_size=_VOXEL02_GRID_SIZE,  # [x_len, y_len, 1]
        voxel_size=_VOXEL02_VOXEL_SIZE,
        out_size_factor=_VOXEL02_OUT_SIZE_FACTOR,
        code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2],
        point_cloud_range=_POINT_CLOUD_RANGE,
    )
)

VOXEL01_TEST_CFG = dict(
    pts=dict(
        dataset="Private",
        grid_size=_VOXEL02_GRID_SIZE,
        out_size_factor=_VOXEL02_OUT_SIZE_FACTOR,
        pc_range=_POINT_CLOUD_RANGE[0:2],
        voxel_size=_VOXEL02_VOXEL_SIZE[:2],
        nms_type=None,
    )
)

CAMERA_ENCODER_CFG = dict(
    img_backbone=dict(
        # type='ResNet',
        depth=50,
        num_stages=4,
        # out_indices=(2, 3),
        out_indices=(0, 1, 2, 3),
        frozen_stages=-1,
        norm_cfg=dict(type="BN", requires_grad=True),
        norm_eval=True,
        style="pytorch",
        with_cp=False,
        init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
    ),
    # img_neck=dict(
    #     # type='FPN',
    #     in_channels=[1024, 2048],
    #     out_channels=256,
    #     num_outs=2,
    #     # with_cp=True,
    # ),
    img_neck=dict(
        type="SECONDFPN",
        in_channels=[256, 512, 1024, 2048],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[32, 32, 64, 128],
    ),
    img_neck_map=dict(
        type="SECONDFPN",
        in_channels=[256, 512, 1024, 2048],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[32, 32, 64, 128],
    ),
)

embed_dims = 256
num_feat_levels = 3
bev_h = 100
bev_w = 60
num_cams= 4

line_num_points = 20
stop_num_points = 4 # new gt: [[x1, y1, z1], [sin(d1), cos(d1), width,], [x2, y2, z2], [sin(d2), cos(d2), width]]
entrance_num_points = 4 # new gt: [[x1, y1, z1], [sin(d1), cos(d1), width,], [x2, y2, z2], [sin(d2), cos(d2), width]]
box_num_points = 4
permute = True
num_classes = 7 # 第一版分为 7 类，laneline / curb / stopline / crosswalk / arrow / nomarking / entrance
in_channels = 256  # neck 出来的 feat dim
# canvas_size = [64, 120]   # 做 img seg 约束的 size
canvas_size = [200, 120] # bev feature size
pc_range = [-30., 0., -6., 30., 100, 6] # [-15., 0., -6., 15., 100, 6]  # 和 dataset 的 map_lidar_range 一致，影响前后帧转换时 norm/denorm
MAP_HEAD = dict(
    type='MapTracker',
    bev_h=bev_h,
    bev_w=bev_w,
    pc_range=pc_range,
    history_steps=4,
    test_time_history_steps=20,
    mem_select_dist_ranges=[1, 5, 10, 15],
    track_fp_aug=False,
    use_memory=False,
    mem_len=4,
    mem_warmup_iters=-1,
    track=False,
    backbone_cfg=dict(
        type='BEVFormerBackbone',
        bev_h=bev_h,
        bev_w=bev_w,
        pc_range=pc_range,
        use_grid_mask=True,
        history_steps=4,
        transformer=dict(
            type='PerceptionTransformer',
            embed_dims=embed_dims,
            num_cams=num_cams,
            encoder=dict(
                type='BEVFormerEncoder',
                num_layers=2,
                pc_range=pc_range,
                num_points_in_pillar=4,
                return_intermediate=False,
                transformerlayers=dict(
                    type='BEVFormerLayer',
                    attn_cfgs=[
                        dict(
                            type='TemporalSelfAttention',
                            embed_dims=embed_dims,
                            num_levels=1),
                        dict(
                            type='SpatialCrossAttention',
                            deformable_attention=dict(
                                type='MSDeformableAttention3D',
                                embed_dims=embed_dims,
                                num_points=8,
                                num_levels=num_feat_levels),
                            embed_dims=embed_dims,
                            num_cams=num_cams,
                        ),
                    ],
                    feedforward_channels=embed_dims*2,
                    ffn_dropout=0.1,
                    operation_order=('self_attn', 'norm', 'cross_attn', 'norm', 
                                        'ffn', 'norm')
                )
            ),
        ),
        positional_encoding=dict(
            type='LearnedPositionalEncoding',
            num_feats=embed_dims//2,
            row_num_embed=bev_h,
            col_num_embed=bev_w,
            ),
    ),
    head_cfg=dict(
        type='MapDetectorBevHead',
        num_queries=400,
        query_split=[0, 200, 250, 350, 400],            # line/stop/entrance/box 四个大类使用query 区间
        query_group=1,                                  # line 多个 query对应一个instance
        num_classes=num_classes,
        num_lane_dash_attr=7,                           # 'unknow' 属性 参与模型训练
        num_lane_color_attr=3,                          # 'unknow' 属性 参与模型训练
        num_curb_attr=3,                                # 'unknow' 属性 参与模型训练
        num_arrow_attr=len(arrow_name_id_map.keys()),   # 'unknow' 属性 参与模型训练
        num_drection_attr=4,                            # 'unknow' 属性 参与模型训练
        attr_nums=6,                                    # 所有道路元素均加上方向
        in_channels=in_channels,
        embed_dims=embed_dims,
        depth_num=64,
        line_num_points=line_num_points,
        stop_num_points=stop_num_points, # stopline, entrance 均使用新建模方式
        entrance_num_points=entrance_num_points, # stopline, entrance 均使用新建模方式
        box_num_points=box_num_points,
        coord_dim=3,
        pc_range=pc_range,
        different_heads=False,  # 不同层之间的 feat 做分类/回归 的 mlp 是否共享参数, False 为共享参数 
        predict_refine=False,   # 每层 query 预测相对ref pts 的偏移值，还是直接预测最终坐标, False 表示预测最终坐标
        sync_cls_avg_factor=True,
        bg_cls_weight=0.,
        trans_loss_weight=0.1,
        transformer=dict(
            type='MapTransformerDecoder_new',
            num_layers=6,
            return_intermediate=True,
            coord_dim=3,
            transformerlayers=dict(
                type='MapTransformerLayer',
                attn_cfgs=[
                    dict(
                        type='MultiheadAttention',
                        embed_dims=embed_dims,
                        num_heads=8,
                        attn_drop=0.1,
                        proj_drop=0.1,
                    ),
                    dict(
                        type='MultiheadAttention',
                        embed_dims=embed_dims,
                        num_heads=8,
                        dropout=0.1,
                    ),
                ],
                ffn_cfgs=dict(
                    type='FFN',
                    embed_dims=embed_dims,
                    feedforward_channels=embed_dims*2,
                    num_fcs=2,
                    ffn_drop=0.1,
                    act_cfg=dict(type='ReLU', inplace=True),        
                ),
                feedforward_channels=embed_dims*2,
                ffn_dropout=0.1,
                operation_order=('self_attn', 'norm', 'cross_attn', 'norm',
                                'ffn', 'norm'),
            )
        ),
        loss_cls=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=5.0
        ),
        loss_line_reg=dict(
            type='SmoothL1Loss',
            loss_weight=50.0,
            beta=0.01,
        ),
        loss_stop_reg=dict(
            type='SmoothL1Loss',
            loss_weight=50.0,
            beta=0.1,
        ),
        loss_entrance_reg=dict(
            type='SmoothL1Loss',
            loss_weight=50.0,
            beta=0.1,
        ),
        loss_box_reg=dict(
            type='SmoothL1Loss',
            loss_weight=50.0,
            beta=0.01,
        ),
        loss_lane_color_attr=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=0.5
        ),
        loss_lane_dash_attr=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=0.5
        ),
        loss_curb_attr=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=0.5
        ),
        loss_arrow_attr=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=5.0
        ),
        loss_direction_attr=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=1.0
        ),
        assigner=dict(
            type='HungarianLinesAssigner',
                cost=dict(
                    type='MapQueriesCost',
                    cls_cost=dict(type='FocalLossCost', weight=5.0),
                    reg_cost=dict(type='LinesL1Cost', weight=50.0, beta=0.01, permute=permute),
                    ),
                ),
        ),
    seg_cfg=dict(
        type='MapSegHead',
        num_classes=num_classes,
        in_channels=in_channels,
        embed_dims=in_channels,
        canvas_size=canvas_size,
        bev_size=(bev_h, bev_w), # 需要和canvas_size成比例
        # bev_size=(32, 60),  # img feats size; petr方案用rv seg
        loss_seg=dict(
            type='MaskFocalLoss',
            use_sigmoid=True,
            loss_weight= 10.0, # 0.1,   # 加了 aug 之后，seg 会有问题，调小权重
        ),
        loss_dice=dict(
            type='MaskDiceLoss',
            loss_weight= 1.0, # 0.1, # 加了 aug 之后，seg 会有问题，调小权重
        )
    ),
)

MODEL_CFG = dict(
    num_query=900,
    num_classes=len(CLASS_NAMES),
    tracking=False,
    grid_mask=True,
    train_backbone=True,
    class_names=CLASS_NAMES,
    camera_encoder=CAMERA_ENCODER_CFG,
    det_head=VOXEL02_DET_HEAD,
    train_cfg=VOXEL02_TRAIN_CFG,
    test_cfg=VOXEL01_TEST_CFG,
    map_head=MAP_HEAD,
)