"""
changelog:
copied from perceptron/exps/end2end/private/model_cfg/det_map_model_cfg_7v5r_wmap_200m_2necks.py (branch map_city_20240912), modified map_head config
"""

from perceptron.exps.end2end.private.maptracker.data_cfg.maptracker_annos_hf import class_names
from perceptron.exps.end2end.private.maptracker.data_cfg.maptracker_annos_hf import arrow_name_id_map

CLASS_NAMES = class_names

_POINT_CLOUD_RANGE = [-15.2, 0., -3., 15.2, 100.8, 3]
_FRONT_LIDAR_POINT_CLOUD_RANGE = [-15.2, 0., -3., 15.2, 100.8, 3]   # 之所以不是整数，是因为后面的 lidar encoder 限制，不改参数的话，输入feature 大小需要是 8 的倍数
_VOXEL02_VOXEL_SIZE = [0.2, 0.2, 6]  # pointpillar

_FRONT_LIDAR_VOXEL02_GRID_SIZE = [
    round((_FRONT_LIDAR_POINT_CLOUD_RANGE[3] - _FRONT_LIDAR_POINT_CLOUD_RANGE[0]) / _VOXEL02_VOXEL_SIZE[0]),
    round((_FRONT_LIDAR_POINT_CLOUD_RANGE[4] - _FRONT_LIDAR_POINT_CLOUD_RANGE[1]) / _VOXEL02_VOXEL_SIZE[1]),
    round((_FRONT_LIDAR_POINT_CLOUD_RANGE[5] - _FRONT_LIDAR_POINT_CLOUD_RANGE[2]) / _VOXEL02_VOXEL_SIZE[2]),
]

CAMERA_ENCODER_CFG = dict(
    img_backbone=dict(
        # type='ResNet',
        depth=50,
        num_stages=4,
        # out_indices=(2, 3),
        out_indices=(0, 1, 2, 3),
        frozen_stages=-1,
        norm_cfg=dict(type="BN", requires_grad=True),
        norm_eval=True,
        style="pytorch",
        with_cp=False, #True,
        init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
    ),
    # img_neck=dict(
    #     # type='FPN',
    #     in_channels=[1024, 2048],
    #     out_channels=256,
    #     num_outs=2,
    #     # with_cp=True,
    # ),
    img_neck=dict(
        type="SECONDFPN",
        in_channels=[256, 512, 1024, 2048],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[32, 32, 64, 128],
    ),
    img_neck_map=dict(
        type="SECONDFPN",
        in_channels=[256, 512, 1024, 2048],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[32, 32, 64, 128],
    ),
)

LIDAR_ENCODER_CFG = dict(
    point_cloud_range=_FRONT_LIDAR_POINT_CLOUD_RANGE,
    voxel_size=_VOXEL02_VOXEL_SIZE,
    grid_size=_FRONT_LIDAR_VOXEL02_GRID_SIZE,
    max_num_points=16,
    # max_voxels=(260000, 280000),
    max_voxels=(100000, 120000),
    # max_voxels=(120000, 120000),
    src_num_point_features=4,
    use_num_point_features=4,
    map_to_bev_num_features=64,
    vfe=dict(
        vfe_num_filters=[64], num_point_features=4, voxel_size=_VOXEL02_VOXEL_SIZE, point_cloud_range=_POINT_CLOUD_RANGE
    ),
    backbone_2d=dict(
        layer_nums=[3, 5, 5],
        layer_strides=[2, 2, 2],
        num_filters=[64, 128, 256],
        upsample_strides=[1, 2, 4],
        num_upsample_filters=[128, 128, 128],
        input_channels=64,  # sp conv output channel
        with_cp=False, #True,
        use_scconv=True,
        upsample_output=False,
    ),
)

embed_dims = 256
num_points = 20
permute = True
num_classes = 7 # 第一版分为 7 类，laneline / curb / stopline / crosswalk / arrow / nomarking / entrance
in_channels = 256  # neck 出来的 feat dim
canvas_size = [64, 120]   # 做 img seg 约束的 size
pc_range = [-15.2, 0., -3., 15.2, 100.8, 3] # 和 dataset 的 map_lidar_range 一致，影响前后帧转换时 norm/denorm
MAP_HEAD = dict(
    type='MapTracker',
    pc_range=pc_range,
    history_steps=4,
    test_time_history_steps=20,
    mem_select_dist_ranges=[1, 5, 10, 15],
    track_fp_aug=False,
    use_memory=False,
    mem_len=4,
    mem_warmup_iters=-1,
    track=False,
    modal=["lidar"],
    head_cfg=dict(
        type='MapDetectorHead',
        modal=["lidar"],
        num_queries=100,
        query_group=4,
        num_classes=num_classes,
        num_lane_dash_attr=4,   # 2,
        num_lane_color_attr=2,
        num_curb_attr=2,
        num_arrow_attr=len(arrow_name_id_map.keys())-1, 
        attr_nums=5,
        in_channels=in_channels,
        embed_dims=embed_dims,
        depth_num=64,
        num_points=num_points,
        coord_dim=3,
        pc_range=pc_range,
        different_heads=False,  # 不同层之间的 feat 做分类/回归 的 mlp 是否共享参数, False 为共享参数 
        predict_refine=False,   # 每层 query 预测相对ref pts 的偏移值，还是直接预测最终坐标, False 表示预测最终坐标
        sync_cls_avg_factor=True,
        bg_cls_weight=0.,
        trans_loss_weight=0.1,
        transformer=dict(
            type='MapTransformerDecoder_new',
            num_layers=6,
            return_intermediate=True,
            coord_dim=3,
            transformerlayers=dict(
                type='MapTransformerLayer',
                attn_cfgs=[
                    dict(
                        type='MultiheadAttention',
                        embed_dims=embed_dims,
                        num_heads=8,
                        attn_drop=0.1,
                        proj_drop=0.1,
                    ),
                    dict(
                        type='MultiheadAttention',
                        embed_dims=embed_dims,
                        num_heads=8,
                        dropout=0.1,
                    ),
                ],
                ffn_cfgs=dict(
                    type='FFN',
                    embed_dims=embed_dims,
                    feedforward_channels=embed_dims*2,
                    num_fcs=2,
                    ffn_drop=0.1,
                    act_cfg=dict(type='ReLU', inplace=True),        
                ),
                feedforward_channels=embed_dims*2,
                ffn_dropout=0.1,
                operation_order=('self_attn', 'norm', 'cross_attn', 'norm',
                                'ffn', 'norm'),
            )
        ),
        loss_cls=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=5.0
        ),
        loss_reg=dict(
            type='LinesL1Loss',
            loss_weight=50.0,
            beta=0.01,
        ),
        loss_lane_color_attr=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=0.5
        ),
        loss_lane_dash_attr=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=0.5
        ),
        loss_curb_attr=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=0.5
        ),
        loss_arrow_attr=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=1.0
        ),
        assigner=dict(
            type='HungarianLinesAssigner',
                cost=dict(
                    type='MapQueriesCost',
                    cls_cost=dict(type='FocalLossCost', weight=5.0),
                    reg_cost=dict(type='LinesL1Cost', weight=50.0, beta=0.01, permute=permute),
                    ),
                ),
        ),
    seg_cfg=dict(
        type='MapSegHead',
        num_classes=num_classes,
        in_channels=in_channels,
        embed_dims=in_channels,
        canvas_size=canvas_size,
        bev_size=(32, 60),  # img feats size
        loss_seg=dict(
            type='MaskFocalLoss',
            use_sigmoid=True,
            loss_weight=10.0,
        ),
        loss_dice=dict(
            type='MaskDiceLoss',
            loss_weight=1.0,
        )
    ),
)

MODEL_CFG = dict(
    num_query=900,
    num_classes=len(CLASS_NAMES),
    tracking=False,
    grid_mask=True,
    train_backbone=True,
    class_names=CLASS_NAMES,
    camera_encoder=CAMERA_ENCODER_CFG,
    lidar_encoder=LIDAR_ENCODER_CFG,
    det_head=dict(),
    train_cfg=dict(),
    test_cfg=dict(),
    map_head=MAP_HEAD,
)
