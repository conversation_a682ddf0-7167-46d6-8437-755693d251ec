from perceptron.data.det3d.modules.annotation import AnnotationDet, MapTrackerSelf
import copy

from perceptron.data.det3d.source.config import HFCar9
from perceptron.data.det3d.modules import LoaderSimFov
from perceptron.exps.end2end.private.sensor_cfg.e2e_sensors_hf_4v0r1l import (
    lidar as lidar_cfg,
    image as image_cfg,
    radar as radar_cfg,
    _CAMERA_LIST,
    _SENSOR_NAMES,
)
from perceptron.data.det3d.modules import (
    EvaluationV3,
    MultiFrameImageAffineTransformation,
    CameraUndistortCPU,
    ObjectRangeFilter,
    PointShuffle,
)
from perceptron.utils.maptracker_utils.evaltools.eval_map import MapEvaluator

category_map = {
    "小汽车": "car",
    "汽车": "car",
    "货车": "truck",
    "工程车": "construction_vehicle",
    "巴士": "bus",
    "摩托车": "motorcycle",
    "自行车": "bicycle",
    "三轮车": "tricycle",
    "骑车人": "cyclist",
    "骑行的人": "cyclist",
    "人": "pedestrian",
    "行人": "pedestrian",
    "其它": "other",
    "其他": "other",
    "残影": "ghost",
    "蒙版": "masked_area",
    "car": "car",
    "truck": "truck",
    "construction_vehicle": "construction_vehicle",
    "bus": "bus",
    "motorcycle": "motorcycle",
    "bicycle": "bicycle",
    "tricycle": "tricycle",
    "cyclist": "cyclist",
    "pedestrian": "pedestrian",
    "other": "other",
    "ghost": "ghost",
    "masked_area": "masked_area",
}

category_map_reverse = {
    "car": "汽车",
    "truck": "货车",
    "construction_vehicle": "工程车",
    "bus": "巴士",
    "motorcycle": "摩托车",
    "bicycle": "自行车",
    "tricycle": "三轮车",
    "cyclist": "骑车人",
    "pedestrian": "人",
    "other": "其它",
    "ghost": "残影",
    "masked_area": "蒙版",
}

class_names = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
    # "masked_area",
]
point_cloud_range = [-15.2, 0., -3., 15.2, 100.8, 3]

_PIPELINE_MULTIFRAME = dict(
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,
    ),
    undistort=dict(
        type=CameraUndistortCPU,
    ),
    point_shuffle=dict(
        type=PointShuffle,
    ),
    # bda_aug: need to be adapted
    ida_aug=dict(
        type=MultiFrameImageAffineTransformation,
        aug_conf=dict(
            final_dim=(512, 960),
            # resize_lim=((0.472, 0.5), (0.472, 0.5)),
            resize_lim=(0.5, 0.5),  # zy check
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=False,
            rot_lim=(-0.0, 0.0),
        ),
        camera_names=_CAMERA_LIST,
        img_norm=True,
        img_conf={
            "img_mean": [123.675, 116.28, 103.53],
            "img_std": [58.395, 57.12, 57.375],
            "to_rgb": False,
        },  # 这里 的 to_rgb 字段在 gpu aug=True 时没有用到
    ),
)

map_class_dict = {
        'laneline': { # 注意数据json中 laneline为lane
            'id': 0,
            'pt_num': 20,
            'w_guass': False,
            "dt_scores": 0.4,
        },
        'curb': {    # 注意数据json中 为curb存在在lane中
            'id': 1,
            'pt_num': 20,
            'w_guass': False,
            "dt_scores": 0.4,
        },
        'stopline': {
            'id': 2,
            'pt_num': 20,
            'w_guass': False,
            "dt_scores": 0.4,
            "filter_range": [-10., 0., -6., 10., 60, 6],
        },
        'crosswalk': {
            'id': 3,
            'pt_num': 20,
            'w_guass': True,
            "dt_scores": 0.4,
            "filter_range": [-10., 0., -6., 10., 60, 6],
        }, 
        'arrow': {
            'id': 4,
            'pt_num': 20,
            'w_guass': True,
            "dt_scores": 0.4,
            "filter_range": [-6., 0., -6., 6., 40, 6],
        }, 
        'noparking': {
            'id': 5,
            'pt_num': 20,
            'w_guass': True,            
            "dt_scores": 0.4,
            "filter_range": [-12., 0., -6., 12., 40., 6.],
        }, 
        'entrance': {
            'id': 6,
            'pt_num': 20,
            'w_guass': True,
            "dt_scores": 0.4,
            "filter_range": [-10., 0., -6., 10., 60, 6],
        },
        'mask': {
            'id': 7,
            'pt_num': 20,
            'w_guass': True,
            "dt_scores": 0.4,
        }, 
    } # 'centerline'


map_cam_dict = {
        "cam_front_120": {},
        "cam_front_30": {},
        "cam_front_left_120": {},
        "cam_front_right_120": {},
    }

arrow_name_id_map = { # TODO：id-name map
    "unknown":0,
    "guide_turn":1,
    "guide_turn+guide_through":2,
    "guide_turn+guide_left":3,
    "guide_turn+guide_right":4,
    "guide_left":5,
    "guide_left+guide_through":6,
    "guide_left+guide_right":7,
    "guide_left+guide_through+guide_right":8,
    "guide_through":9,
    "guide_through+guide_right":10,
    "guide_right":11,

    "forbid_turn":12,
    "forbid_left":13,
    "forbid_left+forbid_right":14,
    "forbid_through":15,
    "forbid_through+forbid_right":16,
    "forbid_through+forbid_left":17,
    "forbid_right":18,

    "merge_left":19,
    "merge_right":20,
}
arrow_id_name_map = {}
for k, v in arrow_name_id_map.items():
    arrow_id_name_map[v] = k

map_ego_range = [0, -15.2, -3, 100.8, 15.2, 3]        # ego [xmin, ymin, zmin, xmax, ymax, zmax]
map_lidar_range = [-15.2, 0., -3., 15.2, 100.8, 3]  # lidar [xmin, ymin, zmin, xmax, ymax, zmax]; map_ego_range 与 map_lidar_range需要联动更改；
annotation_cfg = dict(
    box=dict(
        type=AnnotationDet,
        category_map=category_map,
        class_names=class_names,
        occlusion_threshold=-1,
        filter_outlier_boxes=False,  # was True,
        filter_outlier_frames=False,  # was True,
        filter_empty_2d_bboxes=False,
        filter_empty_frames=False,  # was True,
        roi_range=point_cloud_range,
        # with_predict=False,
        HF=True,
        # fut_traj_len=65,
    ),
    maptracker=dict(
        type=MapTrackerSelf,
        map_class_dict=map_class_dict,
        map_cam_dict=map_cam_dict,
        arrow_name_id_map=arrow_name_id_map,
        map_point_cloud_range=map_ego_range,
        map_lidar_range=map_lidar_range,
        seg_canvas_size=[64, 120],   # 和模型 seg decoder 里定义的 canvas size 一致或者略大于模型定义即可
        seg_thickness=2,
        scene_weight=False, 
    ),
)

_CAR = HFCar9
base_dataset_cfg = dict(
    car=dict(type=_CAR),
    mode="train",
    gpu_aug=True,
    postcollate_tensorize=True,
    sensor_names=_SENSOR_NAMES,
    num_frames_per_sample=1,
    loader=dict(
        type=LoaderSimFov,
        car=_CAR,
        camera_names=_CAMERA_LIST,
        datasets_names=["map_city_roadentrance_for_debug"], # "map_lane_mf", "map_city_roadentrance_20w", "map_city_roadentrance_43w_1009"
        only_key_frame=False,  # was True
        rebuild=False,
    ),
    lidar=lidar_cfg,
    image=image_cfg,
    radar=radar_cfg,
    annotation=annotation_cfg,
    pipeline=_PIPELINE_MULTIFRAME,
)

# config for eval dataset
val_dataset_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_cfg.update(mode="val")
val_dataset_cfg["loader"].update(datasets_names=["CAR9_BMK_OCC_DAY"])
val_dataset_cfg["annotation"]["box"].update(occlusion_threshold=1)  # 这里需要确认下!
val_dataset_cfg["radar"]["with_virtual_radar"] = False
val_dataset_cfg.update(
    evaluator=dict(
        type=EvaluationV3,
        category_map=category_map_reverse,
        dump_det_results=False,
        eval_cfg_l3="e2e_l3_far",
        eval_cfg_l2="e2e_gaosu_l3",
    )
)

val_dataset_cfg_map = copy.deepcopy(val_dataset_cfg)
val_dataset_cfg_map.update(
    evaluator=dict(
        type=MapEvaluator,
        map_range=map_lidar_range,
        eval_range=[-15., 0., -6., 15., 100, 6],    # lidar [xmin, ymin, zmin, xmax, ymax, zmax]; map_ego_range 与 map_lidar_range需要联动更改；
        core_range=[-6, 5, -2, 6, 50, 2],           # 评测核心关注区域 
        line_eval_cfg=dict(     
            inter_dis=0.1,                          # cd 需要较小的inter_dis, pl 能使用更大的inter_dis 1m；
            threash_dict=dict(fp_cd_thresh_list=0.3,    # dt 作为 fp 距离判定阈值， 单位 voxel / m；
                            inf_th_dis=20),             # 无效 inf_th
            metric_conf=dict(keep_ratio=0.8, mode="d<->g"), 
            decay_dis=dict(x=60, y=600, z=12),          # 评测完全衰减距离；
        ),
        box_eval_cfg=dict(
            iou_th=0.5,
            inf_iou=0.0,
        ),
        map_class_dict=map_class_dict,              # 进行评测道路元素
        arrow_name_id_map=arrow_name_id_map,        # 箭头 name-id 
        arrow_id_name_map=arrow_id_name_map,        # 箭头 id-name
        
        with_attr=False,                            # TODO: 评测过程是否考虑属性
        with_cd=False,                              # 评测 Line 使用ChamferDistanceMetric
        with_vd=False,                              # TODO: 评测 Line 使用VoxelDistanceMetric
        with_pl=True,                               # 评测 Line 使用PointLineDistanceMetric
        with_iou=True,                              # 评测 Box 使用 IOU Metric
        with_kl_d=False,                            # 评测 Box 使用 KL Metric
        with_wt_d=False,                            # 评测 Box 使用 Wasserstein Metric
        with_vis=True,                              # 评测过程是否可视化
    )
)
val_dataset_cfg_map["loader"]["datasets_names"] = ["map20w_val_mf"]
val_dataset_cfg_map["loader"]["only_key_frame"] = False
