"""

Car:
    Define mapping for cameras, and provide data list for train and eval.

Image Options:
    undistort: Switch of all undistortion related functions

    target_resolution: Determine the size of the undistorted image

    undistort_func: Calculate the transformation relations from the calibration parameters. SimFov is used to project the physical camera to a hypothetical standard camera with the same intrinsics.

    postpone_undistort: Option for sending original image and undistortion mapping to pipeline, such as gpu undistort

Lidar Options:
    referen_lidar: Transform to convert lidar to gnss coordinates

    pc_fields: Key and order from structured point cloud data to numpy array

    lidar_sweeps_idx: Deprecated

    lidar_with_timestamp: Deprecated, add frame timestamp to last column
"""

from perceptron.data.det3d.modules import (
    UndistortStandard,
    MultiFrameImageAffineTransformation,
    CameraUndistortCPU,
    LidarBase,
    ImageSimFov,
    # EvaluationE2E,
    PointShuffle,
    ObjectRangeFilter,
)
from perceptron.data.det3d.source.config import GeelyCar1


_CAR = GeelyCar1

_CAMERA_LIST = [
    "cam_front_left_120",
    "cam_front_right_120",
    "cam_back_left_120",
    "cam_back_right_120",
    "cam_front_70_left",
    "cam_back_120",
]
_LIDAR_LIST = [
    "fuser_lidar",
]

_SENSOR_NAMES = dict(
    camera_names=_CAMERA_LIST,
    lidar_names=_LIDAR_LIST,
)

_CAMERA_UNDISTORT_FUNC = dict(
    cam_front_left_120=(UndistortStandard,),
    cam_front_right_120=(UndistortStandard,),
    cam_back_left_120=(UndistortStandard,),
    cam_back_right_120=(UndistortStandard,),
    cam_front_70_left=(UndistortStandard,),
    cam_back_120=(UndistortStandard,),
)

point_cloud_range = [-51.2, -51.2, -5.0, 51.2, 51.2, 3.0]

category_map = {
    "小汽车": "car",
    "汽车": "car",
    "货车": "truck",
    "工程车": "construction_vehicle",
    "巴士": "bus",
    "摩托车": "motorcycle",
    "自行车": "bicycle",
    "三轮车": "tricycle",
    "骑车人": "cyclist",
    "骑行的人": "cyclist",
    "人": "pedestrian",
    "行人": "pedestrian",
    "其它": "other",
    "其他": "other",
    "残影": "ghost",
    "蒙版": "masked_area",
}

category_map_reverse = {
    "car": "汽车",
    "truck": "货车",
    "construction_vehicle": "工程车",
    "bus": "巴士",
    "motorcycle": "摩托车",
    "bicycle": "自行车",
    "tricycle": "三轮车",
    "cyclist": "骑车人",
    "pedestrian": "人",
    "other": "其它",
    "ghost": "残影",
    "masked_area": "蒙版",
}

class_names = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
    # "masked_area",
]

_PIPELINE_MULTIFRAME = dict(
    point_shuffle=dict(
        type=PointShuffle,
    ),
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,
    ),
    undistort=dict(
        type=CameraUndistortCPU,
    ),
    ida_aug=dict(
        type=MultiFrameImageAffineTransformation,
        aug_conf=dict(
            final_dim=(256, 704),
            resize_lim=(0.34, 0.55),
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=False,
            rot_lim=(-0.0, 0.0),
        ),
        camera_names=_CAMERA_LIST,
        img_norm=True,
        img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
    ),
)

lidar = dict(
    type=LidarBase,
    car=_CAR,
    lidar_names=_LIDAR_LIST,
    referen_lidar="middle_lidar",
    pc_fields=["x", "y", "z", "i"],
    used_echo_id=[1],  # 激光雷达回波
    lidar_sweeps_idx=[],
    lidar_with_timestamp=False,
)
image = dict(
    type=ImageSimFov,
    car=_CAR,
    camera_names=_CAMERA_LIST,
    undistort=True,
    undistort_func=_CAMERA_UNDISTORT_FUNC,
    target_resolution="200w",
    postpone_undistort=True,
)
