from perceptron.data.det3d.modules import (
    UndistortStandard,
    LidarBase,
    ImageSimFov,
    HFRadar,
)
from perceptron.data.det3d.source.config import Z10


_CAR = Z10
# camera 被注释掉了
_CAMERA_LIST = [
    "cam_front_120",  # 地图需要, 这里需要放【第1个】，用来和 rv 监督对齐
    # "cam_front_120_sim_fov70",
    "cam_front_30",
    # "cam_back_120_sim_fov70",
    "cam_front_left_120",
    "cam_front_right_120",
    # "cam_back_left_120",
    # "cam_back_right_120",
]
_LIDAR_LIST = []

_RADAR_KEY_LIST = []

_SENSOR_NAMES = dict(camera_names=_CAMERA_LIST, lidar_names=_LIDAR_LIST, radar_names=_RADAR_KEY_LIST)

_CAMERA_UNDISTORT_FUNC = dict(
    cam_front_120=(UndistortStandard,),
    # cam_front_120_sim_fov70=(UndistortSimFov,),
    cam_front_left_120=(UndistortStandard,),
    cam_front_right_120=(UndistortStandard,),
    # cam_back_left_120=(UndistortStandard,),
    # cam_back_right_120=(UndistortStandard,),
    cam_front_30=(UndistortStandard,),
    # cam_back_120_sim_fov70=(UndistortSimFov,),
)

lidar = dict(
    type=LidarBase,
    car=_CAR,
    lidar_names=_LIDAR_LIST,
    referen_lidar="front_lidar",
    pc_fields=["x", "y", "z", "i"],
    used_echo_id=[1],  # 激光雷达回波
    lidar_sweeps_idx=[],
    lidar_with_timestamp=False,
)

image = dict(
    type=ImageSimFov,
    car=_CAR,
    camera_names=_CAMERA_LIST,
    undistort=True,
    undistort_func=_CAMERA_UNDISTORT_FUNC,
    target_resolution="200w",
    postpone_undistort=True,
)

radar = dict(
    type=HFRadar,
    car=_CAR,
    radar_mode="mlp",
    with_virtual_radar=False,
)
