import os
import pickle
import warnings
import nori2 as nori
import numpy as np
import copy
import mmcv
from numba import errors
from refile import smart_open
from data3d.datasets.data_path_manager import GtDatabasePathManager
from data3d.transforms.t3d.meg3d import functional
from mmdet3d.core.bbox import box_np_ops
from mmdet3d.datasets.pipelines.data_augment_utils import box_collision_test
from mmdet3d.datasets.pipelines.dbsampler import BatchSampler
from data3d.transforms.t3d.mm3d.e2e.loading import LoadPointsFromNori

warnings.filterwarnings("ignore", category=errors.NumbaPerformanceWarning)

NORI_FETCHER = nori.Fetcher()


def path_join(dir_path, relative_path):
    if type(dir_path) == str:
        abs_path = os.path.join(dir_path, relative_path)
    else:
        abs_path = dir_path.resolve() / relative_path
    return str(abs_path)


class DataBaseSampler(object):
    def __init__(
        self,
        root_path,
        data_name,
        data_split,
        class_names,
        sampler_groups,
        num_point_feature,
        remove_extra_width=(0, 0, 0),
        use_road_plane=False,
        database_with_fakelidar=False,
        filter_by_min_points_cfg=None,
        removed_difficulty=None,
        limit_whole_scene=False,
        logger=None,
    ):
        self.root_path = root_path
        self.class_names = class_names
        self.use_road_plane = use_road_plane
        self.limit_whole_scene = limit_whole_scene
        self.remove_extra_width = remove_extra_width
        self.database_with_fakelidar = database_with_fakelidar
        self.num_point_feature = num_point_feature
        self.logger = logger
        self.cat2label = {name: i for i, name in enumerate(class_names)}
        self.label2cat = {i: name for i, name in enumerate(class_names)}
        self.db_infos = {}
        for class_name in class_names:
            self.db_infos[class_name] = []

        db_info_path = GtDatabasePathManager.get_gt_database_path_by_name(data_name)[data_split]
        if not db_info_path.startswith("s3"):
            db_info_path = path_join(self.root_path, db_info_path)
        with smart_open(str(db_info_path), "rb") as f:
            infos = pickle.load(f)
            [self.db_infos[cur_class].extend(infos[cur_class]) for cur_class in class_names]

        if filter_by_min_points_cfg is not None:
            self.db_infos = self.filter_by_min_points(self.db_infos, filter_by_min_points_cfg)
        if removed_difficulty is not None:
            self.db_infos = self.filter_by_difficulty(self.db_infos, removed_difficulty)

        self.sample_groups = {}
        self.sample_class_num = {}

        for x in sampler_groups:
            class_name, sample_num = x.split(":")
            if class_name not in class_names:
                continue
            self.sample_class_num[class_name] = sample_num
            self.sample_groups[class_name] = {
                "sample_num": sample_num,
                "pointer": len(self.db_infos[class_name]),
                "indices": np.arange(len(self.db_infos[class_name])),
            }

    def __getstate__(self):
        d = dict(self.__dict__)
        del d["logger"]
        return d

    def __setstate__(self, d):
        self.__dict__.update(d)

    def filter_by_difficulty(self, db_infos, removed_difficulty):
        new_db_infos = {}
        for key, dinfos in db_infos.items():
            pre_len = len(dinfos)
            new_db_infos[key] = [info for info in dinfos if info["difficulty"] not in removed_difficulty]
            if self.logger is not None:
                self.logger.info("Database filter by difficulty %s: %d => %d" % (key, pre_len, len(new_db_infos[key])))
        return new_db_infos

    def filter_by_min_points(self, db_infos, min_gt_points_list):
        for name_num in min_gt_points_list:
            name, min_num = name_num.split(":")
            min_num = int(min_num)
            if min_num > 0 and name in db_infos.keys():
                filtered_infos = []
                for info in db_infos[name]:
                    if info["num_points_in_gt"] >= min_num:
                        filtered_infos.append(info)

                if self.logger is not None:
                    self.logger.info(
                        "Database filter by min points %s: %d => %d" % (name, len(db_infos[name]), len(filtered_infos))
                    )
                db_infos[name] = filtered_infos

        return db_infos

    def sample_with_fixed_number(self, class_name, sample_group):
        """
        Args:
            class_name:
            sample_group:
        Returns:

        """
        sample_num, pointer, indices = int(sample_group["sample_num"]), sample_group["pointer"], sample_group["indices"]
        if pointer >= len(self.db_infos[class_name]):
            indices = np.random.permutation(len(self.db_infos[class_name]))
            pointer = 0

        sampled_dict = [self.db_infos[class_name][idx] for idx in indices[pointer : pointer + sample_num]]
        pointer += sample_num
        sample_group["pointer"] = pointer
        sample_group["indices"] = indices
        return sampled_dict

    @staticmethod
    def put_boxes_on_road_planes(gt_boxes, road_planes, calib):
        """
        Only validate in KITTIDataset
        Args:
            gt_boxes: (N, 7 + C) [x, y, z, dx, dy, dz, heading, ...]
            road_planes: [a, b, c, d]
            calib:

        Returns:
        """
        a, b, c, d = road_planes
        center_cam = calib.lidar_to_rect(gt_boxes[:, 0:3])
        cur_height_cam = (-d - a * center_cam[:, 0] - c * center_cam[:, 2]) / b
        center_cam[:, 1] = cur_height_cam
        cur_lidar_height = calib.rect_to_lidar(center_cam)[:, 2]
        mv_height = gt_boxes[:, 2] - gt_boxes[:, 5] / 2 - cur_lidar_height
        gt_boxes[:, 2] -= mv_height  # lidar view
        return gt_boxes, mv_height

    def add_sampled_boxes_to_scene(self, data_dict, sampled_gt_boxes, total_valid_sampled_dict):
        gt_boxes_mask = data_dict.get("gt_boxes_mask", range(data_dict["gt_boxes"].shape[0]))
        gt_boxes = data_dict["gt_boxes"][gt_boxes_mask]
        if "gt_names" in data_dict:
            gt_names = data_dict["gt_names"][gt_boxes_mask]
        else:
            gt_names = np.array([self.label2cat[l] for l in data_dict["gt_labels"]])[gt_boxes_mask]
        points = data_dict["points"]
        if self.use_road_plane:
            sampled_gt_boxes, mv_height = self.put_boxes_on_road_planes(
                sampled_gt_boxes, data_dict["road_plane"], data_dict["calib"]
            )
            data_dict.pop("calib")
            data_dict.pop("road_plane")

        obj_points_list = []
        for idx, info in enumerate(total_valid_sampled_dict):

            if "nori_id" in info:
                obj_points = np.frombuffer(NORI_FETCHER.get(info["nori_id"]), dtype=np.float32)
                obj_points = np.array(obj_points, dtype=np.float32)
            else:
                file_path = os.path.join("../data/once", info["path"])
                obj_points = np.fromfile(str(file_path), dtype=np.float32)

            obj_points = obj_points.reshape([-1, self.num_point_feature])

            obj_points[:, :3] += info["box3d_lidar"][:3]

            if self.use_road_plane:
                # mv height
                obj_points[:, 2] -= mv_height[idx]

            """
            We add this for point painting
            """
            if points.shape[1] > obj_points.shape[1]:
                append_dim = points.shape[1] - obj_points.shape[1]
                obj_label = self.class_names.index(info["name"]) + 1  # +1 for bg
                one_hot_feature = np.zeros((obj_points.shape[0], append_dim))
                one_hot_feature[:, obj_label] = 1
                obj_points = np.concatenate([obj_points, one_hot_feature], axis=1)
            """
            end
            """

            obj_points_list.append(obj_points)

        obj_points = np.concatenate(obj_points_list, axis=0)
        sampled_gt_names = np.array([x["name"] for x in total_valid_sampled_dict])

        large_sampled_gt_boxes = functional.enlarge_box3d(sampled_gt_boxes[:, 0:7], extra_width=self.remove_extra_width)
        points = functional.remove_points_in_boxes(points, large_sampled_gt_boxes)

        points = np.concatenate([obj_points, points], axis=0)
        gt_names = np.concatenate([gt_names, sampled_gt_names], axis=0)
        gt_boxes = np.concatenate([gt_boxes, sampled_gt_boxes], axis=0)
        if "gt_names" in data_dict:
            data_dict["gt_names"] = gt_names
        else:
            data_dict["gt_labels"] = np.array([self.cat2label[cat] for cat in gt_names])
        data_dict["gt_boxes"] = gt_boxes
        data_dict["points"] = points
        return data_dict

    def __call__(self, data_dict):
        """
        Args:
            data_dict:
                gt_boxes: (N, 7 + C) [x, y, z, dx, dy, dz, heading, ...]

        Returns:

        """
        gt_boxes = data_dict["gt_boxes"]
        assert (
            "gt_names" in data_dict or "gt_labels" in data_dict
        ), "gt_labels or gt_names must be included in data_dict"
        if "gt_names" in data_dict:
            gt_names = data_dict["gt_names"].astype(str)
        else:
            gt_names = np.array([self.label2cat[l] for l in data_dict["gt_labels"]])
        existed_boxes = gt_boxes
        total_valid_sampled_dict = []
        for class_name, sample_group in self.sample_groups.items():
            if self.limit_whole_scene:
                num_gt = np.sum(class_name == gt_names) if gt_names.shape[0] > 0 else 0
                sample_group["sample_num"] = str(int(self.sample_class_num[class_name]) - num_gt)

            num_sampled = int(sample_group["sample_num"])
            num_gt = existed_boxes.shape[0]
            if num_sampled > 0:
                sampled_dict = self.sample_with_fixed_number(class_name, sample_group)
                sampled_boxes = np.stack([x["box3d_lidar"] for x in sampled_dict], axis=0).astype(np.float32)

                if self.database_with_fakelidar:
                    sampled_boxes = functional.boxes3d_kitti_fakelidar_to_lidar(sampled_boxes)

                total_boxes = np.concatenate([existed_boxes, sampled_boxes], axis=0).copy()
                total_boxes_bev = functional.center_to_corner_box2d(
                    total_boxes[:, 0:2], total_boxes[:, 3:5], total_boxes[:, 6]
                )
                coll_mat = box_collision_test(total_boxes_bev, total_boxes_bev)

                diag = np.arange(total_boxes_bev.shape[0])
                coll_mat[diag, diag] = False

                valid_sampled_dict = []
                for i in range(num_gt, total_boxes_bev.shape[0]):
                    if coll_mat[i].any():
                        coll_mat[i] = False
                        coll_mat[:, i] = False
                    else:
                        valid_sampled_dict.append(sampled_dict[i - num_gt])

                if len(valid_sampled_dict) > 0:
                    valid_sampled_boxes = np.stack([x["box3d_lidar"] for x in valid_sampled_dict], axis=0).astype(
                        np.float32
                    )

                    existed_boxes = np.concatenate((existed_boxes, valid_sampled_boxes), axis=0)
                    total_valid_sampled_dict.extend(valid_sampled_dict)

        sampled_gt_boxes = existed_boxes[gt_boxes.shape[0] :, :]
        if total_valid_sampled_dict.__len__() > 0:
            data_dict = self.add_sampled_boxes_to_scene(data_dict, sampled_gt_boxes, total_valid_sampled_dict)
        if "gt_boxes_mask" in data_dict:
            data_dict.pop("gt_boxes_mask")
        return data_dict


class UnifiedDataBaseSampler(object):
    """Class for sampling data from the ground truth database.

    Args:
        info_path (str): Path of groundtruth database info.
        data_root (str): Path of groundtruth database.
        rate (float): Rate of actual sampled over maximum sampled number.
        prepare (dict): Name of preparation functions and the input value.
        sample_groups (dict): Sampled classes and numbers.
        classes (list[str]): List of classes. Default: None.
        points_loader(dict): Config of points loader. Default: dict(
            type='LoadPointsFromFile', load_dim=4, use_dim=[0,1,2,3])
    """

    def __init__(
        self,
        info_path,
        data_root,
        rate,
        prepare,
        sample_groups,
        classes=None,
        points_loader=dict(
            #  type='LoadPointsFromFile',
            coord_type="LIDAR",
            load_dim=4,
            use_dim=[0, 1, 2, 3],
        ),
    ):
        super().__init__()
        self.data_root = data_root
        self.info_path = info_path
        self.rate = rate
        self.prepare = prepare
        self.classes = classes
        self.cat2label = {name: i for i, name in enumerate(classes)}
        self.label2cat = {i: name for i, name in enumerate(classes)}
        self.points_loader = LoadPointsFromNori(**points_loader)

        # db_infos = mmcv.load(info_path)
        with smart_open(str(info_path), "rb") as f:
            db_infos = pickle.load(f)

        self.db_infos = db_infos

        # load sample groups
        # TODO: more elegant way to load sample groups
        self.sample_groups = []
        for name, num in sample_groups.items():
            self.sample_groups.append({name: int(num)})

        self.group_db_infos = self.db_infos  # just use db_infos
        self.sample_classes = []
        self.sample_max_nums = []
        for group_info in self.sample_groups:
            self.sample_classes += list(group_info.keys())
            self.sample_max_nums += list(group_info.values())

        self.sampler_dict = {}
        for k, v in self.group_db_infos.items():
            self.sampler_dict[k] = BatchSampler(v, k, shuffle=True)
        # TODO: No group_sampling currently

    @staticmethod
    def filter_by_difficulty(db_infos, removed_difficulty):
        """Filter ground truths by difficulties.

        Args:
            db_infos (dict): Info of groundtruth database.
            removed_difficulty (list): Difficulties that are not qualified.

        Returns:
            dict: Info of database after filtering.
        """
        new_db_infos = {}
        for key, dinfos in db_infos.items():
            new_db_infos[key] = [info for info in dinfos if info["difficulty"] not in removed_difficulty]
        return new_db_infos

    @staticmethod
    def filter_by_min_points(db_infos, min_gt_points_dict):
        """Filter ground truths by number of points in the bbox.

        Args:
            db_infos (dict): Info of groundtruth database.
            min_gt_points_dict (dict): Different number of minimum points
                needed for different categories of ground truths.

        Returns:
            dict: Info of database after filtering.
        """
        for name, min_num in min_gt_points_dict.items():
            min_num = int(min_num)
            if min_num > 0:
                filtered_infos = []
                for info in db_infos[name]:
                    if info["num_points_in_gt"] >= min_num:
                        filtered_infos.append(info)
                db_infos[name] = filtered_infos
        return db_infos

    def sample_all(self, gt_bboxes, gt_labels, with_img=False):
        """Sampling all categories of bboxes.

        Args:
            gt_bboxes (np.ndarray): Ground truth bounding boxes.
            gt_labels (np.ndarray): Ground truth labels of boxes.

        Returns:
            dict: Dict of sampled 'pseudo ground truths'.

                - gt_labels_3d (np.ndarray): ground truths labels \
                    of sampled objects.
                - gt_bboxes_3d (:obj:`BaseInstance3DBoxes`): \
                    sampled ground truth 3D bounding boxes
                - points (np.ndarray): sampled points
                - group_ids (np.ndarray): ids of sampled ground truths
        """

        sampled_num_dict = {}
        sample_num_per_class = []

        for class_name, max_sample_num in zip(self.sample_classes, self.sample_max_nums):
            class_label = self.cat2label[class_name]
            # sampled_num = int(max_sample_num -
            #                   np.sum([n == class_name for n in gt_names]))
            sampled_num = int(max_sample_num - np.sum([n == class_label for n in gt_labels]))
            sampled_num = np.round(self.rate * sampled_num).astype(np.int64)
            sampled_num_dict[class_name] = sampled_num
            sample_num_per_class.append(sampled_num)

        sampled = []
        sampled_gt_bboxes = []
        avoid_coll_boxes = gt_bboxes

        for class_name, sampled_num in zip(self.sample_classes, sample_num_per_class):
            if sampled_num > 0:
                sampled_cls = self.sample_class_v2(class_name, sampled_num, avoid_coll_boxes)

                sampled += sampled_cls
                if len(sampled_cls) > 0:
                    if len(sampled_cls) == 1:
                        sampled_gt_box = sampled_cls[0]["box3d_lidar"][np.newaxis, ...]
                    else:
                        sampled_gt_box = np.stack([s["box3d_lidar"] for s in sampled_cls], axis=0)

                    sampled_gt_bboxes += [sampled_gt_box]
                    avoid_coll_boxes = np.concatenate([avoid_coll_boxes, sampled_gt_box], axis=0)

        ret = None
        if len(sampled) > 0:
            sampled_gt_bboxes = np.concatenate(sampled_gt_bboxes, axis=0)
            # center = sampled_gt_bboxes[:, 0:3]
            s_points_list = []
            s_idx_list = []
            s_imgs_list = []
            count = 0
            for info in sampled:
                file_path = os.path.join(self.data_root, info["path"]) if self.data_root else info["path"]
                results = dict(pts_filename=file_path)
                if "noriid" in info:
                    results["pts_filename"] = info["noriid"]
                    s_points = self.points_loader(results)["points"]
                else:
                    s_points = self.points_loader(results)["points"]
                s_points.translate(info["box3d_lidar"][:3])
                idx_points = count * np.ones(len(s_points), dtype=np.int)
                s_points_list.append(s_points)
                s_idx_list.append(idx_points)
                count += 1

                if with_img:
                    if "image_noriid" in info:
                        if len(info["image_noriid"]) > 0:
                            with smart_open("nori://" + info["image_noriid"], "rb") as f:
                                s_img = mmcv.imfrombytes(f.read(), "color")
                        else:
                            s_img = []
                    else:
                        if len(info["image_path"]) > 0:
                            img_path = (
                                os.path.join(self.data_root, info["image_path"])
                                if self.data_root
                                else info["image_path"]
                            )
                            s_img = mmcv.imread(img_path, "unchanged")
                            # s_img = Image.open(img_path)
                        else:
                            s_img = []
                    s_imgs_list.append(s_img)

            gt_labels = np.array([self.cat2label[s["name"]] for s in sampled], dtype=np.long)
            ret = {
                "gt_labels_3d": gt_labels,
                "gt_bboxes_3d": sampled_gt_bboxes,
                "points": s_points_list[0].cat(s_points_list),
                "points_idx": np.concatenate(s_idx_list, axis=0),
                "images": s_imgs_list,
                "group_ids": np.arange(gt_bboxes.shape[0], gt_bboxes.shape[0] + len(sampled)),
            }

        return ret

    def sample_class_v2(self, name, num, gt_bboxes):
        """Sampling specific categories of bounding boxes.

        Args:
            name (str): Class of objects to be sampled.
            num (int): Number of sampled bboxes.
            gt_bboxes (np.ndarray): Ground truth boxes.

        Returns:
            list[dict]: Valid samples after collision test.
        """
        sampled = self.sampler_dict[name].sample(num)
        sampled = copy.deepcopy(sampled)
        num_gt = gt_bboxes.shape[0]
        num_sampled = len(sampled)
        # gt_bboxes need to be defined in mmdet3d coord system.
        gt_bboxes_bv = box_np_ops.center_to_corner_box2d(gt_bboxes[:, 0:2], gt_bboxes[:, 3:5], gt_bboxes[:, 6])

        sp_boxes = np.stack([i["box3d_lidar"] for i in sampled], axis=0)
        boxes = np.concatenate([gt_bboxes, sp_boxes], axis=0).copy()

        sp_boxes_new = boxes[gt_bboxes.shape[0] :]
        sp_boxes_bv = box_np_ops.center_to_corner_box2d(sp_boxes_new[:, 0:2], sp_boxes_new[:, 3:5], sp_boxes_new[:, 6])

        total_bv = np.concatenate([gt_bboxes_bv, sp_boxes_bv], axis=0)
        coll_mat = box_collision_test(total_bv, total_bv)
        diag = np.arange(total_bv.shape[0])
        coll_mat[diag, diag] = False

        valid_samples = []
        for i in range(num_gt, num_gt + num_sampled):
            if coll_mat[i].any():
                coll_mat[i] = False
                coll_mat[:, i] = False
            else:
                valid_samples.append(sampled[i - num_gt])
        return valid_samples
