import torch
import numpy as np
import warnings
from typing import Dict, <PERSON><PERSON>
from abc import ABC
from torchvision.transforms import functional as F


class ImageAffineTransGPU(ABC):
    def __init__(
        self,
        mode: str = "train",
        img_norm: bool = False,
        img_conf: Dict = {"img_mean": [0, 0, 0], "img_std": [1, 1, 1]},
    ) -> None:
        super().__init__()
        self.mode = mode
        self.img_norm = img_norm
        self.img_conf = img_conf

    def _multisize_img_transform(self, batch_dict: Dict) -> None:
        warnings.warn(
            "Multi size img input is deprecated, consider use multiple dataset with different config instead.",
            DeprecationWarning,
        )
        img_list = batch_dict["imgs"]
        img_metas = batch_dict["img_metas"]

        item = img_list
        outer_dims = []
        while isinstance(item, list):
            outer_dims.append(len(item))
            item = item[0]
        # Batch x Time x Cameras x 3 x H x W
        N, T, C = (*outer_dims,)
        K, H, W = item.shape

        img_batch_aug = []

        for batch_idx in range(N):
            for time_idx in range(T):
                for camera_idx in range(C):
                    _ida_paras = img_metas[batch_idx]["ida_mats_detail"][0][camera_idx]
                    img_batch_aug.append(
                        self._img_transform_torch(
                            img_list[batch_idx][time_idx][camera_idx].unsqueeze(0),
                            _ida_paras["resize"],
                            _ida_paras["resize_dims"][::-1],
                            _ida_paras["crop"],
                            _ida_paras["flip"],
                            _ida_paras["rotate"],
                        ).squeeze(0)
                    )
        H_new, W_new = img_batch_aug[0].shape[-2:]
        img_batch_aug = torch.stack(img_batch_aug).view(N, T, C, K, H_new, W_new).contiguous()
        batch_dict["imgs"] = img_batch_aug

    def __call__(self, batch_dict: Dict) -> None:
        # 重构：移除side image相关功能，仅暂时保留list形式输入，如需side image可考虑通过增加额外dataset实现
        r"""
        Description:  preprocess image to tensor, and compute by GPU.
        Batch["imgs"]: Option 1 -> List[Tensor] with shape 1 x 3 x H x W .
        batch["imgs"]: Option 2 -> Tensor with shape N x Time x Cameras X Channel x W x H.
        """
        assert set(("imgs", "img_metas")).issubset(batch_dict.keys()), "batch_dict should have img and img_metas"

        if isinstance(batch_dict["imgs"], list):  # !NOTE: List[Tensor] will be deprecated in the future.
            # batch_dict_aug = [self._img_transform_torch(img) for img in batch_dict["imgs"]]
            self._multisize_img_transform(batch_dict)
        else:
            img_batch = batch_dict["imgs"]
            img_metas = batch_dict["img_metas"]

            # Batch x Time x Cameras x 3 x H x W
            N, T, C, K, H, W = img_batch.shape
            img_batch_aug = []

            for batch_idx in range(N):
                for camera_idx in range(C):
                    _ida_paras = img_metas[batch_idx]["ida_mats_detail"][0][camera_idx]
                    img_batch_aug.append(
                        self._img_transform_torch(
                            img_batch[batch_idx, :, camera_idx, ...],
                            _ida_paras["resize"],
                            _ida_paras["resize_dims"][::-1],
                            _ida_paras["crop"],
                            _ida_paras["flip"],
                            _ida_paras["rotate"],
                            img_norm=True,
                        )  # [1, 3, h, w]
                    )
            C_tmp = len(img_metas[0]["ida_mats_detail"][0])
            H_new, W_new = img_batch_aug[0].shape[-2:]
            img_batch_aug = (
                torch.stack(img_batch_aug).view(N, C_tmp, T, K, H_new, W_new).permute(0, 2, 1, 3, 4, 5).contiguous()
            )
            batch_dict["imgs"] = img_batch_aug

            # 这里最好要和 cam name 对齐，由于这里没有传入 cam_name, 只能按顺序对齐
            if "img_semantic_seg" in batch_dict:
                rv_seg = batch_dict["img_semantic_seg"]  # batch x time x cameras(1) x 1 x H x W
                _, _, num_cam, num_channel, h, w = rv_seg.shape
                rv_seg_batch_aug = []
                for batch_idx in range(N):
                    for camera_idx in range(num_cam):
                        rv_seg_batch_aug.append(
                            self._img_transform_torch(
                                rv_seg[batch_idx, :, camera_idx, ...],
                                _ida_paras["resize"],
                                _ida_paras["resize_dims"][::-1],
                                _ida_paras["crop"],
                                _ida_paras["flip"],
                                _ida_paras["rotate"],
                                img_norm=False,
                            )
                        )
                rv_seg_batch_aug = (
                    torch.stack(rv_seg_batch_aug)
                    .view(N, num_cam, T, num_channel, H_new, W_new)
                    .permute(0, 2, 1, 3, 4, 5)
                    .contiguous()
                )
                batch_dict["img_semantic_seg"] = rv_seg_batch_aug

            # tmp = img_batch_aug[0, 0, 0].cpu().permute(1, 2, 0).numpy()
            # tmp[:, :, 1] += (rv_seg_batch_aug[0, 0, 0, 0].cpu().numpy()>0)*100

    @torch.no_grad()
    def _img_transform_torch(
        self,
        img: torch.Tensor,
        resize: float = 1.0,
        resize_dims: Tuple = (0, 0),
        crop: Tuple = (0, 0, 0, 0),
        flip: bool = False,
        rotate: float = 0,
        img_norm=True,
    ) -> torch.Tensor:
        r"""
        Description:  image augmentation by torh, and compute by GPU.

        Args:
            img  [Torch.tensor] : N * C * H * W.
            resize      [float] : default 1.0.
            resize_dims [Tuple] : (0, 0).
            Crop        [Tuple] : (x1, y1, x2, y2).
            flip        [bool]  : defalut `False`

        Return:
            return torch.Tensor : N * C * H * W.

        Example::
            >>> TODO @tanfeiyang
        """

        def _robust_crop(img: torch.Tensor, crop: Tuple) -> torch.Tensor:
            r"""img shape is N x C x H x W"""
            x1, y1, x2, y2 = crop
            img_h, img_w = img.shape[2], img.shape[3]
            pad_up = max(0, -y1)
            pad_down = max(0, y2 - img_h)
            pad_left = max(0, -x1)
            pad_right = max(0, x2 - img_w)

            # left, top, right and bottom
            img = F.pad(img, (pad_left, pad_up, pad_right, pad_down), 0, "constant")

            y2 += -min(0, y1)
            y1 += -min(0, y1)
            x2 += -min(0, x1)
            x1 += -min(0, x1)

            return img[..., y1:y2, x1:x2]

        if self.mode == "train":
            interpolate_candidate = [
                F.InterpolationMode.NEAREST,
                F.InterpolationMode.BILINEAR,
                F.InterpolationMode.BICUBIC,
            ]
            index = np.random.randint(0, len(interpolate_candidate))
            interpolation = interpolate_candidate[index]
        else:
            interpolation = F.InterpolationMode.BILINEAR
        img = F.resize(img, resize_dims, interpolation=interpolation)
        img = _robust_crop(img, crop)
        if flip:
            img = F.hflip(img)
        img = F.rotate(img, rotate, interpolation=F.InterpolationMode.BILINEAR)
        if self.img_norm and img_norm:
            img = F.normalize(img.float(), self.img_conf["img_mean"], self.img_conf["img_std"])
        return img


class ImageUndistortGPU(ABC):
    def __init__(self, mode: str = "train") -> None:
        super().__init__()
        self.mode = mode

    @torch.no_grad()
    def _remap_pytorch(self, img: torch.Tensor, map1: torch.Tensor, map2: torch.Tensor) -> torch.Tensor:
        r"""
        Description: Replace cv2.remap with torch.nn.functional.grid_sample

        Args:
            img  [torch.Tensor] : Input image tensor with shape [B, C, H, W]
            map1 [torch.Tensor] : Input map1 tensor with shape  [B, H, W, 1]
            map2 [torch.Tensor] : Input map2 tensor with shape  [B, H, W, 1]

        Return:
            undistort image : torch.Tensor

        Example::
            >>> img = torch.randn(1, 3, 1080, 1920)
            >>> map1 = torch.randn(1, 1080, 1920, 1)
            >>> map2 = torch.randn(1, 1080, 1920, 1)
            >>> img_undistort = remap_pytorch(img, map1, map2)
        """

        # Define height and width
        height, width = img.shape[-2:]

        # Normalize map and permute dimensions
        map1 = 2.0 * map1 / (width - 1) - 1.0
        map2 = 2.0 * map2 / (height - 1) - 1.0

        grid = torch.stack([map1.float(), map2.float()], dim=-1)  # fix float32 on stepmind.

        return torch.nn.functional.grid_sample(img.float(), grid, align_corners=False)

    def __call__(self, batch_dict: Dict) -> None:
        imgs = batch_dict["imgs"]
        if isinstance(imgs, list):  # !NOTE: List[Tensor] will be deprecated in the future.
            img_list = imgs
            map1_list = batch_dict["map1s"]

            item = img_list
            outer_dims = []
            while isinstance(item, list):
                outer_dims.append(len(item))
                item = item[0]
            # Batch x Time x Cameras x 3 x H x W
            N, T, C = (*outer_dims,)
            K, H, W = item.shape

            for batch_idx in range(N):
                for time_idx in range(T):
                    for camera_idx in range(C):
                        img = img_list[batch_idx][time_idx][camera_idx].unsqueeze(0)
                        map1 = map1_list[batch_idx][time_idx][camera_idx].unsqueeze(0)
                        # map2 = map2_list[batch_idx][time_idx][camera_idx].unsqueeze(0)
                        img_remapped = self._remap_pytorch(img, map1[..., 0], map1[..., 1])
                        img_list[batch_idx][time_idx][camera_idx] = img_remapped.squeeze(0)
        else:
            img_batch = imgs
            map1_batch = batch_dict["map1s"]

            N, T, C, K, H, W = img_batch.shape
            img_batch = img_batch.view(N * T * C, K, H, W)
            map1_batch = map1_batch.view(N * T * C, H, W, 2)
            img_batch_remapped = (
                self._remap_pytorch(img_batch, map1_batch[..., 0], map1_batch[..., 1])
                .view(N, T, C, K, H, W)
                .contiguous()
            )

            batch_dict["imgs"] = img_batch_remapped
