import os
import cv2
import numpy as np

from perceptron.data.det3d.source.base import IMAGE_RESOLUTION, Sensor
from perceptron.utils.file_io import load_pkl
from perceptron.utils.env import get_cluster

if os.environ.get("MLP_CONSOLE_HOST"):
    intrinsic_k_path = "s3://wangningzi-data/e2e_data/sivfov_calib/fovs_intrinsic_k_dict.pkl"
else:
    intrinsic_k_path = "s3://camera-perceptron/resources/calib/fovs_intrinsic_k_dict.pkl"


class UndistortBase:
    def __init__(self, use_new_intrisic=False, undistort_para={}):
        self.use_new_intrisic = use_new_intrisic
        # for sim fov

        # self.fovs_intrinsic_k_dict = load_pkl(intrinsic_k_path)

        site = get_cluster()
        if site == "https://hh-d.brainpp.cn":
            self.fovs_intrinsic_k_dict = load_pkl("s3://wangningzi-data/perceptron_files/fovs_intrinsic_k_dict.pkl")
        elif site == "https://qy.machdrive.cn":
            self.fovs_intrinsic_k_dict = load_pkl("s3://wangningzi-data-qy/perceptron_files/fovs_intrinsic_k_dict.pkl")
        else:
            raise NotImplementedError

        self.undistort_para = undistort_para

    @staticmethod
    def _check_camera_model(name):
        assert name in [
            "fisheye",
            "pinhole",
            "undistorted",
        ], "distortion model {} not supported".format(name)

    @staticmethod
    def _fisheye_intrinsic_K(K, D, dim, balance, fov_scale):
        return cv2.fisheye.estimateNewCameraMatrixForUndistortRectify(
            K, D, dim, np.eye(3), balance=balance, fov_scale=fov_scale
        )

    @staticmethod
    def _fisheye_map(K, D, new_K, dim):
        return cv2.fisheye.initUndistortRectifyMap(
            K,
            D,
            np.eye(3),
            new_K,
            dim,
            cv2.CV_16SC2,
        )

    @staticmethod
    def _pinhole_intrinsic_K(K, D, dim, new_dim):
        return cv2.getOptimalNewCameraMatrix(
            K,
            D,
            dim,
            alpha=0.0,
            newImgSize=new_dim,
        )

    @staticmethod
    def _pinhole_map(K, D, new_K, dim):
        return cv2.initUndistortRectifyMap(
            K,
            D,
            np.eye(3),
            new_K,
            dim,
            cv2.CV_16SC2,
        )

    def _fisheye_undistort(self, K, D, dim, balance=0.0, fov_scale=1.0):
        if self.use_new_intrisic:
            new_K = self._fisheye_intrinsic_K(K, D, dim, balance, fov_scale)
        else:
            new_K = K
        map1, map2 = self._fisheye_map(
            K,
            D,
            new_K,
            dim,
        )
        return new_K, (map1, map2)
    
    def _my_fisheye_undistort(self, new_K, K, D, dim, balance=0.0, fov_scale=1.0):
        map1, map2 = self._fisheye_map(
            K,
            D,
            new_K,
            dim,
        )
        return new_K, (map1, map2)

    def _pinhole_undistort(self, K, D, dim):
        if self.use_new_intrisic:
            new_K, _ = self._pinhole_intrinsic_K(K, D, dim, dim)
        else:
            new_K = K
        map1, map2 = self._pinhole_map(K, D, new_K, dim)
        return new_K, (map1, map2)

    def _fisheye_undistort_simfov(self, K, D, dim, fov_tag, scale):
        new_K = self.fovs_intrinsic_k_dict[fov_tag].copy()
        new_K[:2] = new_K[:2] / scale
        map1, map2 = self._fisheye_map(
            K,
            D,
            new_K,
            dim,
        )
        return new_K, (map1, map2)

    def _pinhole_undistort_simfov(self, K, D, dim, fov_tag, scale):
        new_K = self.fovs_intrinsic_k_dict[fov_tag].copy()
        new_K[:2] = new_K[:2] / scale
        map1, map2 = self._pinhole_map(K, D, new_K, dim)
        return new_K, (map1, map2)

    def __call__(self, sensor_name: str, sensor: Sensor, camera_calib: dict, camera_dim_target: tuple):
        raise NotImplementedError


class UndistortStandard(UndistortBase):
    def __call__(self, sensor_name: str, sensor: Sensor, camera_calib: dict, camera_dim_target: tuple, cam_scale=1):
        self._check_camera_model(camera_calib["intrinsic"]["distortion_model"])

        intrinsic_K = np.array(camera_calib["intrinsic"]["K"]).reshape(3, 3)
        intrinsic_D = np.array(camera_calib["intrinsic"]["D"])
        camera_dim = IMAGE_RESOLUTION[sensor["resolution"]]

        scale = 1
        if camera_dim_target[0] is not None and camera_dim != camera_dim_target:
            scale = camera_dim[0] / camera_dim_target[0]

        undistort_map = {}
        if camera_calib["intrinsic"]["distortion_model"] == "undistorted":
            new_intrinsic_K = intrinsic_K
            map = (None, None)
        elif camera_calib["intrinsic"]["distortion_model"] == "fisheye":
            if cam_scale != 1:
                new_intrinsic_K = intrinsic_K.copy()
                new_intrinsic_K[0][0] = new_intrinsic_K[0][0] * cam_scale
                new_intrinsic_K[1][1] = new_intrinsic_K[1][1] * cam_scale
                new_intrinsic_K, map = self._my_fisheye_undistort(
                    new_intrinsic_K,
                    intrinsic_K,
                    intrinsic_D,
                    camera_dim,
                    balance=self.undistort_para.get("balance", 0),
                    fov_scale=self.undistort_para.get("fov_scale", 1.0),
                )
            else:
                new_intrinsic_K, map = self._fisheye_undistort(
                    intrinsic_K,
                    intrinsic_D,
                    camera_dim,
                    balance=self.undistort_para.get("balance", 0),
                    fov_scale=self.undistort_para.get("fov_scale", 1.0),
                )
        else:
            new_intrinsic_K, map = self._pinhole_undistort(intrinsic_K, intrinsic_D, camera_dim)
        undistort_map[camera_dim] = map

        if scale != 1:
            new_intrinsic_K[:2] = new_intrinsic_K[:2] / scale  # 共享地址...
            if cam_scale != 1:
                intrinsic_K[:2] = intrinsic_K[:2] / scale  # 不共享地址. 写法有问题, 太隐蔽..
            if camera_calib["intrinsic"]["distortion_model"] == "undistorted":
                map = (None, None)
            elif camera_calib["intrinsic"]["distortion_model"] == "fisheye":
                map = self._fisheye_map(intrinsic_K, intrinsic_D, new_intrinsic_K, camera_dim_target)
            else:
                map = self._pinhole_map(intrinsic_K, intrinsic_D, new_intrinsic_K, camera_dim_target)
            undistort_map[camera_dim_target] = map
        return new_intrinsic_K, undistort_map


class UndistortSimFov(UndistortBase):
    def __call__(self, sensor_name: str, sensor: Sensor, camera_calib: dict, camera_dim_target: tuple):
        """
        Calculate new_intrinsic_K and undistort_map for sim fov.
        """
        # import ipdb
        # ipdb.set_trace()
        self._check_camera_model(camera_calib["intrinsic"]["distortion_model"])

        intrinsic_K = np.array(camera_calib["intrinsic"]["K"]).reshape(3, 3)
        intrinsic_D = np.array(camera_calib["intrinsic"]["D"])
        camera_dim = IMAGE_RESOLUTION[sensor["resolution"]]

        fov_tag = sensor_name.split("_")[-1]
        assert fov_tag in self.fovs_intrinsic_k_dict

        sensor_scale = IMAGE_RESOLUTION["800w"][0] / camera_dim[0]

        undistort_map = {}
        if camera_calib["intrinsic"]["distortion_model"] == "undistorted":
            new_intrinsic_K = intrinsic_K
            map = (None, None)
        elif camera_calib["intrinsic"]["distortion_model"] == "fisheye":
            new_intrinsic_K, map = self._fisheye_undistort_simfov(
                intrinsic_K, intrinsic_D, camera_dim, fov_tag, scale=sensor_scale
            )
        else:
            new_intrinsic_K, map = self._pinhole_undistort_simfov(
                intrinsic_K, intrinsic_D, camera_dim, fov_tag, scale=sensor_scale
            )
        undistort_map[camera_dim] = map

        if camera_dim_target[0] is not None and camera_dim != camera_dim_target:
            sensor_scale = IMAGE_RESOLUTION["800w"][0] / camera_dim_target[0]
            resolution_scale = camera_dim[0] / camera_dim_target[0]

            intrinsic_K[:2] = intrinsic_K[:2] / resolution_scale
            if camera_calib["intrinsic"]["distortion_model"] == "undistorted":
                new_intrinsic_K = intrinsic_K
                map = (None, None)
            elif camera_calib["intrinsic"]["distortion_model"] == "fisheye":
                new_intrinsic_K, map = self._fisheye_undistort_simfov(
                    intrinsic_K, intrinsic_D, camera_dim_target, fov_tag, scale=sensor_scale
                )
            else:
                new_intrinsic_K, map = self._pinhole_undistort_simfov(
                    intrinsic_K, intrinsic_D, camera_dim_target, fov_tag, scale=sensor_scale
                )
            undistort_map[camera_dim_target] = map

        return new_intrinsic_K, undistort_map
