import random
from collections import defaultdict
import json

import pickle
import cv2

cv2.setNumThreads(0)
cv2.ocl.setUseOpenCL(False)
import nori2 as nori
import numpy as np
import hashlib
import msgpack

from loguru import logger
from tqdm import tqdm
from refile import smart_open

from perceptron.data.det3d.modules.utils.distributed import is_master
from perceptron.data.det3d.source.base import IMAGE_RESOLUTION
from perceptron.data.det3d.utils.functional import camera_filter, get_lidar_to_pixel
from perceptron.utils import torch_dist
from perceptron.utils.env import get_cluster
from perceptron.data.det3d.utils.redis_cache_stepmind import LocalCachedDict

from .undistort import UndistortBase, UndistortStandard

_CAMERA_MAPPING = {
    "cam_front_120": "cam_front_120",  # 地图需要, 这里需要放【第1个】，用来和 rv 监督对齐
    "cam_front_30": "cam_front_30",

    "cam_front_left_120": "cam_front_left_120",
    "cam_front_right_120": "cam_front_right_120",
}

def combine_remap(mapx1, mapy1, mapx2, mapy2):
    # mapx1 and mapy1 are the mapping matrices for the first remap
    # mapx2 and mapy2 are the mapping matrices for the second remap

    # Get the shape of the mappings
    h, w = mapx1.shape

    # Create empty matrices for the combined map
    mapy_combined = np.zeros((h, w), dtype=np.float32)
    mapx_combined = np.zeros((h, w), dtype=np.float32)

    # Compute the combined map
    # mapy_combined = mapy2[mapy1.astype(np.int32), mapx1.astype(np.int32)]
    # mapx_combined = mapx2[mapy1.astype(np.int32), mapx1.astype(np.int32)]
    #
    mapy_combined = cv2.remap(mapy2.astype(np.float32), mapx1.astype(np.float32), mapy1.astype(np.float32), interpolation=cv2.INTER_LINEAR)
    mapx_combined = cv2.remap(mapx2.astype(np.float32), mapx1.astype(np.float32), mapy1.astype(np.float32), interpolation=cv2.INTER_LINEAR)

    return mapy_combined, mapx_combined


class ImageBase:
    """
    The transformation for image undistort is generate by calibration parameters in initialisation, and decode the original image from the nori address, which is used as the input for the subsequent pipeline.
    The image output in this stage is a list sorted by camera_names, regardless of whether the image sizes of different cameras are the same or not.

    Options:
        undistort: Switch of all undistortion related functions

        target_resolution: Determine the size of the undistorted image

        undistort_func: Calculate the transformation relations from the calibration parameters. SimFov is used to project the physical camera to a hypothetical standard camera with the same intrinsics

        postpone_undistort: Option for sending original image and undistortion mapping to pipeline, such as gpu undistort
    """

    def __init__(
        self,
        car,
        camera_names,
        loader_output,
        mode,
        undistort=True,
        undistort_func=UndistortStandard,
        target_resolution="200w",
        postpone_undistort=False,
        img_warp_maps_dict=None,
        warp_tgt_json=None,
        target_extrinsic=None,
        cam120_scale=1.0,  # must be set for new data after 20250303
        map_cache="/data/cache_default",  # map cache, mem perf.
    ) -> None:
        self.car = car
        self.camera_names = camera_names
        self.loader_output = loader_output
        self.mode = mode
        self.nori_fetcher = None
        self.undistort = undistort
        self.volcano_platform = True # env.is_volcano_platform() 
        assert target_resolution in list(IMAGE_RESOLUTION.keys()) + ["original"]
        if target_resolution in IMAGE_RESOLUTION:
            self.target_resolution = IMAGE_RESOLUTION[target_resolution]
        else:
            self.target_resolution = (None, None)

        self.calibrated_sensors = self.loader_output["calibrated_sensors"]
        self.camera_name_mapping_s2h = self.loader_output["camera_name_mapping"]["standard"]
        self.raw_names = [self.camera_name_mapping_s2h[k]["hidden_name"] for k in self.camera_names]

        if self.undistort:
            if isinstance(undistort_func, dict):
                assert set(undistort_func.keys()) == set(camera_names), "existing camera without undistortion function."
                for camera_name, func_ in undistort_func.items():
                    assert camera_name in self.camera_names and issubclass(
                        func_[0], UndistortBase
                    ), "invalid undistort function input."
                self.undistort_func = undistort_func
            elif issubclass(undistort_func[0], UndistortBase):
                self.undistort_func = {camera_name: undistort_func for camera_name in self.camera_names}
            else:
                raise ("invalid undistort function format.")

            self.init_camera_undistort_flag = False
            self.postpone_undistort = postpone_undistort
        else:
            logger.warning(f"self.postpone_undistort = False")
            self.postpone_undistort = False
            self._init_camera_translation_matrix()

        self.img_warp_maps_dict = None
        self.target_extrinsic = None

        if target_extrinsic is not None:
            self.target_extrinsic = dict(np.load(target_extrinsic))
        if img_warp_maps_dict is not None:
            self.img_warp_maps_dict = dict(np.load(img_warp_maps_dict))
        self.nori_available = get_cluster() == "https://hh-d.brainpp.cn"

        # # fix 0301
        # if warp_tgt_json is not None:
        #     self.warp_tgt_dict = json.load(smart_open(warp_tgt_json, "r"))
        # else:
        #     self.warp_tgt_dict = None
        
        # if target_extrinsic is not None:
        #     self.target_extrinsic = dict(np.load(target_extrinsic))
        # else:
        #     self.target_extrinsic = None


        # add scale for newdata
        self.cam120_scale = cam120_scale

        # self.local_map_cache = LocalCachedDict("/data/cache_0523")  # perf. reduce mem cost
        # self.local_map_cache = LocalCachedDict("/data/cache_0621")  # perf. reduce mem cost
        self.local_map_cache = LocalCachedDict(map_cache)  # perf. reduce mem cost

    def _init_sensors_info(self):
        pass

    def _init_camera_translation_matrix(self):
        """用于预处理了去畸变的图像"""
        camera_info_cache = {
            "calib_param_to_index": defaultdict(int),
            "new_k": [],
            "lidar_to_pix": [],
        }
        self._init_sensors_info()
        for json_idx, sensors_info in tqdm(
            self.loader_output["calibrated_sensors"].items(),
            disable=(not is_master()),
            desc="[Init lidar to pixel translation]",
        ):
            for camera_name in self.camera_names:
                hidden_name = self._get_hidden_name(camera_name)
                sensor_info = sensors_info[hidden_name]

                if str(sensor_info) in camera_info_cache["calib_param_to_index"]:
                    map_id = camera_info_cache["calib_param_to_index"][str(sensor_info)]
                    cur_cam_info = self.calibrated_sensors[json_idx][hidden_name]
                    cur_cam_info["intrinsic"]["K"] = camera_info_cache["new_k"][map_id]
                    cur_cam_info["T_lidar_to_pixel"] = camera_info_cache["lidar_to_pix"][map_id]
                else:
                    cache_idx = len(camera_info_cache["lidar_to_pix"])
                    camera_info_cache["calib_param_to_index"][str(sensor_info)] = cache_idx
                    cur_cam_info = self.calibrated_sensors[json_idx][hidden_name]

                    intrinsic_k = np.array(cur_cam_info["intrinsic"]["K"]).reshape(3, 3)
                    lidar2pix = get_lidar_to_pixel(sensor_info, intrinsic_k)

                    cur_cam_info["T_lidar_to_pixel"] = lidar2pix

                    camera_info_cache["new_k"].append(cur_cam_info["intrinsic"]["K"])
                    camera_info_cache["lidar_to_pix"].append(lidar2pix)

    def _get_hidden_name(self, camera_name):
        hidden_name = self.camera_name_mapping_s2h[camera_name]["hidden_name"]
        return hidden_name

    def _init_camera_undistort_mapping(self):
        camera_info_cache = {
            "calib_param_to_index": defaultdict(int),
            "new_k": [],
            "undistort_map": [],
            "lidar_to_pix": [],
        }

        self._init_sensors_info()

        for json_idx, sensors_info in tqdm(
            self.loader_output["calibrated_sensors"].items(), disable=(not is_master()), desc="[Init Undistort Maps]"
        ):
            for camera_name in self.camera_names:
                hidden_name = self._get_hidden_name(camera_name)
                sensor_info = sensors_info[hidden_name]

                # 因为所有的value在新的数据去重机制下，都是同一个内存地址，因此选择改用id来判重
                if str(sensor_info) in camera_info_cache["calib_param_to_index"]:
                    map_id = camera_info_cache["calib_param_to_index"][str(sensor_info)]
                    cur_cam_info = self.calibrated_sensors[json_idx][hidden_name]
                    cur_cam_info["intrinsic"]["distortion_model"] = "undistorted"
                    cur_cam_info["intrinsic"]["K"] = camera_info_cache["new_k"][map_id]
                    cur_cam_info["T_lidar_to_pixel"] = camera_info_cache["lidar_to_pix"][map_id]
                    cur_cam_info["distortion_maps"] = camera_info_cache["undistort_map"][map_id]
                    cur_cam_info["intrinsic"]["target_resolution"] = self.target_resolution
                else:
                    func_ = self.undistort_func[camera_name]
                    # undistort_instance = None
                    if len(func_) > 1:
                        undistort_instance = func_[0](**func_[1])
                    else:
                        undistort_instance = func_[0]()
                    if camera_name == "cam_front_120":
                        new_k, undistort_map = undistort_instance(
                            sensor_name=camera_name,
                            sensor=self.camera_name_mapping_s2h[camera_name],
                            camera_calib=sensor_info,
                            camera_dim_target=self.target_resolution,
                            cam_scale=self.cam120_scale,
                        )
                    else:
                        new_k, undistort_map = undistort_instance(
                            sensor_name=camera_name,
                            sensor=self.camera_name_mapping_s2h[camera_name],
                            camera_calib=sensor_info,
                            camera_dim_target=self.target_resolution,
                        )

                    # if self.img_warp_maps_dict is not None:
                    #     map1, map2 = self.img_warp_maps_dict[camera_name]
                    #     # assert map1 is not None and map2 is not None, "Invalid remap map"
                    #     map1_new, map2_new = combine_remap(
                    #         undistort_map[self.target_resolution][0][:, :, 0],
                    #         undistort_map[self.target_resolution][0][:, :, 1],
                    #         map1,
                    #         map2,
                    #     )
                    #     undistort_map[self.target_resolution] = (
                    #         np.stack((map1_new, map2_new), axis=-1),
                    #         undistort_map[self.target_resolution][1],
                    #     )
                    cache_idx = len(camera_info_cache["undistort_map"])
                    camera_info_cache["calib_param_to_index"][str(sensor_info)] = cache_idx
                    cur_cam_info = self.calibrated_sensors[json_idx][hidden_name]
                    lidar2pix = get_lidar_to_pixel(sensor_info, new_k)
                    new_k = new_k.tolist()  # new_k.flatten().tolist()
                    cur_cam_info["intrinsic"]["distortion_model"] = "undistorted"
                    cur_cam_info["intrinsic"]["K"] = new_k
                    camera_info_cache["new_k"].append(new_k)

                    if self.target_extrinsic is not None:
                        lidar2img = np.concatenate([np.array(lidar2pix), np.array([[0.0, 0.0, 0.0, 1.0]])])
                        lidar2img_tar = self.target_extrinsic[_CAMERA_MAPPING[camera_name]]

                        campose_geom_trans_matrix = lidar2img_tar @ np.linalg.inv(lidar2img)
                        img_warp_matrix = calc_warp_between_imgs(
                            campose_geom_trans_matrix,
                            np.eye(3),  # sample_queue[0]["tran_mats_dict"]["cam2img"][i],
                            lidar2img,  # trans_ego2cam[-1],
                            self.target_resolution[0],
                            self.target_resolution[1],
                        )  # 根据目标外参计算当前的warp
                        # print(self.target_resolution)
                        map1, map2 = compute_perspective_remap(img_warp_matrix, size=self.target_resolution)  # 近似计算warp的remap
                        
                        
                        # 合并remap, 先做去畸变, 再做warp, 合并为一个remap
                        map1_new, map2_new = combine_remap(
                            map2,
                            map1,
                            undistort_map[self.target_resolution][0][:, :, 1],
                            undistort_map[self.target_resolution][0][:, :, 0],
                        )  
                        undistort_map[self.target_resolution] = (
                            np.stack((map1_new, map2_new), axis=-1),
                            undistort_map[self.target_resolution][1],
                        )  # for gpu remap

                        # ## 合并remap, 先做warp, 再做去畸变, 合并为一个remap
                        # map1_new, map2_new = combine_remap(
                        #     undistort_map[self.target_resolution][0][:, :, 0],
                        #     undistort_map[self.target_resolution][0][:, :, 1],
                        #     map1,
                        #     map2,
                        # )
                        # undistort_map[self.target_resolution] = (
                        #     np.stack((map1_new, map2_new), axis=-1),
                        #     undistort_map[self.target_resolution][1],
                        # )

                        # import pdb ; pdb.set_trace()  # undistort_map[(1920, 1080)][0].shape
                        lidar2pix = lidar2img_tar[:3, :]
                    
                    cur_cam_info["intrinsic"]["target_resolution"] = self.target_resolution

                    cur_cam_info["T_lidar_to_pixel"] = lidar2pix
                    # cur_cam_info["distortion_maps"] = undistort_map
                    camera_info_cache["lidar_to_pix"].append(lidar2pix)
                    # camera_info_cache["undistort_map"].append(undistort_map)

                    local_key = hashlib.md5(msgpack.dumps(sensor_info)).hexdigest()

                    if local_key not in self.local_map_cache:
                        self.local_map_cache[local_key] = undistort_map

                    cur_cam_info["distortion_maps"] = local_key
                    camera_info_cache["undistort_map"].append(local_key)


    @staticmethod
    def _able_to_get_image(name, sensor_data):
        is_distributed = torch_dist.is_distributed()
        local_rank = 0
        if is_distributed:
            local_rank = torch_dist.get_rank()

        Flag = True
        if name not in sensor_data:
            if local_rank == 0:
                logger.warning(f"{name} is not in {sensor_data.keys()}")
            Flag = False
        elif sensor_data[name] is None:
            if local_rank == 0:
                logger.warning(f"{name} in {sensor_data.keys()}, but has no info")
            Flag = False
        elif sensor_data[name]["nori_id"] is None:
            if local_rank == 0:
                logger.warning(f"The Nori id of {name} is None")
            Flag = False
        return Flag

    def get_images(self, idx, data_dict):
        """
        Loading image with given sample index
        """
        print(f"[DEBUG] get_images called for idx: {idx}")
        if self.nori_fetcher is None:
            print(f"[DEBUG] Creating nori fetcher...")
            self.nori_fetcher = nori.Fetcher()
        if self.undistort:
            if self.init_camera_undistort_flag is False:
                print(f"[DEBUG] Undistort maps already initialized")
                self._init_camera_undistort_mapping()   # dwj Q: 这里好像什么都没改？
                self.init_camera_undistort_flag = True

        if self.postpone_undistort:
            resize_first = True
        elif self.mode == "train" and self.target_resolution[0] is not None:
            resize_first = random.choice([True, False])
        else:
            resize_first = False

        print(f"[DEBUG] Starting image loading for {len(self.camera_names)} cameras...")
        imgs, lidar2imgs = [], []
        sensors_info = self.calibrated_sensors[self.loader_output["calibrated_sensors_id"][idx]]
        sensor_data = self.loader_output["frame_data_list"][idx]["sensor_data"]

        if camera_filter(self.raw_names, sensor_data):
            print(f"[DEBUG] Camera filter failed for idx: {idx}")
            print(f"[DEBUG] Raw camera names: {self.raw_names}")
            print(f"[DEBUG] Available sensor data keys: {list(sensor_data.keys())}")
            return None

        map1s, map2s = [], []
        for camera_name in self.camera_names:
            print(f"[DEBUG] Processing camera: {camera_name}")
            hidden_name = self._get_hidden_name(camera_name)
            if "nori_id" in sensor_data[hidden_name]:
                nori_img_id = sensor_data[hidden_name]["nori_id"]
                print(f"[DEBUG] Loading image from nori_id: {nori_img_id}")
                try:
                    if self.nori_available:
                        print(f"[DEBUG] Using nori fetcher...")
                        img = cv2.imdecode(np.frombuffer(self.nori_fetcher.get(nori_img_id), dtype=np.uint8), 1)
                    else:
                        print(f"[DEBUG] Using volume reader...")
                        vid = int(nori_img_id.split(",")[0])
                        nori_path = sensor_data[hidden_name]["nori_path"].replace("s3://", "/mnt/tf-rhea-data-bpp/")
                        vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                        img = cv2.imdecode(np.frombuffer(vreader.get(nori_img_id), dtype=np.uint8), 1)
                    print(f"[DEBUG] Successfully loaded image for {camera_name}")
                except Exception as e:
                    print(f"[DEBUG] Failed to load image for {camera_name}: {e}")
                    print(sensor_data[hidden_name], "not found")
                    return None
            else:
                print(f"[DEBUG] Loading image from file path...")
                if "s3_path" in sensor_data[hidden_name]:
                    tmp_name = "s3_path"
                elif "data_path" in sensor_data[hidden_name]:
                    tmp_name = "data_path"
                else:
                    print(f"[DEBUG] No valid path found for {camera_name}")
                    return None
                print(f"[DEBUG] Loading from {tmp_name}: {sensor_data[hidden_name][tmp_name]}")
                with smart_open(sensor_data[hidden_name][tmp_name], "rb") as f:
                    img = cv2.imdecode(np.frombuffer(f.read(), dtype=np.uint8), 1)
                cv2.cvtColor(img, cv2.COLOR_BGR2RGB, img)

            if self.mode == "train":
                interpolation = random.choice(
                    [cv2.INTER_AREA, cv2.INTER_LINEAR, cv2.INTER_NEAREST, cv2.INTER_CUBIC, cv2.INTER_LANCZOS4]
                )
            else:
                interpolation = cv2.INTER_LINEAR

            if self.undistort:
                if resize_first:
                    if img.shape[0] / img.shape[1] == self.target_resolution[1] / self.target_resolution[0]:
                        img = cv2.resize(img, self.target_resolution, interpolation=interpolation)
                    else:
                        img = img[: self.target_resolution[1], : self.target_resolution[0]]
                    image_resolution = self.target_resolution
                else:
                    resolution = self.camera_name_mapping_s2h[camera_name]["resolution"]
                    image_resolution = IMAGE_RESOLUTION[resolution]

                maps_key = f"{sensors_info[hidden_name]['distortion_maps']}.{image_resolution}"
                map1, map2 = self.local_map_cache[maps_key]  # perf. reduce mem cost
                map1s.append(map1)
                map2s.append(map2)

            T_lidar_to_pixel = sensors_info[hidden_name]["T_lidar_to_pixel"]
            lidar2img = np.concatenate([np.array(T_lidar_to_pixel), np.array([[0.0, 0.0, 0.0, 1.0]])])
            assert lidar2img is not None, "Incorrect camera name in image info"

            imgs.append(img)
            lidar2imgs.append(np.array(lidar2img, dtype=np.float32))

        data_dict["imgs"] = imgs
        data_dict["lidar2imgs"] = np.stack(lidar2imgs)
        if self.undistort and self.postpone_undistort:
            data_dict["map1s"] = map1s
            data_dict["map2s"] = map2s

        return data_dict


def compute_perspective_remap(M, size):
    """
    计算用于透视变换的映射表 map1 和 map2。

    :param M: 透视变换矩阵 (3x3)
    :param size: 目标图像的尺寸 (宽度, 高度)
    :return: (map1, map2) 映射表
    """
    width, height = size
    map1 = np.zeros((height, width), dtype=np.float32)
    map2 = np.zeros((height, width), dtype=np.float32)

    # 创建坐标网格
    x_indices, y_indices = np.meshgrid(np.arange(width), np.arange(height))

    # 计算透视变换
    pts = np.stack((x_indices.ravel(), y_indices.ravel(), np.ones_like(x_indices.ravel())), axis=1)
    src_pts = np.dot(np.linalg.inv(M), pts.T).T
    src_pts /= src_pts[:, 2, np.newaxis]  # 归一化

    map2 = src_pts[:, 0].reshape(height, width)
    map1 = src_pts[:, 1].reshape(height, width)

    return map1, map2


def calc_warp_between_imgs(campose_geom_trans_matrix, K, ego2cam_proj, W, H, ego_voxels=None):
    try:
        if ego_voxels is None:
            # x_coord, y_coord, z_coord = np.mgrid[-15:15, 0:100, -10:10]  # lidar-coord! map-front-4v
            # x_coord, y_coord, z_coord = np.mgrid[-15:15, 0:100, -5:5]  # lidar-coord! map-front-4v
            x_coord, y_coord, z_coord = np.mgrid[-15:15, 0:100, -1:1]  # lidar-coord! map-front-4v
            ego_voxels = np.stack((x_coord, y_coord, z_coord), axis=-1).reshape(-1, 3)
            ego_voxels = np.c_[ego_voxels, np.ones(len(ego_voxels))]

        cam_voxels = (ego2cam_proj @ ego_voxels.T).T  # (n, 4)
        tgt_cam_voxels = (campose_geom_trans_matrix @ cam_voxels.T).T

        src_pixels = (K @ cam_voxels[:, :3].T).T
        dst_pixels = (K @ tgt_cam_voxels[:, :3].T).T

        src_pixels[:, :2] = src_pixels[:, :2] / src_pixels[:, 2:3]
        dst_pixels[:, :2] = dst_pixels[:, :2] / dst_pixels[:, 2:3]

        depth_filter = (src_pixels[:, 2] > 0) & (dst_pixels[:, 2] > 0)
        width_filter = ((src_pixels[:, 0] > 0) & (src_pixels[:, 0] < W)) & (
            (dst_pixels[:, 0] > 0) & (dst_pixels[:, 0] < W)
        )
        height_filter = ((src_pixels[:, 1] > 0) & (src_pixels[:, 1] < H)) & (
            (dst_pixels[:, 1] > 0) & (dst_pixels[:, 1] < H)
        )
        filters = depth_filter & width_filter & height_filter

        src_pixels = src_pixels[filters].astype(np.float32)[:, :2]
        dst_pixels = dst_pixels[filters].astype(np.float32)[:, :2]

        warp_matrix, valid_pts = cv2.findHomography(src_pixels, dst_pixels, cv2.RANSAC, 2.0)
        warp_matrix_inv = np.linalg.inv(warp_matrix)  # 通过求逆来判断是否合格
        # print(K)
        # print(warp_matrix)
        # # import pdb; pdb.set_trace()
        if np.count_nonzero(np.isnan(warp_matrix_inv)):
            return np.eye(3)
        # print(sum(valid_pts), dst_pixels.shape)
        return warp_matrix
    except Exception:
        return np.eye(3)
