import random
from collections import defaultdict
from copy import deepcopy
from tqdm import tqdm

import cv2
import nori2 as nori
import numpy as np
import torch
from pyquaternion import Quaternion
from refile import smart_open, smart_path_join, smart_exists


from perceptron.data.det3d.modules.utils.distributed import is_master
from perceptron.data.det3d.source.base import IMAGE_RESOLUTION
from perceptron.data.det3d.utils.functional import camera_filter, get_lidar_to_pixel
from perceptron.utils.file_io import load_pkl
from .undistort import intrinsic_k_path
from .base import ImageBase

import bisect


class SensorCalibrationInterface(object):
    """
    copied from perceptron.data.map3d.bezier file
    """

    def __init__(self, calibrated_sensors):
        self.calibrated_sensors = calibrated_sensors

    def get_lidar2ego_trans(self, inverse=False):
        if "lidar2ego" in self.calibrated_sensors:
            lidar2ego_params = self.calibrated_sensors["lidar2ego"].get(
                "transform", self.calibrated_sensors["lidar2ego"]
            )
        else:
            lidar2ego_params = self.calibrated_sensors["lidar_ego"]["extrinsic"]["transform"]
        translation = np.array(list(lidar2ego_params["translation"].values()))
        rotation = Quaternion(list(lidar2ego_params["rotation"].values()))
        trans = self.transform_matrix(translation, rotation, inverse=inverse)
        return trans

    def get_lidar2cam_trans(self, img_key, inverse=False):
        lidar2cam_params = self.calibrated_sensors[img_key]["extrinsic"].get(
            "transform", self.calibrated_sensors[img_key]["extrinsic"]
        )
        translation = np.array(list(lidar2cam_params["translation"].values()))
        rotation = Quaternion(list(lidar2cam_params["rotation"].values()))
        trans_lidar2cam = self.transform_matrix(translation, rotation, inverse=inverse)
        return trans_lidar2cam

    def get_ego2cam_trans(self, img_key, inverse=False):
        if not inverse:
            trans_ego2lidar = self.get_lidar2ego_trans(inverse=True)
            trans_lidar2cam = self.get_lidar2cam_trans(img_key, inverse=False)
            return trans_lidar2cam @ trans_ego2lidar  # 可以把 ego 坐标系下的点转换到 cam 坐标系下
        else:
            trans_lidar2ego = self.get_lidar2ego_trans(inverse=False)
            trans_cam2lidar = self.get_lidar2cam_trans(img_key, inverse=True)
            return trans_lidar2ego @ trans_cam2lidar

    def get_distortion_status(self, img_key):
        return self.calibrated_sensors[img_key]["intrinsic"]["distortion_model"]

    def get_camera_intrinsic(self, img_key):
        cam_intrinsic_params = self.calibrated_sensors[img_key]["intrinsic"]
        _K = np.array(cam_intrinsic_params["K"]).reshape(3, 3)
        _D = np.array(cam_intrinsic_params["D"])
        mode = np.array(cam_intrinsic_params["distortion_model"])
        return _K, _D, mode

    def get_cam2img_trans(self, img_key):
        return self.get_camera_intrinsic(img_key)[0]

    @staticmethod
    def transform_matrix(translation, rotation, inverse=False):
        trans_mat = np.eye(4)
        if not inverse:
            trans_mat[:3, :3] = rotation.rotation_matrix
            trans_mat[:3, 3] = np.transpose(np.array(translation))
        else:
            trans_mat[:3, :3] = rotation.rotation_matrix.T
            trans_mat[:3, 3] = trans_mat[:3, :3].dot(np.transpose(-np.array(translation)))
        return trans_mat

    def get_cam_resolution(self, img_key):
        w, h = self.calibrated_sensors[img_key]["intrinsic"]["resolution"]  # resolution一定要是w,h
        return np.array([w, h])  # (2, )

    def project_xyz2uv(self, xyz_pts, img_key, return_format="tensor"):
        """
        :param xyz_pts: (N, 3)
        :param img_key: str
        :param return_format:
        :return:
        """
        assert isinstance(xyz_pts, (np.ndarray, torch.Tensor))
        assert len(xyz_pts.shape) == 2 and xyz_pts.shape[-1] == 3
        assert return_format in ["numpy", "tensor"]
        # 通过内外参计算xyz投影到uv平面上的坐标
        xyz_pts = xyz_pts if isinstance(xyz_pts, np.ndarray) else xyz_pts.cpu().data.numpy()
        xyz_pts = np.concatenate([xyz_pts, np.ones(xyz_pts.shape[0])[:, None]], axis=-1)  # (N, 4)
        ego2cam_trans = self.get_ego2cam_trans(img_key, inverse=False)
        cam_pts = ego2cam_trans @ xyz_pts.T
        cam_pts = cam_pts[:3]
        cam2img_trans = self.get_cam2img_trans(img_key)
        uv_pts = cam2img_trans @ cam_pts
        uv_pts = uv_pts[:2, :] / uv_pts[2, :]
        uv_pts = uv_pts if return_format == "numpy" else torch.from_numpy(uv_pts)
        # 保证xyz投影到图像上的有效性
        w, h = self.calibrated_sensors[img_key]["intrinsic"]["resolution"]  # resolution一定要是w,h
        valid_indices_u = np.bitwise_and(uv_pts[0] >= 0, uv_pts[0] < w)
        valid_indices_v = np.bitwise_and(uv_pts[1] >= 0, uv_pts[1] < h)
        valid_indices_d = (cam_pts[2] > 0).astype(bool)
        valid_indices_uv = np.bitwise_and(valid_indices_u, valid_indices_v)
        valid_indices = np.bitwise_and(valid_indices_uv, valid_indices_d)
        return uv_pts.T, valid_indices


class ImageSimFov(ImageBase):
    def _init_camera_translation_matrix(self):
        """用于预处理了去畸变的图像"""
        camera_info_cache = {
            "calib_param_to_index": defaultdict(int),
            "new_k": [],
            "lidar_to_pix": [],
        }
        self._init_sensors_info()
        for json_idx, sensors_info in tqdm(
            self.calibrated_sensors.items(),
            disable=(not is_master()),
            desc="[Init lidar to pixel translation]",
        ):
            for camera_name in self.camera_names:
                hidden_name = self._get_hidden_name(camera_name)
                sensor_info = sensors_info[hidden_name]

                if str(sensor_info) in camera_info_cache["calib_param_to_index"]:
                    map_id = camera_info_cache["calib_param_to_index"][str(sensor_info)]
                    cur_cam_info = self.calibrated_sensors[json_idx][hidden_name]
                    cur_cam_info["intrinsic"]["K"] = camera_info_cache["new_k"][map_id]
                    cur_cam_info["T_lidar_to_pixel"] = camera_info_cache["lidar_to_pix"][map_id]
                else:
                    cache_idx = len(camera_info_cache["lidar_to_pix"])
                    camera_info_cache["calib_param_to_index"][str(sensor_info)] = cache_idx
                    cur_cam_info = self.calibrated_sensors[json_idx][hidden_name]
                    if "_sim_fov" in camera_name:
                        sim_key = camera_name.split("sim_")[1]
                        intrinsic_k = self.fovs_intrinsic_k_dict[sim_key]
                        cur_cam_info["intrinsic"]["K"] = intrinsic_k.tolist()
                    else:
                        intrinsic_k = np.array(cur_cam_info["intrinsic"]["K"]).reshape(3, 3)
                    lidar2pix = get_lidar_to_pixel(sensor_info, intrinsic_k)

                    lidar2pix = np.array(lidar2pix)
                    lidar2pix[:2] /= cur_cam_info["intrinsic"]["resolution"][0] / 1920
                    lidar2pix = lidar2pix.tolist()

                    cur_cam_info["T_lidar_to_pixel"] = lidar2pix

                    camera_info_cache["new_k"].append(cur_cam_info["intrinsic"]["K"])
                    camera_info_cache["lidar_to_pix"].append(lidar2pix)

    def _init_sensors_info(self):
        """
        clone sensor informataion for "cam_xx_sim_fov"
        cameras/standard name  | hidden name   | sensor_info[name]
        cam_front_60          | camera_15     | camera_15
        cam_front_60_sim_fov  | camera_15     | camera_15_sim_fov
        """
        self.fovs_intrinsic_k_dict = load_pkl(intrinsic_k_path)
        for json_idx, sensors_info in self.calibrated_sensors.items():
            for camera_name in self.camera_names:
                hidden_name = self.camera_name_mapping_s2h[camera_name]["hidden_name"]
                if "_sim_fov" in camera_name:
                    hidden_name_new = hidden_name + "_sim_fov" + camera_name.split("_sim_fov")[1]
                    sensors_info[hidden_name_new] = deepcopy(sensors_info[hidden_name])
                    sensors_info[hidden_name_new]["sensor_name"] = hidden_name_new
                else:
                    sensors_info[hidden_name]["sensor_name"] = camera_name

    def _get_hidden_name(self, camera_name):
        hidden_name = self.camera_name_mapping_s2h[camera_name]["hidden_name"]
        if "_sim_fov" in camera_name:
            hidden_name = hidden_name + "_sim_fov" + camera_name.split("_sim_fov")[1]
        return hidden_name

    def get_images(self, idx, data_dict):
        """
        Loading image and intrinsic mats with given sample index
        """
        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()
        if self.undistort:
            if self.init_camera_undistort_flag is False:
                self._init_camera_undistort_mapping()
                self.init_camera_undistort_flag = True

        if self.postpone_undistort:
            resize_first = True
        elif self.mode == "train" and self.target_resolution[0] is not None:
            resize_first = random.choice([True, False])
        else:
            resize_first = False

        imgs, lidar2imgs, intrin_mats = [], [], []
        sensors_info = self.calibrated_sensors[self.loader_output["calibrated_sensors_id"][idx]]
        sensor_data = self.loader_output["frame_data_list"][idx]["sensor_data"]


        if camera_filter(self.raw_names, sensor_data):
            print("bad sensor!!")
            return None

        calibrator = SensorCalibrationInterface(sensors_info)
        trans_ego2cam, trans_cam2ego, trans_cam2img, trans_lidar2ego, resolution = [], [], [], [], []
        map1s, map2s = [], []
        for camera_name in self.camera_names:
            # hidden_name = self._get_hidden_name(camera_name)
            # img = self._get_single_img(sensor_data, camera_name)
            # if img is None:
            #     return None

            hidden_name = self.camera_name_mapping_s2h[camera_name]["hidden_name"]
            
            if hidden_name not in sensor_data:
                json_collections = self.loader_output["json_collection"]
                json_idx = bisect.bisect_right(self.loader_output["frame_data_list"].cumulative_sizes, idx)
                json_path = self.loader_output["json_collection"][json_idx]
                print("bad sensor, bad json: {}".format(json_path))
                print(sensor_data[hidden_name], "not found")
                return None

            data_mode = data_dict["data_mode"]  # nori or jpg
            
            if data_mode == "nori": 
                # print("nori mode")
                # 读nori
                if "cam_front_120" in sensor_data:
                    nori_id = sensor_data["cam_front_120"]["nori_id"]
                else:
                    nori_id = None
                nori_img_id = sensor_data[hidden_name]["nori_id"]
                # train v3.0
                try:
                    vid = int(nori_img_id.split(",")[0])
                    nori_path = sensor_data[hidden_name]["nori_path"].replace("/mnt/tf-rhea-data-bpp/", "s3://").replace("s3://", data_dict["gpfs_prefix"])
                    if not smart_exists(nori_path):
                        json_collections = self.loader_output["json_collection"]
                        json_idx = bisect.bisect_right(self.loader_output["frame_data_list"].cumulative_sizes, idx)
                        json_path = self.loader_output["json_collection"][json_idx]
                        print("mode nori, bad json: {}".format(json_path))
                        return None
                    vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                    img = cv2.imdecode(np.frombuffer(vreader.get(nori_img_id), dtype=np.uint8), 1)
                    # print("nori", img.shape)
                except Exception:
                    json_collections = self.loader_output["json_collection"]
                    json_idx = bisect.bisect_right(self.loader_output["frame_data_list"].cumulative_sizes, idx)
                    json_path = self.loader_output["json_collection"][json_idx]
                    print("mode nori, bad json: {}".format(json_path))
                    print(sensor_data[hidden_name], "not found")
                    return None
            elif data_mode == "qyjpg":
                # 庆阳数据
                if "cam_front_120" not in sensor_data:
                    return None
                nori_id = sensor_data["cam_front_120"]["nori_id"]
                try:
                    nori_img_id = sensor_data[hidden_name]["nori_id"]
                    nori_path = sensor_data[hidden_name]["nori_path"].replace("s3://", data_dict["gpfs_prefix"])
                    if not smart_exists(nori_path):
                        return None
                    img_path = smart_path_join(nori_path, nori_img_id)
                    with smart_open(img_path, "rb") as f:
                        img = cv2.imdecode(np.frombuffer(f.read(), dtype=np.uint8), 1)
                except Exception:
                    json_collections = self.loader_output["json_collection"]
                    json_idx = bisect.bisect_right(self.loader_output["frame_data_list"].cumulative_sizes, idx)
                    json_path = self.loader_output["json_collection"][json_idx]
                    print("mode qyjpg, bad json: {}".format(json_path))
                    print(sensor_data[hidden_name], img_path, "not found")
                    return None
            elif data_mode == "qyjpg-jl":
                # 0330后static下的jpg数据
                if "cam_front_120" not in sensor_data:
                    return None
                nori_id = sensor_data["cam_front_120"]["nori_id"]
                try:
                    nori_img_id = sensor_data[hidden_name]["nori_id"]
                    nori_path = sensor_data[hidden_name]["nori_path"].replace("s3://", data_dict["gpfs_prefix"])
                    img_path = smart_path_join(nori_path, nori_img_id)
                    with smart_open(img_path, "rb") as f:
                        img = cv2.imdecode(np.frombuffer(f.read(), dtype=np.uint8), 1)
                        img = cv2.resize(img, (1920, 1080))
                except Exception:
                    print(sensor_data[hidden_name], img_path, "not found")
                    return None
            elif data_mode == "jpg":
                # 直接读jpg, 高精
                nori_id = None
                if "s3_path" in sensor_data[hidden_name]:
                    # print("s3 path")
                    try:
                        img_path = sensor_data[hidden_name]["s3_path"].replace("s3://", data_dict["gpfs_prefix"])
                        if not smart_exists(img_path):
                            json_collections = self.loader_output["json_collection"]
                            json_idx = bisect.bisect_right(self.loader_output["frame_data_list"].cumulative_sizes, idx)
                            json_path = self.loader_output["json_collection"][json_idx]
                            print("mode hd jpg, bad json: {}".format(json_path))
                            return None
                        with smart_open(img_path, "rb") as f:
                            img = cv2.imdecode(np.frombuffer(f.read(), dtype=np.uint8), 1)
                        if not data_dict["save_rgb"]:
                            cv2.cvtColor(img, cv2.COLOR_BGR2RGB, img)  # jira图像rb通道颠倒,需要该操作
                    except:
                        json_collections = self.loader_output["json_collection"]
                        json_idx = bisect.bisect_right(self.loader_output["frame_data_list"].cumulative_sizes, idx)
                        json_path = self.loader_output["json_collection"][json_idx]
                        print("mode qyjpg, bad json: {}".format(json_path))
                        print(img_path, data_dict["json_path"], "fail..")
                        return None
                else:
                    json_collections = self.loader_output["json_collection"]
                    json_idx = bisect.bisect_right(self.loader_output["frame_data_list"].cumulative_sizes, idx)
                    json_path = self.loader_output["json_collection"][json_idx]
                    print("mode qyjpg no s3_path, bad json: {}".format(json_path))
                    print("no valid path")
                    return None

            if img is None:
                print(img_path, "is None!!!!!!!!!!!!!")
                return None

            if self.mode == "train":
                interpolation = random.choice(
                    [cv2.INTER_AREA, cv2.INTER_LINEAR, cv2.INTER_NEAREST, cv2.INTER_CUBIC, cv2.INTER_LANCZOS4]
                )
            else:
                interpolation = cv2.INTER_LINEAR

            if self.undistort:
                if resize_first:
                    if img.shape[0] / img.shape[1] == self.target_resolution[1] / self.target_resolution[0]:
                        img = cv2.resize(img, self.target_resolution, interpolation=interpolation)
                    else:
                        img = img[: self.target_resolution[1], : self.target_resolution[0]]
                    image_resolution = self.target_resolution
                else:
                    resolution_key = self.camera_name_mapping_s2h[camera_name]["resolution"]
                    image_resolution = IMAGE_RESOLUTION[resolution_key]
                # (map1, map2) = sensors_info[hidden_name]["distortion_maps"][image_resolution]
                maps_key = f"{sensors_info[hidden_name]['distortion_maps']}.{image_resolution}"
                map1, map2 = self.local_map_cache[maps_key]  # perf. reduce mem cost

                if not self.postpone_undistort:
                    img = cv2.remap(img, map1, map2, interpolation)
                else:
                    map1s.append(map1)
                    map2s.append(map2)
            else:
                image_resolution = self.target_resolution

            T_lidar_to_pixel = sensors_info[hidden_name]["T_lidar_to_pixel"]
            lidar2img = np.concatenate([np.array(T_lidar_to_pixel), np.array([[0.0, 0.0, 0.0, 1.0]])])
            assert lidar2img is not None, "Incorrect camera name in image info"
            intrin_mat = np.array(sensors_info[hidden_name]["intrinsic"]["K"]).reshape(3, 3)
            imgs.append(img)
            
            lidar2imgs.append(np.array(lidar2img, dtype=np.float32))

            trans_ego2cam.append(calibrator.get_ego2cam_trans(hidden_name, inverse=False))
            trans_cam2ego.append(calibrator.get_ego2cam_trans(hidden_name, inverse=True))

            intrin_mats.append(np.array(intrin_mat, dtype=np.float32))

            trans_cam2img.append(calibrator.get_cam2img_trans(hidden_name))
            trans_lidar2ego.append(calibrator.get_lidar2ego_trans(inverse=False))
            resolution.append(image_resolution)

        tran_mats_dict = dict(
            cam2img=np.stack(trans_cam2img),
            cam2ego=np.stack(trans_cam2ego),
            ego2cam=np.stack(trans_ego2cam),
            lidar2ego=np.stack(trans_lidar2ego),
            cam_resolution=np.stack(resolution),
        )
        data_dict["imgs"] = imgs
        data_dict["lidar2imgs"] = np.stack(lidar2imgs)
        data_dict["intrin_mats"] = np.stack(intrin_mats)
        data_dict["tran_mats_dict"] = tran_mats_dict
        if self.undistort and self.postpone_undistort:
            data_dict["map1s"] = map1s
            data_dict["map2s"] = map2s
        data_dict['nori_id'] = nori_id
        return data_dict
