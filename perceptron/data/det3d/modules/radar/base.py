from perceptron.utils.env import get_cluster


class RadarBase:
    def __init__(
        self,
        car=None,
        radar_key_list=["0", "1", "2", "3", "4"],
        loader_output=None,
        radar_mode="mlp",
        mode="train",
        with_virtual_radar=False,
        radar_mv2center=False,
    ):
        self.car = car
        self.radar_key_list = radar_key_list
        self.loader_output = loader_output
        self.mode = mode
        self.radar_mode = radar_mode
        self.nori_fetcher = None
        self.with_virtual_radar = with_virtual_radar
        self.radar_mv2center = radar_mv2center
        self.nori_available = get_cluster() == "https://hh-d.brainpp.cn"

    # only support load key frame
    def get_radars(self, idx, data_dict):
        NotImplemented
