import io
import nori2 as nori
import numpy as np
import bisect
from numpy.lib import recfunctions as rfn
from scipy.spatial.transform import Rotation as R

from loguru import logger
from perceptron.utils import torch_dist

# import megfile
# import refile
# from perceptron.utils import torch_dist, env

from perceptron.utils.file_io import load_pkl
from perceptron.utils.env import get_cluster
from refile import smart_open, smart_path_join, smart_exists

# 当前文件里原有的 ego 全部改为 gnss, 另新增真实的 gnss
import quaternion

def get_lidar_ego_rt(calib):
    return get_rt(calib)

def get_rt(calib):
    if "extrinsic" in calib:
        param = calib["extrinsic"]
    else:
        param = calib

    if "transform" in param:
        rota = param["transform"]["rotation"]
        tran = param["transform"]["translation"]
    else:
        rota = param["rotation"]
        tran = param["translation"]
    q = np.quaternion(rota["w"], rota["x"], rota["y"], rota["z"])
    t = np.array([tran["x"], tran["y"], tran["z"]]).astype(np.float32)
    r = quaternion.as_rotation_matrix(q).astype(np.float32)
    rt = np.eye(4)
    rt[:3, :3] = r
    rt[:3, 3] = t
    return rt

class LidarBase:
    def __init__(
        self,
        car,
        lidar_names,
        loader_output,
        mode,
        referen_lidar="middle_lidar",
        pc_fields=["x", "y", "z", "i"],
        used_echo_id=[1],
        lidar_sweeps_idx=[-2, -1],
        lidar_with_timestamp=False,
        lidar_ids=[],
    ) -> None:
        self.load_points = True
        if not lidar_names:
            logger.warning("Empty lidar_names. lidar points will not be loaded")
            self.load_points = False
        self.car = car
        self.lidar_names = lidar_names
        self.loader_output = loader_output
        self.mode = mode
        self.nori_fetcher = None
        self.referen_lidar = referen_lidar
        self.pc_fields = pc_fields
        self.used_echo_id = used_echo_id
        self.lidar_sweeps_idx = lidar_sweeps_idx
        self.lidar_with_timestamp = lidar_with_timestamp
        self.lidar_ids = lidar_ids
        self.nori_available = get_cluster() == "https://hh-d.brainpp.cn"
        if lidar_ids:
            assert self.pc_fields[-1] == "lidar_id", "when use lidar_ids, lidar_id should in pc_fields"
        self.volcano_platform = True # env.is_volcano_platform()

    @staticmethod
    def _able_to_get_lidar(name, sensor_data):
        is_distributed = torch_dist.is_distributed()
        local_rank = 0
        if is_distributed:
            local_rank = torch_dist.get_rank()

        Flag = True
        if name not in sensor_data:
            if local_rank == 0:
                logger.warning(f"{name} is not in {sensor_data.keys()}")
            Flag = False
        elif sensor_data[name] is None:
            if local_rank == 0:
                logger.warning(f"{name} in {sensor_data.keys()}, but has no info")
            Flag = False
        elif "s3_path" not in sensor_data[name]:
            # update 04.17, qy-lidar
            if "nori_id" not in sensor_data[name]:
                sensor_data[name]["nori_id"] = "{}.npy".format(sensor_data[name]["timestamp"])

            if "nori_id" not in sensor_data[name] or sensor_data[name]["nori_id"] is None:
                if local_rank == 0:
                    logger.warning(f"The Nori id of {name} is None")
                Flag = False

        return Flag

    def _get_point_cloud(self, idx, lidar_names, data_dict):
        """
        Loading point cloud with given sample index

        Refer to https://wiki.megvii-inc.com/pages/viewpage.action?pageId=316705342 for details about the point cloud format.

        Args:
            idx (int): Sampled index
        Returns:
            point cloud (Dict[str, np.ndarray]): (N, 4), (x-y-z-reflection)
        """
        lidars = []
        sensor_data = self.loader_output["frame_data_list"][idx]["sensor_data"]
        for lidar_name in lidar_names:
            if not self._able_to_get_lidar(lidar_name, sensor_data):
                return None
            
            if data_dict["data_mode"] == "qyjpg":
                qy_lidar_path = "{}/{}".format(sensor_data[lidar_name]["nori_path"], sensor_data[lidar_name]["nori_id"]) 
                qy_lidar_path = qy_lidar_path.replace("s3://", data_dict["gpfs_prefix"])  # /mnt/acceldata/static/
                try:
                    pc_data_raw = np.load(smart_open(qy_lidar_path, "rb"))
                except:
                    print(f"{qy_lidar_path} not found! mode: qyjpg")
                    return None
                pc_data = rfn.structured_to_unstructured(pc_data_raw[np.array(self.pc_fields)])
            elif data_dict["data_mode"] == "jpg":
                qy_lidar_path = sensor_data[lidar_name]["s3_path"]
                qy_lidar_path = qy_lidar_path.replace("s3://", data_dict["gpfs_prefix"])  # /mnt/acceldata/static/
                pc_data_raw = np.load(smart_open(qy_lidar_path, "rb"))
                pc_data = rfn.structured_to_unstructured(pc_data_raw[np.array(self.pc_fields)])
                # # 通常为id=4的模式, 需要rslidar->lidar
                calib = self.loader_output["calibrated_sensors"][0]
                rslidar2lidar_rt = get_lidar_ego_rt(calib["front_lidar"])
                homo_pc_data_rslidar = np.concatenate([pc_data[:, :3], np.ones((pc_data.shape[0], 1))], axis=1)
                homo_pc_data_lidar = (rslidar2lidar_rt @ homo_pc_data_rslidar.T).T
                pc_data[:,:3] = homo_pc_data_lidar[:, :3]
            elif data_dict["data_mode"] == "nori":
                nori_lidar_id = sensor_data[lidar_name]["nori_id"]
                vid = int(nori_lidar_id.split(",")[0])
                nori_path = sensor_data[lidar_name]["nori_path"].replace("s3://", data_dict["gpfs_prefix"])
                vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                data = vreader.get(sensor_data["fuser_lidar"]["nori_id"])
                pc_data_raw = np.load(io.BytesIO(data)).copy()
                pc_data = rfn.structured_to_unstructured(pc_data_raw[np.array(self.pc_fields)])
            else:
                print("check mode.., not qy")
                return None

            pc_data = pc_data[np.isin(pc_data_raw["echo_id"], self.used_echo_id)]
            if self.lidar_ids:
                pc_data_list = []
                for lidar_id in self.lidar_ids:
                    pc_data_list.append(pc_data[pc_data[:, -1] == lidar_id][:, :-1])

                pc_data = np.concatenate(pc_data_list)
            # import ipdb;ipdb.set_trace()
            # add timestamp in pointcloud
            if self.lidar_with_timestamp:
                timestamp = self._get_timestamp(idx, lidar_name)
                time = np.ones((pc_data.shape[0], 1)) * timestamp
                pc_data = np.concatenate((pc_data, time), axis=1)
            lidars.append(pc_data)
        return lidars

    @staticmethod
    def _get_lidar_to_gnss_rotate_and_translation(lidar_to_gnss_info):
        # NOTE：数据json需要批量修正
        if "lidar_gnss" in lidar_to_gnss_info.keys():
            trans = lidar_to_gnss_info["lidar_gnss"]["transform"]["translation"]
            quan = lidar_to_gnss_info["lidar_gnss"]["transform"]["rotation"]
        if "extrinsic" in lidar_to_gnss_info.keys():
            trans = lidar_to_gnss_info["extrinsic"]["transform"]["translation"]
            quan = lidar_to_gnss_info["extrinsic"]["transform"]["rotation"]
        trans = np.array([trans["x"], trans["y"], trans["z"]], dtype=np.float64)
        quan = np.array([quan["x"], quan["y"], quan["z"], quan["w"]], dtype=np.float64)
        rot_mat = R.from_quat(quan).as_matrix()
        return rot_mat, trans

    @staticmethod
    def _get_lidar_to_ego_rotate_and_translation(lidar_to_ego_info):
        trans = lidar_to_ego_info["lidar_ego"]["extrinsic"]["transform"]["translation"]
        quan = lidar_to_ego_info["lidar_ego"]["extrinsic"]["transform"]["rotation"]
        trans = np.array([trans["x"], trans["y"], trans["z"]], dtype=np.float64)
        quan = np.array([quan["x"], quan["y"], quan["z"], quan["w"]], dtype=np.float64)
        rot_mat = R.from_quat(quan).as_matrix()
        return rot_mat, trans

    @staticmethod
    def _get_lidar_pose_rotate_and_translation(lidar_pose_info):
        trans = lidar_pose_info["lidar_pose"]["translation"]
        quan = lidar_pose_info["lidar_pose"]["rotation"]
        trans = np.array([trans["x"], trans["y"], trans["z"]], dtype=np.float64)
        quan = np.array([quan["x"], quan["y"], quan["z"], quan["w"]], dtype=np.float64)
        rot_mat = R.from_quat(quan).as_matrix()
        return rot_mat, trans

    @staticmethod
    def _get_gnss_to_world_rotate_and_translation(sensor_data):
        # 之前private data初期ins 数据有误，因此存的是odom 数据，之后 ins 数据正确后，改存ins 数据，更多信息见：
        # https://wiki.megvii-inc.com/pages/viewpage.action?pageId=312323474
        if "odom_data" in sensor_data:
            odom_data = sensor_data["odom_data"]
            trans = odom_data["pose"]["pose"]["position"]  # dict
            quan = odom_data["pose"]["pose"]["orientation"]  # dict
        elif "ins_data" in sensor_data:
            ins_data = sensor_data["ins_data"]
            trans = ins_data["localization"]["position"]  # dict
            quan = ins_data["localization"]["orientation"]  # dict
        else:
            raise ValueError(f"{sensor_data['frame_id']} lack odom/ins data")
        trans = np.array([trans["x"], trans["y"], trans["z"]], dtype=np.float64)
        quan = np.array([quan["x"], quan["y"], quan["z"], quan["w"]], dtype=np.float64)
        rot_mat = R.from_quat(quan).as_matrix()
        return rot_mat, trans

    def get_lidar_info(self, idx):
        # current lidar to gnss extrinsics
        lidar2gnss = np.eye(4)
        scene_id = bisect.bisect_right(self.loader_output["frame_data_list"].cumulative_sizes, idx)
        cur_lidar2gnss_rot, cur_lidar2gnss_trans = self._get_lidar_to_gnss_rotate_and_translation(
            self.loader_output["calibrated_sensors"][scene_id][self.referen_lidar]
        )
        lidar2gnss[:3, :3] = cur_lidar2gnss_rot
        lidar2gnss[:3, 3] = cur_lidar2gnss_trans

        # current gnss to world extrinsics
        gnss2global = np.eye(4)
        cur_gnss2world_rot, cur_gnss2world_trans = self._get_gnss_to_world_rotate_and_translation(
            self.loader_output["frame_data_list"][idx]
        )
        gnss2global[:3, :3] = cur_gnss2world_rot
        gnss2global[:3, 3] = cur_gnss2world_trans

        # middle lidar to ego extrinsics
        lidar2ego = np.eye(4)
        cur_lidar2ego_rot, cur_lidar2ego_trans = self._get_lidar_to_ego_rotate_and_translation(
            self.loader_output["calibrated_sensors"][scene_id]
        )
        lidar2ego[:3, :3] = cur_lidar2ego_rot
        lidar2ego[:3, 3] = cur_lidar2ego_trans

        # lidar_pose for multi frame
        # T_lidar_pose = np.eye(4).astype(np.float64)
        # cur_lidar_pose_rot, cur_lidar_pose_trans = self._get_lidar_pose_rotate_and_translation(
        #     self.loader_output["frame_data_list"][scene_id]
        # )
        # T_lidar_pose[:3, :3] = cur_lidar_pose_rot
        # T_lidar_pose[:3, 3] = cur_lidar_pose_trans

        return lidar2gnss, gnss2global, lidar2ego, gnss2global @ lidar2gnss  # T_lidar_pose

    def _get_timestamp(self, idx, lidar_name):
        sensor_data = self.loader_output["frame_data_list"][idx]["sensor_data"]
        if lidar_name in sensor_data.keys():
            timestamp = sensor_data[lidar_name]["timestamp"]
        else:
            potential_lidar_names = ["middle_lidar", "front_lidar", "cam_front_120"]
            valid_lidar_names = [name for name in potential_lidar_names if name in sensor_data.keys()]
            if len(valid_lidar_names) > 0:
                timestamp = sensor_data[valid_lidar_names[0]]["timestamp"]
            else:
                raise RuntimeError(f"invalid lidar name in :{lidar_name}")
        return float(timestamp) * 1e6

    def _load_single_sweep(self, lidar_name, idx):
        sw_lidar_infos = dict()
        load_dim = 5 if self.lidar_with_timestamp else 4
        lidar_data = self._get_point_cloud(idx, self.lidar_names)
        sw_lidar = lidar_data[0][:, :load_dim]
        sw_lidar_infos["sweep_lidar_to_gnss"], sw_lidar_infos["sweep_gnss_to_global"] = self.get_lidar_info(idx)
        if self.lidar_with_timestamp:
            sw_lidar_infos["sweep_lidar_timestamp"] = self._get_timestamp(idx, lidar_name)
        return sw_lidar, sw_lidar_infos

    def _load_lidar_sweeps(self, lidar_name, current_idx):
        sweeps, sweep_points, sweep_points_infos = dict(), list(), list()
        for sw_idx in self.lidar_sweeps_idx:
            # calcurate current index
            lidar_sw_idx = sw_idx + current_idx
            if lidar_sw_idx < 0 and lidar_sw_idx >= len(self.loader_output["frame_data_list"]):
                lidar_sw_idx = current_idx

            sw_lidar, sw_lidar_infos = self._load_single_sweep(lidar_name, lidar_sw_idx)
            sweep_points.append(sw_lidar)
            sweep_points_infos.append(sw_lidar_infos)

        sweeps["sweep_points"] = sweep_points
        sweeps["sweep_lidar_infos"] = sweep_points_infos
        return sweeps

    def _concat_sweeps(self, idx, lidars, lidar_infos, lidar_sweeps):
        assert len(lidars) == len(lidar_sweeps), "Incorrect lidar information"
        concat_points = []
        for lidar_id, points in enumerate(lidars):
            lidar_name, lidar_sweep = self.lidar_names[lidar_id], lidar_sweeps[lidar_id]
            sweep_point, sweep_info = lidar_sweep["sweep_points"], lidar_sweep["sweep_lidar_infos"]
            key_gnss_to_global = lidar_infos["gnss2global"]
            key_lidar_to_gnss = lidar_infos["lidar2gnss"]
            all_points = points.copy()
            if all_points.shape[-1] == 5:
                all_points[:, -1] = 0.0
            for swid, frame in enumerate(sweep_point):
                sweep_gnss_to_global = sweep_info[swid]["sweep_gnss_to_global"]
                homogeneous_point = np.ones((frame.shape[0], 4))
                homogeneous_point[:, :3] = frame[:, :3]
                sweep_on_key = (
                    np.linalg.inv(key_lidar_to_gnss)
                    @ np.linalg.inv(key_gnss_to_global)
                    @ sweep_gnss_to_global
                    @ key_lidar_to_gnss
                    @ homogeneous_point.T
                ).T
                frame[:, :3] = sweep_on_key[:, :3]
                if all_points.shape[-1] == 5:
                    cur_timestamp = self._get_timestamp(idx, lidar_name)
                    frame[:, -1] = (cur_timestamp - sweep_info[swid]["sweep_lidar_timestamp"]) / 1e6
                all_points = np.concatenate([all_points, frame])
            concat_points.append(all_points)
        return concat_points

    def get_lidars(self, idx, data_dict):
        """
        Loading lidar with given sample index
        """
        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()

        lidar_infos = dict()
        (
            lidar_infos["lidar2gnss"],
            lidar_infos["gnss2global"],
            lidar_infos["lidar2ego"],
            lidar_infos["T_lidar_pose"],
        ) = self.get_lidar_info(idx)
        ego2lidar = np.linalg.inv(lidar_infos["lidar2ego"])  # [4, 4]

        lidar_infos["ego2global"] = lidar_infos["T_lidar_pose"] @ ego2lidar  # NOTE

        if self.load_points:
            # load current key frame
            lidars = self._get_point_cloud(idx, self.lidar_names, data_dict)
            # load lidar sweeps
            if self.lidar_sweeps_idx:
                lidar_sweeps = []
                for lidar_name in self.lidar_names:
                    lidar_sweeps.append(self._load_lidar_sweeps(lidar_name, idx))
                lidars = self._concat_sweeps(idx, lidars, lidar_infos, lidar_sweeps)
            if lidars == [] or lidars is None:
                return None
            lidars = np.concatenate(lidars)
            data_dict.update(
                {
                    "points": lidars,
                }
            )

        try:
            timestamp = self._get_timestamp(idx, self.referen_lidar)
        except Exception:
            timestamp = 000000.000000
            Warning("No lidar timestamp, use 00000.00000 instead")
        # load to data_dict
        data_dict.update(
            {
                "lidar2ego": lidar_infos["lidar2ego"],
                "lidar2gnss": lidar_infos["lidar2gnss"],
                "gnss2global": lidar_infos["gnss2global"],
                "lidar2global": lidar_infos["gnss2global"] @ lidar_infos["lidar2gnss"],
                "timestamp": timestamp,
                "ego2global": lidar_infos["ego2global"],
            }
        )
        return data_dict


# import io
# import nori2 as nori
# import numpy as np
# import bisect
# from numpy.lib import recfunctions as rfn
# from scipy.spatial.transform import Rotation as R

# from loguru import logger
# from perceptron.utils import torch_dist


# class LidarBase:
#     def __init__(
#         self,
#         car,
#         lidar_names,
#         loader_output,
#         mode,
#         referen_lidar="middle_lidar",
#         pc_fields=["x", "y", "z", "i"],
#         used_echo_id=[1],
#         lidar_sweeps_idx=[-2, -1],
#         lidar_with_timestamp=False,
#     ) -> None:
#         self.load_points = True
#         if not lidar_names:
#             logger.warning("Empty lidar_names. lidar points will not be loaded")
#             self.load_points = False
#         self.car = car
#         self.lidar_names = lidar_names
#         self.loader_output = loader_output
#         self.mode = mode
#         self.nori_fetcher = None
#         self.referen_lidar = referen_lidar
#         self.pc_fields = pc_fields
#         self.used_echo_id = used_echo_id
#         self.lidar_sweeps_idx = lidar_sweeps_idx
#         self.lidar_with_timestamp = lidar_with_timestamp

#     @staticmethod
#     def _able_to_get_lidar(name, sensor_data):
#         is_distributed = torch_dist.is_distributed()
#         local_rank = 0
#         if is_distributed:
#             local_rank = torch_dist.get_rank()

#         Flag = True
#         if name not in sensor_data:
#             if local_rank == 0:
#                 logger.warning(f"{name} is not in {sensor_data.keys()}")
#             Flag = False
#         elif sensor_data[name] is None:
#             if local_rank == 0:
#                 logger.warning(f"{name} in {sensor_data.keys()}, but has no info")
#             Flag = False
#         elif sensor_data[name]["nori_id"] is None:
#             if local_rank == 0:
#                 logger.warning(f"The Nori id of {name} is None")
#             Flag = False
#         return Flag

#     def _get_point_cloud(self, idx, lidar_names):
#         """
#         Loading point cloud with given sample index

#         Refer to https://wiki.megvii-inc.com/pages/viewpage.action?pageId=316705342 for details about the point cloud format.

#         Args:
#             idx (int): Sampled index
#         Returns:
#             point cloud (Dict[str, np.ndarray]): (N, 4), (x-y-z-reflection)
#         """
#         lidars = []
#         sensor_data = self.loader_output["frame_data_list"][idx]["sensor_data"]
#         for lidar_name in lidar_names:
#             if not self._able_to_get_lidar(lidar_name, sensor_data):
#                 return None
#             nori_id = sensor_data[lidar_name]["nori_id"]
#             data = self.nori_fetcher.get(nori_id)
#             pc_data_raw = np.load(io.BytesIO(data)).copy()
#             pc_data = rfn.structured_to_unstructured(pc_data_raw[np.array(self.pc_fields)])
#             pc_data = pc_data[np.isin(pc_data_raw["echo_id"], self.used_echo_id)]

#             # add timestamp in pointcloud
#             if self.lidar_with_timestamp:
#                 timestamp = self._get_timestamp(idx, lidar_name)
#                 time = np.ones((pc_data.shape[0], 1)) * timestamp
#                 pc_data = np.concatenate((pc_data, time), axis=1)
#             lidars.append(pc_data)
#         return lidars

#     @staticmethod
#     def _get_lidar_to_gnss_rotate_and_translation(lidar_to_gnss_info):
#         trans = lidar_to_gnss_info["lidar_gnss"]["transform"]["translation"]
#         quan = lidar_to_gnss_info["lidar_gnss"]["transform"]["rotation"]
#         trans = np.array([trans["x"], trans["y"], trans["z"]], dtype=np.float64)
#         quan = np.array([quan["x"], quan["y"], quan["z"], quan["w"]], dtype=np.float64)
#         rot_mat = R.from_quat(quan).as_matrix()
#         return rot_mat, trans

#     @staticmethod
#     def _get_gnss_to_world_rotate_and_translation(sensor_data):
#         # 之前private data初期ins 数据有误，因此存的是odom 数据，之后 ins 数据正确后，改存ins 数据，更多信息见：
#         # https://wiki.megvii-inc.com/pages/viewpage.action?pageId=312323474
#         if "odom_data" in sensor_data:
#             odom_data = sensor_data["odom_data"]
#             trans = odom_data["pose"]["pose"]["position"]  # dict
#             quan = odom_data["pose"]["pose"]["orientation"]  # dict
#         elif "ins_data" in sensor_data:
#             ins_data = sensor_data["ins_data"]
#             trans = ins_data["localization"]["position"]  # dict
#             quan = ins_data["localization"]["orientation"]  # dict
#         else:
#             raise ValueError(f"{sensor_data['frame_id']} lack odom/ins data")
#         trans = np.array([trans["x"], trans["y"], trans["z"]], dtype=np.float64)
#         quan = np.array([quan["x"], quan["y"], quan["z"], quan["w"]], dtype=np.float64)
#         rot_mat = R.from_quat(quan).as_matrix()
#         return rot_mat, trans

#     def get_lidar_info(self, idx):
#         # current lidar to ego extrinsics
#         lidar2ego = np.eye(4)
#         scene_id = bisect.bisect_right(self.loader_output["frame_data_list"].cumulative_sizes, idx)
#         cur_lidar2gnss_rot, cur_lidar2gnss_trans = self._get_lidar_to_gnss_rotate_and_translation(
#             self.loader_output["calibrated_sensors"][scene_id][self.referen_lidar]
#         )
#         lidar2ego[:3, :3] = cur_lidar2gnss_rot
#         lidar2ego[:3, 3] = cur_lidar2gnss_trans

#         # current ego to world extrinsics
#         ego2global = np.eye(4)
#         cur_gnss2world_rot, cur_gnss2world_trans = self._get_gnss_to_world_rotate_and_translation(
#             self.loader_output["frame_data_list"][idx]
#         )
#         ego2global[:3, :3] = cur_gnss2world_rot
#         ego2global[:3, 3] = cur_gnss2world_trans
#         return lidar2ego, ego2global

#     def _get_timestamp(self, idx, lidar_name):
#         timestamp = self.loader_output["frame_data_list"][idx]["sensor_data"][lidar_name]["timestamp"]
#         return float(timestamp) * 1e6

#     def _load_single_sweep(self, lidar_name, idx):
#         sw_lidar_infos = dict()
#         load_dim = 5 if self.lidar_with_timestamp else 4
#         lidar_data = self._get_point_cloud(idx, self.lidar_names)
#         sw_lidar = lidar_data[0][:, :load_dim]
#         sw_lidar_infos["sweep_lidar_to_ego"], sw_lidar_infos["sweep_ego_to_global"] = self.get_lidar_info(idx)
#         if self.lidar_with_timestamp:
#             sw_lidar_infos["sweep_lidar_timestamp"] = self._get_timestamp(idx, lidar_name)
#         return sw_lidar, sw_lidar_infos

#     def _load_lidar_sweeps(self, lidar_name, current_idx):
#         sweeps, sweep_points, sweep_points_infos = dict(), list(), list()
#         for sw_idx in self.lidar_sweeps_idx:
#             # calcurate current index
#             lidar_sw_idx = sw_idx + current_idx
#             if lidar_sw_idx < 0 and lidar_sw_idx >= len(self.loader_output["frame_data_list"]):
#                 lidar_sw_idx = current_idx

#             sw_lidar, sw_lidar_infos = self._load_single_sweep(lidar_name, lidar_sw_idx)
#             sweep_points.append(sw_lidar)
#             sweep_points_infos.append(sw_lidar_infos)

#         sweeps["sweep_points"] = sweep_points
#         sweeps["sweep_lidar_infos"] = sweep_points_infos
#         return sweeps

#     def _concat_sweeps(self, idx, lidars, lidar_infos, lidar_sweeps):
#         assert len(lidars) == len(lidar_sweeps), "Incorrect lidar information"
#         concat_points = []
#         for lidar_id, points in enumerate(lidars):
#             lidar_name, lidar_sweep = self.lidar_names[lidar_id], lidar_sweeps[lidar_id]
#             sweep_point, sweep_info = lidar_sweep["sweep_points"], lidar_sweep["sweep_lidar_infos"]
#             key_ego_to_global = lidar_infos["ego2global"]
#             key_lidar_to_ego = lidar_infos["lidar2ego"]
#             all_points = points.copy()
#             if all_points.shape[-1] == 5:
#                 all_points[:, -1] = 0.0
#             for swid, frame in enumerate(sweep_point):
#                 sweep_ego_to_global = sweep_info[swid]["sweep_ego_to_global"]
#                 homogeneous_point = np.ones((frame.shape[0], 4))
#                 homogeneous_point[:, :3] = frame[:, :3]
#                 sweep_on_key = (
#                     np.linalg.inv(key_lidar_to_ego)
#                     @ np.linalg.inv(key_ego_to_global)
#                     @ sweep_ego_to_global
#                     @ key_lidar_to_ego
#                     @ homogeneous_point.T
#                 ).T
#                 frame[:, :3] = sweep_on_key[:, :3]
#                 if all_points.shape[-1] == 5:
#                     cur_timestamp = self._get_timestamp(idx, lidar_name)
#                     frame[:, -1] = (cur_timestamp - sweep_info[swid]["sweep_lidar_timestamp"]) / 1e6
#                 all_points = np.concatenate([all_points, frame])
#             concat_points.append(all_points)
#         return concat_points

#     def get_lidars(self, idx, data_dict):
#         """
#         Loading lidar with given sample index
#         """
#         if self.nori_fetcher is None:
#             self.nori_fetcher = nori.Fetcher()

#         lidar_infos = dict()
#         lidar_infos["lidar2ego"], lidar_infos["ego2global"] = self.get_lidar_info(idx)

#         if self.load_points:
#             # load current key frame
#             lidars = self._get_point_cloud(idx, self.lidar_names)
#             # load lidar sweeps
#             if self.lidar_sweeps_idx:
#                 lidar_sweeps = []
#                 for lidar_name in self.lidar_names:
#                     lidar_sweeps.append(self._load_lidar_sweeps(lidar_name, idx))
#                 lidars = self._concat_sweeps(idx, lidars, lidar_infos, lidar_sweeps)
#             if lidars == []:
#                 return None
#             lidars = np.concatenate(lidars)
#             data_dict.update(
#                 {
#                     "points": lidars,
#                 }
#             )

#         try:
#             timestamp = self._get_timestamp(idx, self.referen_lidar)
#         except Exception:
#             return None
#         # load to data_dict
#         data_dict.update(
#             {
#                 "lidar2ego": lidar_infos["lidar2ego"],
#                 "ego2global": lidar_infos["ego2global"],
#                 "lidar2global": lidar_infos["ego2global"] @ lidar_infos["lidar2ego"],
#                 "timestamp": timestamp,
#             }
#         )
#         return data_dict
