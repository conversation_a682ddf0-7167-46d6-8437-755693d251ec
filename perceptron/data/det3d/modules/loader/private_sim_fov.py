from collections import defaultdict

from .private_base import LoaderBase


class LoaderSimFov(LoaderBase):
    def _parse_camera_name_mapping(self):
        """
        All json information can only obtained by using hidden name!

        Take car1 sensors for example:
            standard_name            | hidden_name(car.sensors)  | sensor_info[name]
            cam_front_60             | camera_15    | camera_15
            cam_fornt_60_sim_fov30   | camera_15    | camera_15_sim_fov30
        """
        camera_name_mapping_h2s = defaultdict(dict)
        camera_name_mapping_s2h = defaultdict(dict)
        for sensor_name in self.camera_names:
            if not sensor_name.startswith("cam"):
                continue
            sensor_name_tmp = sensor_name
            if "_sim_fov" in sensor_name:
                sensor_name_tmp = sensor_name.split("_sim_fov")[0]
            sensor_ins = getattr(self.car.sensors, sensor_name_tmp)
            camera_name_mapping_h2s[sensor_ins.name] = {
                "standard_name": sensor_name,
                "resolution": sensor_ins.resolution,
            }
            camera_name_mapping_s2h[sensor_name] = {
                "hidden_name": sensor_ins.name,
                "resolution": sensor_ins.resolution,
            }
        self._camera_name_mapping = {"hidden": camera_name_mapping_h2s, "standard": camera_name_mapping_s2h}
