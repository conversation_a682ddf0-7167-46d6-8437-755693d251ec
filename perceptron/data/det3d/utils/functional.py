import copy
import inspect
import multiprocessing as mp
import time
from functools import wraps
from typing import Any, Dict

import cv2
import numpy as np
from scipy.spatial.transform import Rotation as R


def check_parameters(func):
    @wraps(func)
    def wrapper(cfg: dict) -> Any:
        if not isinstance(cfg, dict):
            raise TypeError("The argument 'cfg' must be a dictionary")

        cfg_ = copy.deepcopy(cfg)
        type_ = cfg_.pop("type")
        if not type_:
            raise ValueError("You must define the type of this object!")
        if not callable(type_):
            raise TypeError("The type of this object must be callable!")

        sig = inspect.signature(type_)
        has_keyword_args = False
        for param in sig.parameters.values():
            if param.kind == inspect.Parameter.VAR_KEYWORD:
                has_keyword_args = True
                break
            if param.default == inspect.Parameter.empty and param.name not in cfg_:
                raise ValueError(f"{param.name} is a required parameter")

        if not has_keyword_args:
            extra_params = set(cfg_) - set(sig.parameters)
            if extra_params:
                raise ValueError(f"The following parameters were not expected: {', '.join(extra_params)}")

        return func(cfg)

    return wrapper


@check_parameters
def initialize_object(cfg: Dict[str, Any]) -> Any:
    """Initialize Object from a configuration.

    Example:
        >>> cfg = Dict(
        ...     type = InitObject,
        ...     args = args
        ...     ...)
        >>> test = initialize_object(cfg=cfg) # Initialize Object
    """
    cfg_ = {}
    for key, value in cfg.items():
        if key != "loader_output":
            cfg_[key] = copy.deepcopy(value)
        else:
            cfg_[key] = value
    type_ = cfg_.pop("type")
    return type_(**cfg_)


def robust_crop_img(img, crop):
    x1, y1, x2, y2 = crop
    img = cv2.copyMakeBorder(
        img,
        -min(0, y1),
        max(y2 - img.shape[0], 0),
        -min(0, x1),
        max(x2 - img.shape[1], 0),
        cv2.BORDER_CONSTANT,
        value=[0, 0, 0],
    )
    y2 += -min(0, y1)
    y1 += -min(0, y1)
    x2 += -min(0, x1)
    x1 += -min(0, x1)
    return img[y1:y2, x1:x2]


def non_gt_filter(gt_boxes, gt_labels, roi_range, **kwargs):
    valid_boxes = gt_boxes[gt_labels != -1]

    if roi_range is not None and len(valid_boxes) > 0:
        mask = (valid_boxes[..., :3] >= roi_range[:3]).all(1)
        mask &= (valid_boxes[..., :3] <= roi_range[3:]).all(1)
        valid_boxes = valid_boxes[mask]

    return len(valid_boxes) == 0


def outlier_filter(gt_boxes, gt_labels, class_names, **kwargs):
    ped_idx = gt_labels == class_names.index("pedestrian")
    ped_boxes = gt_boxes[ped_idx]
    if len(ped_boxes) > 0 and (ped_boxes[:, 3:6] > np.array([3, 3, 3])).any():
        return True
    vehicle_idx = (
        (gt_labels == class_names.index("car"))
        | (gt_labels == class_names.index("bus"))
        | (gt_labels == class_names.index("bicycle"))
    )
    vehicle_boxes = gt_boxes[vehicle_idx]
    if len(vehicle_boxes) > 0 and (vehicle_boxes[:, 3:6] > np.array([30, 6, 10])).any():
        return True


def camera_filter(camera_names, camera_info, **kwargs):
    for k in camera_names:
        if camera_info.get(k, None) is None:
            return True
        
        if camera_info[k].get("nori_id", None) is None and camera_info[k].get("s3_path", None) is None:
            return True


def frame_washer(annotation, camera_names, start, end, filters, num_workers=16):
    frame_index = annotation.loader_output["frame_index"]
    class_names = annotation.class_names

    def worker(_start, _end, q):
        for dataset_idx, sub_dataset in enumerate(frame_index.datasets[_start:_end]):
            remove_list = []
            for sort_idx, frame_idx in enumerate(sub_dataset):
                annos = annotation.get_annos(frame_idx)
                gt_labels = np.array(
                    [class_names.index(i) if i in class_names else -1 for i in annos["labels"]],
                    dtype=np.float32,
                )
                gt_boxes = np.array(annos["gt_boxes"], dtype=np.float32)

                for filter in filters:
                    filter_tag = filter(
                        gt_boxes=gt_boxes,
                        gt_labels=gt_labels,
                        roi_range=annotation.roi_range,
                        class_names=annotation.class_names,
                        camera_names=camera_names,
                        camera_info=annotation.loader_output["frame_data_list"][frame_idx]["sensor_data"],
                    )
                    if filter_tag:
                        remove_list.append(sort_idx)
                        break
            sub_dataset = np.delete(sub_dataset, remove_list)
            q.put((_start + dataset_idx, sub_dataset))
        time.sleep(1)

    manager = mp.Manager()
    result_list = [0] * (end - start)
    q = manager.Queue()
    num_worker = min(num_workers, end - start)
    step = int(np.ceil((end - start) / num_worker))
    process_list = []
    for i in range(num_worker):
        _start = i * step + start
        _end = min((i + 1) * step + start, end)
        t = mp.Process(target=worker, args=(_start, _end, q))
        process_list.append(t)
        t.start()
    for t in process_list:
        t.join()
    while not q.empty():
        idx, result = q.get()
        result_list[idx - start] = result
    return start, end, result_list


# annoation utils
def load_angle_anno(anno):
    """
    四元数转欧拉角
    """
    quat = np.zeros((4,), dtype=np.float32)
    quat[0] = anno["angle_lidar"]["x"]
    quat[1] = anno["angle_lidar"]["y"]
    quat[2] = anno["angle_lidar"]["z"]
    quat[3] = anno["angle_lidar"]["w"]
    return R.from_quat(quat).as_euler("xyz")[-1]


# image utils
def get_sensor_tran_matrix(sensor_extrinsic):
    """
    相机外参转换为矩阵形式
    """
    trans = [sensor_extrinsic["transform"]["translation"][key] for key in ["x", "y", "z"]]
    quats = [sensor_extrinsic["transform"]["rotation"][key] for key in ["x", "y", "z", "w"]]
    trans_matrix = np.eye(4, 4)
    rotation = R.from_quat(quats).as_matrix()
    trans_matrix = np.eye(4)
    trans_matrix[:3, :3] = rotation
    trans_matrix[:3, 3] = trans
    return trans_matrix


def get_lidar_to_pixel(sensor_info, intrinsic_k):
    """
    相机参数矩阵，包含内参和外参
    """
    transform = get_sensor_tran_matrix(sensor_info["extrinsic"])
    lidar2pix = np.eye(4)
    lidar2pix[:3, :3] = intrinsic_k
    lidar2pix = (lidar2pix @ transform)[:3].tolist()
    return lidar2pix
