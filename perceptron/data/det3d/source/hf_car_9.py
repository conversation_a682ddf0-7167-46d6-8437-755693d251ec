# TODO: 数据情况需要更新


# ----- 训练数据全集，定期增量回流 -----
TRAINSET = {}


# ----- 训练数据子集，人工设计规则 -----
TRAINSET_PARTIAL = {
    "0929": "s3://peppa-meg-detect/tmpdata/datasets/car9_train_0929.json",
    "1023": "s3://peppa-meg-detect/tmpdata/datasets/car9_train_1023_more.json",
    "prelabels_radar": "s3://gongjiahao-share/e2e/test-file/HF9_RADAR_prelabel_path_e2e.json",
    "prelabels_radar_all": "/home/<USER>/HF9_RADAR_prelabelALL_path_e2e.json",
    "HF_e2e_labeled": "s3://gongjiahao-share/e2e/test-file/HF_e2e_labeled.json",
    "HF_e2e_labeled_861": "s3://gongjiahao-share/e2e/test-file/HF_e2e_labeled_861.json",
    "HF_e2e_labeled_861_with_occ": "s3://gongjiahao-share/e2e/test-file/HF_e2e_labeled_with_occ.json",
    "HF_e2e_labeled_bad_radar": "s3://gongjiahao-share/e2e/test-file/HF_e2e_labeled_bad_radar.json",
    "HF_e2e_labeled_bad_radar_with_occ": "s3://gongjiahao-share/e2e/test-file/HF_e2e_labeled_bad_radar_with_occ.json",
    "0720_0831": "s3://end2end/data/paths_list/det/hf/hf_car9_train_0720_0831.json",
    "0901_0926_filter_bmk": "s3://end2end/data/paths_list/det/hf/hf_car9_train_0901_0926_filted_bmk.json",
    "0720_0831_add_map_prelabels": "s3://end2end/data/paths_list/det/hf/hf_car9_train_0720_0831_add_map_prelabels.json",
    "0901_0926_filter_bmk_add_map_prelabels": "s3://end2end/data/paths_list/det/hf/hf_car9_train_0901_0926_filted_bmk_add_map_prelabels.json",
    "debug_sample_map": "s3://wangningzi-data/dataset/e2e_map_dataset/reorg_sample/sample_trainset_det_map.json",
    "debug_sample_track": "s3://wangningzi-data/dataset/e2e_map_dataset/reorg_sample/sample_trainset_track.json",
    "quick": "/home/<USER>/bmk_quick.json",
    "map_share_prelabels_bad_radar": "s3://gongjiahao-share/e2e/test-file/HF_map_share_prelabel_bad_radar.json",  # 带了map
    "map_share_prelabels": "s3://gongjiahao-share/e2e/test-file/HF_map_share_prelabel.json",  # 带了map
    "mapbmk_align": "s3://dwj-share/tmp/admap_bmk/base_align_prelabel_list.json",  # prelabel
    "CAR9_BMK_OCC_DAY": "s3://peppa-meg-detect/tmpdata/datasets/car9_bmk_occ_day.json",
    "bmk_new_withOCC": "s3://end2end/data/paths_list/track/HF_e2e_bmk_107_with_occ.json",
    "map_share_prelabels_for_e2e": "s3://gongjiahao-share/e2e/test-file/HF_map_share_prelabel_for_e2e.json",
    "map_lane_mf": "s3://mj-share/admap_e2e/data_e2eformat_18-6w/train_mf_list.json",  # 18.6w_lane_mf
    "debug_e2e_mf": "s3://mj-share/admap_e2e/e2e_joint_data/debug_mf_list.json",
    "map20w_val_mf": "s3://mj-share/admap_e2e/data_e2eformat_bmk_mf/6k_bmk_mf_list.json",
    "map_city_roadentrance_43w_1009_rm_bmk": "s3://tf-map-data-new/tmp/map_city_roadentrance_43w_1009_rm_bmk.json",
    "map_city_roadentrance_60w_1024_rm_bmk": "s3://tf-map-data-new/tmp/map_city_roadentrance_60w_1024_rm_bmk.json",
    "map_city_roadentrance_for_debug": "s3://tf-map-data-new/tmp/map_city_roadentrance_for_debug.json",
    "map_city_roadentrance_for_debug_one": "s3://tf-map-data-new/tmp/map_city_roadentrance_for_debug_one.json",
    "map_city_roadentrance_60w_1024_rm_bmk": "s3://tf-map-data-new/tmp/map_city_roadentrance_60w_1024_rm_bmk.json",
    "map_city_roadentrance_1121_merge_data": "s3://tf-map-data-new/tmp/list/map_city_roadentrance_1121_merge_data.json",
    "hf_label_det_cq_good_radar_1246json": "s3://gxt-share/e2e-data/hf_label_0717_det_2184_goodradar.json",
    "hf_label_det_cq_bad_radar_938json": "s3://gxt-share/e2e-data/hf_label_0717_det_2184_badradar.json",
    # cq track
    # hf prelabel 4441 json
    "hf_prelabel_track_4441json": "s3://gxt-share/e2e-data/hf_prelabel_0717_track_4441.json",
    "hf_prelabel_track_good_2738json": "s3://gxt-share/e2e-data/hf_prelabel_0717_track_4441_goodradar.json",
    "hf_prelabel_track_bad_1703json": "s3://gxt-share/e2e-data/hf_prelabel_0717_track_4441_badradar.json",
    # hf prelabel 4441 json
    "hf_prelabel_track_good_2738json_tracklength": "s3://gxt-share/e2e-data/hf_prelabel_0717_track_2738_goodradar_tracklength.json",
    "hf_prelabel_track_bad_1703json_tracklength": "s3://gxt-share/e2e-data/hf_prelabel_0717_track_1703_badradar_tracklength.json",
    # hf label cq
    "hf_label_track_1410json": "s3://gxt-share/e2e-data/hf_label_1410_jsons.json",
    "hf_label_track_good_radar_97json": "s3://gxt-share/e2e-data/hf_label_1410_jsons_goodradar.json",
    "hf_label_track_bad_radar_608json": "s3://gxt-share/e2e-data/hf_label_1410_jsons_badradar.json",
    # cq filter gs bmk
    "hf_label_1410_jsons_goodradar_filter": "s3://yrn-share/tmp/hf_label_1410_jsons_goodradar_filter.json",
    "hf_label_1410_jsons_badradar_filter": "s3://yrn-share/tmp/hf_label_1410_jsons_badradar_filter.json",
    "hf_prelabel_4441_jsons_goodradar_filter": "s3://yrn-share/tmp/hf_prelabel_0717_track_4441_goodradar_tracklength_filter.json",
    "hf_prelabel_4441_jsons_badradar_filter": "s3://yrn-share/tmp/hf_prelabel_0717_track_4441_badradar_tracklength_filter.json",
    "HF15_1002": "s3://gongjiahao-share/e2e-data-list/HF15_all_prelabels_tracking_1002_GS.json",
    
    # hf cq e2e data
    "map_city_e2e_177922_to_177749": "s3://tf-map-data-new/tmp/list/map_city_e2e_177922-177749.json",
    "map_city_e2e_177922_to_177749_ao": "s3://tf-map-data-new/tmp/list/map_city_e2e_177922-177749_ao.json",
    "map_city_roadentrance_43w_1009_rm_bmk_ao": "s3://tf-map-data-new/tmp/list/map_city_roadentrance_43w_1009_rm_bmk_ao.json",
    "qy_test_data": "s3://tf-map-data-new/data_rebuild/data_rebuild_mm_wfl_newcp/list/map_city_roadentrance_177756_wfl.json", # "s3://tf-map-data-new/data_rebuild/data_rebuild_mm_wfl_newcp/list/map_city_roadentrance_177407_177465_177519_177518_177517_wfl.json",
    "qy_test_data_v1": "s3://mj-share/admap_e2e_stepmind/map_data_city/list/map_city_stepmind_v1_list.json", 
    "z10_qy_city": "s3://tf-map-data-qy/data_rebuild/data_rebuild_mm_wfl_newcp_z10_fixed_calib/list_merge/map_city_roadentrance_0127_wo_z08.json", 
}


# ----- 测试数据全集，定期增量回流 -----
BENCHMARK = []


# ----- 测试数据子集，人工设计规则 -----
BENCHMARK_PARTIAL = {
    "CAR12_BMK_LIST_LABEL_OCC_GS": "s3://hangzhou-tf/tf_labels/动态感知2dbmk/export_labels/100874/car_12/20230920_dp-det_yueying_checked/ppl_bag_20230920_163417_det/v0_230926_152943/",
    # "CAR9_BMK_OCC": '/data/tmpdata/car9_bmk_radar.json',
    "CAR9_BMK_OCC_DAY": "s3://peppa-meg-detect/tmpdata/datasets/car9_bmk_occ_day.json",
    "CAR9_BMK_OCC_DAY_e2e": "s3://end2end/data/paths_list/track/hf9_track_bmk119.json",
    "CAR9_BMK_OCC_DAY_e2e_filter": "/home/<USER>/hf9_track_bmk119.json",
    "bmk_new": "s3://end2end/data/paths_list/track/HF_e2e_bmk_107.json",
    "bmk_new_withOCC": "s3://end2end/data/paths_list/track/HF_e2e_bmk_107_with_occ.json",
    "trt_eval_sample_300f": "s3://wangningzi-data/dataset/e2e_map_dataset/data_lists_v5/trt_eval_300f_sample_hf.json",
    "trt_eval_sample_14scene": "s3://end2end/data/paths_list/track/hf9_track_bmk15_trt_eval.json",
    "quick": "/home/<USER>/bmk_quick.json",
    "mapbmk_align": "s3://dwj-share/tmp/admap_bmk/base_align_prelabel_list.json",  # prelabel
    "map_share_prelabels": "s3://gongjiahao-share/e2e/test-file/HF_map_share_prelabel.json",  # 带了map
    "map20w_val": "s3://dwj-share/tmp/admap_bmk/base_align_prelabel_BMK_6k.json",
    "map20w_val_down100": "s3://dwj-share/tmp/admap_bmk/base_align_prelabel_BMK_6k_downsample100.json",
    "map20w_val_mf": "s3://mj-share/admap_e2e/data_e2eformat_bmk_mf/6k_bmk_mf_list.json",  # bmk_6k_mf
    "map-test": "/home/<USER>/6k_bmk_mf_list.json",
    "testOnTrain": "s3://wuyuhang-datasets/HF9/HF_e2e_labeled_861_GS_566.json",
    "map_city_roadentrance_for_debug_one": "s3://tf-map-data-new/tmp/map_city_roadentrance_for_debug_one.json",
    "map_city_roadentrance_bmk_1022": "s3://tf-map-data-new/tmp/map_city_roadentrance_bmk_1022.json",
    "map_city_roadentrance_for_debug_one_new": "s3://tf-map-data-new/tmp/map_city_roadentrance_for_debug_one_new.json",
    "map_city_roadentrance_bmk_1024": "s3://tf-map-data-new/tmp/map_city_roadentrance_bmk_1024.json",
    "map_city_roadentrance_bmk_1024_2fps": "s3://tf-map-data-new/tmp/map_city_roadentrance_bmk_1024_2fps.json",
    "car15_tmp_test": "s3://tf-map-data-new/tmp/tmp_map_city_noroadentrance_bmk_14k.json",
    "hf15_highway_test": "s3://mj-share/admap_e2e/prelabel_list/hf15_val_bmk/e2e_hf15_bmk_1008cut_list.json",  # hf15bmk
    "car15_tmp_test_top5": "s3://dwj-share/workflow/maptracker/20241115_release_pre/tmp_map_city_noroadentrance_bmk_14k_top5.json",
    "1129_city_bmk": "s3://tf-map-data-new/bmk/1129_city_bmk_det/list/1129_city_bmk_list.json",
    "1129_city_bmk_one": ["s3://tf-map-data-new/bmk/1129_city_bmk_det/json/176979_checked/car_12/ppl_bag_20240831_183212_det/v0_240910_021431/ppl_bag_20240831_183212_det_3607_3786.json"],
    "1130_demo": "s3://caiqianxi/tmp/list/hf14_pass_177922_newcp.json",
    "1216_debug": ["s3://caiqianxi/tmp/f12_road_entrance_e2emulti_fl_newcp_det/176979_checked/car_12/ppl_bag_20240831_183212_det/v0_240910_021431/ppl_bag_20240831_183212_det_9955_10151.json"],
    "1130_turn_rl": ["s3://caiqianxi/tmp/hf14_road_entrance_e2emulti_fl_newcp_det/177922/car_14/ppl_bag_20241123_072237_det/v0_241124_181341/ppl_bag_20241123_072237_det_1132_1907.json", 
                     "s3://caiqianxi/tmp/hf14_road_entrance_e2emulti_fl_newcp_det/177922/car_14/ppl_bag_20241123_072237_det/v0_241124_181341/ppl_bag_20241123_072237_det_11574_12592.json",
                     "s3://caiqianxi/tmp/hf14_road_entrance_e2emulti_fl_newcp_det/177922/car_14/ppl_bag_20241123_072237_det/v0_241124_181341/ppl_bag_20241123_072237_det_23740_24212.json"],
    "1225_RL_BMK": "s3://tf-map-data-new/bmk/1225_city_bmk_det/list/1225_city_bmk_list_v3.json",
    "1225_RL_BMK_debug": ["s3://tf-map-data-new/bmk/1225_city_bmk_det_debug/json/178410_new/car_12/ppl_bag_20241208_080052_det/v0_241209_173142/ppl_bag_20241208_080052_det_24826_25167.json",],
    "z08_tmp": "s3://mj-share/admap_e2e_stepmind/map_data_city/list/val_z08_tmp.json",
}
