import numpy as np
import torch

from torch.utils.data import Dataset
from perceptron.data.det3d.utils.functional import initialize_object
from perceptron.data.det3d.modules.pipelines import Compose


class BaseDataset(Dataset):
    def __init__(
        self,
        loader=None,
        pipeline=None,
        class_names=None,
        use_cbgs=False,
        filter_empty=True,
        mode="train",
    ):
        assert mode in ["train", "test"]
        super().__init__()
        self.class_names = class_names
        self.mode = mode
        self.filter_empty = filter_empty
        loader.update({"mode": mode, "class_names": class_names})
        self.loader = initialize_object(loader)
        self.infos = self.loader.infos
        for key, transform in pipeline.items():
            transform.update({"mode": self.mode})
        self.pipeline = Compose(pipeline)
        if use_cbgs and self.is_train:
            self.sample_indices = self._get_sample_indices()
        else:
            self.sample_indices = list(range(len(self.infos)))

    def __len__(self):
        return len(self.sample_indices)

    @property
    def is_train(self):
        return self.mode == "train"

    def _get_sample_indices(self):
        """Load annotations from ann_file.

        Args:
            ann_file (str): Path of the annotation file.

        Returns:
            list[dict]: List of annotations after class sampling.
        """

        def map_name(name):
            return name if not hasattr(self.loader, "name_mapping") else self.loader.name_mapping[name]

        cat2id = {name: i for i, name in enumerate(self.class_names)}
        class_sample_idxs = {cat_id: [] for cat_id in cat2id.values()}
        for idx, info in enumerate(self.infos):
            gt_names = [category for category in info["gt_names"]]
            gt_names = set([map_name(gt_name) for gt_name in gt_names])
            for gt_name in gt_names:
                if gt_name not in self.class_names:
                    continue
                class_sample_idxs[cat2id[gt_name]].append(idx)
        duplicated_samples = sum([len(v) for _, v in class_sample_idxs.items()])
        class_distribution = {k: len(v) / duplicated_samples for k, v in class_sample_idxs.items()}

        sample_indices = []

        frac = 1.0 / len(self.class_names)
        ratios = [frac / v for v in class_distribution.values()]
        for cls_inds, ratio in zip(list(class_sample_idxs.values()), ratios):
            sample_indices += np.random.choice(cls_inds, int(len(cls_inds) * ratio)).tolist()
        return sample_indices

    def __getitem__(self, idx):
        idx = self.sample_indices[idx]
        data_dict = self.loader(idx)
        data_dict = self.pipeline(data_dict)
        if self.is_train and self.filter_empty and data_dict["gt_boxes"].shape[0] == 0:
            new_idx = np.random.choice(len(self))
            return self.__getitem__(new_idx)
        return data_dict

    @staticmethod
    def collate_fn(data: tuple):
        def fill_batch_tensor(batch_data: list):
            if type(batch_data[0]) == int:
                return torch.tensor(batch_data)
            elif max([len(x) for x in batch_data]) == min([len(x) for x in batch_data]):
                return torch.stack(
                    [data if isinstance(data, torch.Tensor) else torch.tensor(data) for data in batch_data]
                ).to(torch.float32)
            else:
                batch_size = len(batch_data)
                batch_length = max([len(x) for x in batch_data])
                for data in batch_data:
                    if data.size != 0:
                        data_shape = data.shape
                        break
                batch_data_shape = (batch_size, batch_length, *data_shape[1:])
                batch_tensor = torch.zeros(batch_data_shape)
                for i, data in enumerate(batch_data):
                    if data.size != 0:
                        batch_tensor[i, : len(data)] = data if isinstance(data, torch.Tensor) else torch.tensor(data)
                return batch_tensor.to(torch.float32)

        batch_collection = dict()
        mats_keys = ["lidar2ego", "ego2global", "ida_mats", "lidar2imgs", "bda_mat"]
        batch_collection["mats_dict"] = dict()

        for key in data[0].keys():
            data_list = [iter_data[key] for iter_data in data]
            if key in mats_keys:
                batch_collection["mats_dict"][key] = fill_batch_tensor(data_list)
                continue
            batch_collection[key] = fill_batch_tensor(data_list)

        return batch_collection
