import concurrent
import random
import uuid
from typing import Any, Dict, Optional

import numpy as np
import rrun
import torch
from loguru import logger
from torch import distributed as dist
from torch.utils.data import Dataset
from tqdm import tqdm

from perceptron.data.det3d.modules.pipelines.compose import Compose
from perceptron.data.det3d.source.base import BaseCar
from perceptron.data.det3d.modules.annotation import AnnotationBase
from perceptron.data.det3d.utils.functional import (
    camera_filter,
    frame_washer,
    initialize_object,
    non_gt_filter,
    outlier_filter,
)
from perceptron.utils import torch_dist


class DatasetBase(Dataset):
    def __init__(
        self,
        car: BaseCar,
        mode: str = "train",
        sensor_names: list = None,
        offline_filter: bool = False,
        loader: Optional[Dict[str, Any]] = None,
        annotation: Optional[Dict[str, Any]] = None,
        pipeline: Optional[Dict[str, Any]] = None,
        evaluator: Optional[Dict[str, Any]] = None,
        image: Optional[Dict[str, Any]] = None,
        lidar: Optional[Dict[str, Any]] = None,
        radar: Optional[Dict[str, Any]] = None,
        roi_mask: list = [-25.6, -85, 25.6, 85],
    ):
        self.mode = mode
        self.car = car
        self.sensor_names = sensor_names
        self._sensor_list_check()

        # Load frame data from Json file
        loader.update({"mode": self.mode})
        self.loader = initialize_object(loader)
        self.loader()
        self.loader_output = self.loader.output

        if isinstance(annotation, dict):
            # Load annotation
            annotation.update(
                {
                    "loader_output": self.loader_output,
                    "mode": self.mode,
                }
            )
            self.annotation: AnnotationBase = initialize_object(annotation)
        elif isinstance(annotation, list):
            annotations = []
            for task in annotation:
                task.update(
                    {
                        "loader_output": self.loader_output,
                        "mode": self.mode,
                    }
                )
                annotations.append(initialize_object(task))
            self.annotation = annotations
        else:
            raise NotImplementedError

        # Prepare camera data
        if image is not None and "camera_names" in sensor_names:
            self.camera_names = [camera_name for camera_name in sensor_names["camera_names"]]
            image.update(
                {
                    "loader_output": self.loader_output,
                    "mode": self.mode,
                }
            )
            self.image = initialize_object(image)

        # filter illegal frames
        if self.mode == "train" and offline_filter:
            self.loader_output["frame_index"] = self._wash_frames()
            self.annotation.loader_output["frame_index"] = self.loader_output["frame_index"]
            self.image.loader_output["frame_index"] = self.loader_output["frame_index"]

        # Prepare lidar data
        if lidar is not None and "lidar_names" in sensor_names:
            self.lidar_names = [lidar_name for lidar_name in sensor_names["lidar_names"]]
            assert self.lidar_names == lidar["lidar_names"]
            lidar.update(
                {
                    "loader_output": self.loader_output,
                    "mode": self.mode,
                }
            )
            self.lidar = initialize_object(lidar)
        if radar is not None and "radar_names" in sensor_names:
            radar.update(
                {"loader_output": self.loader_output, "mode": self.mode, "radar_key_list": sensor_names["radar_names"]}
            )
            self.radar = initialize_object(radar)

        # filter illegal frames
        if self.mode == "train" and offline_filter:
            self.loader_output["frame_index"] = self._wash_frames()
            self.annotation.loader_output["frame_index"] = self.loader_output["frame_index"]
            self.image.loader_output["frame_index"] = self.loader_output["frame_index"]

        # Process data pipeline
        for key, transform in pipeline.items():
            transform.update({"mode": self.mode})
        self.pipeline = Compose(pipeline)
        if self.mode != "train":
            evaluator.update(
                {
                    "loader_output": self.loader_output,
                    "annotation": self.annotation,
                }
            )
            self.evaluator = initialize_object(evaluator)

        self.roi_mask = roi_mask

    def _sensor_list_check(self) -> None:
        assert "camera_names" in self.sensor_names.keys(), "sensor_name dict must include camera_names "

    def __len__(self):
        return len(self.loader.output["frame_index"])

    def _rand_index(self):
        return random.randint(0, self.__len__() - 1)

    def _wash_frames(self):
        num_jsons = len(self.loader_output["frame_index"].datasets)
        clean_dataset_list = [0] * num_jsons

        if torch_dist.is_master():
            spec = rrun.RunnerSpec()
            spec.name = "clean-dataset-%s" % (uuid.uuid1().hex)
            spec.log_dir = "/data/tmp/rrun_debug"
            spec.charged_group = ""
            spec.resources.cpu = 24
            spec.resources.gpu = 0
            spec.resources.memory_in_mb = 10240
            spec.max_wait_duration = "24h"
            spec.minimum_lifetime = 24 * 3600 * int(1e9)
            spec.preemptible = False

            num_washers = min(15, num_jsons)
            executor = rrun.RRunExecutor(spec, num_washers, 1)
            step = int(np.ceil(num_jsons / num_washers))
            pbar = tqdm(total=num_jsons, desc="[remove dirty key frame]")
            futures = []
            for i in range(num_washers):
                start = i * step
                end = min((i + 1) * step, num_jsons)
                futures.append(
                    executor.submit(
                        frame_washer,
                        self.annotation,
                        self.image.raw_names,
                        start,
                        end,
                        [non_gt_filter, outlier_filter, camera_filter],
                        24,
                    )
                )

            for future in concurrent.futures.as_completed(futures):
                # subset_slice is list of array with int dtype different lengths.
                start, end, subset_slice = future.result()
                clean_dataset_list[start:end] = subset_slice
                pbar.update(len(subset_slice))
            executor.shutdown(wait=False)

        if torch_dist.is_distributed():
            dist.barrier()
            dist.broadcast_object_list(clean_dataset_list, 0)
        if torch_dist.is_master():
            print(clean_dataset_list)
        clean_frame_index = torch.utils.data.dataset.ConcatDataset(clean_dataset_list)

        return clean_frame_index

    def __getitem__(self, index):
        is_distributed = torch_dist.is_distributed()
        local_rank = 0
        if is_distributed:
            local_rank = torch_dist.get_rank()

        while True:
            frame_idx = self.loader_output["frame_index"][index]
            data_dict = {
                "frame_id": frame_idx,
            }
            # annotation
            anno_info = self.annotation.get_annos(frame_idx, data_dict)
            if anno_info is None:
                index = self._rand_index()
                if local_rank == 0:
                    logger.warning("Anno in this frame is not valid! Pick another one.")
                continue

            # image
            img_info = self.image.get_images(frame_idx, data_dict)
            if img_info is None:
                index = self._rand_index()
                if local_rank == 0:
                    logger.warning("Image in this frame is not valid! Pick another one.")
                continue
            else:
                break

        # preocess image
        data_dict = self.pipeline(data_dict)

        data_dict["roi_mask"] = np.asarray(self.roi_mask)
        return data_dict

    def generate_prediction_dicts(self, batch_dict, pred_dicts, class_names, output_path=None):
        return self.evaluator.generate_prediction_dicts(batch_dict, pred_dicts, class_names, output_path=output_path)

    def evaluation(self, det_annos, class_names, **kwargs):
        return self.evaluator.evaluation(det_annos, class_names, **kwargs)

    @staticmethod
    def collate_fn(data: dict):
        """merges a list of samples to form a mini-batch of Tensor(s).

        Args:
            data_dict (dict): samples contain all elements about inputs of network

        Returns:
            dict: mini-batch of network input tensors
        """

        batch_collection = dict()
        batch_collection["mats_dict"] = dict()
        mats_shape_list = [(3, 3), (4, 3), (4, 4)]

        for key, value in data[0].items():
            data_list = [iter_data[key] for iter_data in data]
            if isinstance(value, (list, int, np.int64, np.int32)):
                batch_collection[key] = np.stack(data_list)
            elif isinstance(value, np.ndarray) and value.shape[-2:] in mats_shape_list:
                batch_collection["mats_dict"][key] = np.stack(data_list)
            else:
                batch_collection[key] = data_list

        return batch_collection
