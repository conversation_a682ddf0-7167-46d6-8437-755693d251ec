import bisect
import math
import warnings
from collections import abc
from functools import partial
from typing import Any, Dict, Optional

import numpy as np
import torch
import copy
from loguru import logger
from mmdet3d.core.bbox import Box3DMode, LiDARInstance3DBoxes, BaseInstance3DBoxes

from perceptron.data.det3d.modules.annotation import E2EAnnotations
from perceptron.data.det3d.modules.pipelines import ImageAffineTransGPU, ImageUndistortGPU
from perceptron.data.det3d.private.base import DatasetBase
from perceptron.utils.env import is_volcano_platform
from perceptron.utils.maptracker_utils.prepare_gt_tracks import match_two_consecutive_frames, assign_global_ids 

def padding_radar(radar_points, max_radar_num):
    """将radar_points设置为max_radar_num个点，超出drop，少于则补0
    FIXME: 修改为按照改batch里面最大的radar点数来设置
    """
    radar_feats_dim = radar_points.shape[1]
    padding_radar_points = np.zeros((max_radar_num, radar_feats_dim))
    s = min(radar_points.shape[0], max_radar_num)
    padding_radar_points[:s, :] = radar_points[:s, :]
    return padding_radar_points


class PrivateMultiModalData(DatasetBase):
    r"""
    Dataset for orin 7v.
    """

    def __init__(self, **kwargs):
        super(PrivateMultiModalData, self).__init__(**kwargs)

    @property
    def is_train(self):
        return self.mode == "train"

    def _sensor_list_check(self) -> None:
        sensor_names = self.sensor_names.keys()
        assert "camera_names" in sensor_names, "sensor_name dict must include camera_names "
        assert (
            "lidar_names" in sensor_names or "radar_names" in sensor_names
        ), "sensor_name dict must include lidar_names or radar_names"

    def __getitem__(self, index):
        """
        Return dataset's final outputs.
        Note:
            "frame_id":       list of frame index.
            "gt_boxes":       (num_boxes, 7) numpy array, 7 means (x,y,z,l,w,h,angle).
            "gt_labels":      (N, 1) numpy array, box's labels.
            "imgs":           list of multi-view image (in order to compatible with inputs of different shapes).
            "lidar2imgs":     (N, 4, 4) numpy array, camera intrinsic matrix.
            "points":         (N, 4) numpy array, concated lidar points.
            "lidar2ego:       (4, 4) numpy array, lidar to ego extrinsics.
            "ego2global":     (4, 4) numpy array, ego to world extrinsics.
            "bda_mat":        (N, 4, 4) numpy array, loaded by pipeline forward
            "ida_mats":       (N, 4, 4) numpy array, loaded by pipeline forward
        """
        while True:
            frame_idx = self.loader_output["frame_index"][index]
            data_dict = {
                "frame_id": frame_idx,
            }
            # annotation
            annos = self.annotation.get_annos(index, data_dict)
            if annos is None and self.is_train:
                index = self._rand_index()
                continue

            # image
            if hasattr(self, "image") and "camera_names" in self.sensor_names:
                img_info = self.image.get_images(index, data_dict)
                if img_info is None and self.is_train:
                    index = self._rand_index()
                    continue

            # lidar
            if hasattr(self, "lidar") and "lidar_names" in self.sensor_names:
                lidar_info = self.lidar.get_lidars(index, data_dict)
                if lidar_info is None and self.is_train:
                    index = self._rand_index()
                    continue

            # radar
            if hasattr(self, "radar") and "radar_names" in self.sensor_names:
                self.radar.get_radars(index, data_dict)

            break

        # apply data augment
        data_dict = self.pipeline(data_dict)

        # 这里写法需要改进，需要支持非对称的roi_mask
        data_dict["roi_mask"] = np.asarray(self.roi_mask)

        if self.mode == "train" and data_dict["gt_boxes"].shape[0] == 0:
            new_idx = np.random.choice(len(self))
            return self.__getitem__(new_idx)
        return data_dict

    @staticmethod
    def collate_fn_fill_batch(data: dict, max_radar_num=200):
        def fill_batch_tensor(batch_data: list):
            if max([len(x) for x in batch_data]) == min([len(x) for x in batch_data]):
                return np.stack([data for data in batch_data])
            else:
                batch_size = len(batch_data)
                batch_length = max([len(x) for x in batch_data])
                for data in batch_data:
                    if data.size != 0:
                        data_shape = data.shape
                        break
                batch_data_shape = (batch_size, batch_length, *data_shape[1:])
                batch_tensor = np.zeros(batch_data_shape)
                for i, data in enumerate(batch_data):
                    if data.size != 0:
                        batch_tensor[i, : len(data)] = data
                return batch_tensor

        def padding_radar(radar_points):
            radar_feats_dim = radar_points.shape[1]
            padding_radar_points = np.zeros((max_radar_num, radar_feats_dim))
            padding_radar_points[: radar_points.shape[0], :] = radar_points[:, :]
            return padding_radar_points

        batch_collection = dict()
        batch_collection["mats_dict"] = dict()
        mats_shape_list = [(3, 3), (4, 3), (4, 4)]
        for key, value in data[0].items():
            if "gt_forecasting" in key or "instance_inds" in key:
                continue
            if key == "gt_boxes":
                data_list = [np.hstack((iter_data[key], np.zeros((iter_data[key].shape[0], 2)))) for iter_data in data]
                batch_collection[key] = fill_batch_tensor(data_list)
            elif key in ["gt_labels", "points"]:
                data_list = [iter_data[key] for iter_data in data]
                batch_collection[key] = fill_batch_tensor(data_list)
            elif key == "radar_points":
                data_list = [padding_radar(iter_data[key]) for iter_data in data]
                batch_collection[key] = fill_batch_tensor(data_list)
            else:
                data_list = [iter_data[key] for iter_data in data]
                if isinstance(value, (list, int, np.int64, np.int32)):
                    batch_collection[key] = np.stack(data_list)
                elif isinstance(value, np.ndarray) and value.shape[-2:] in mats_shape_list:
                    batch_collection["mats_dict"][key] = np.stack(data_list)
                elif isinstance(value, np.ndarray):
                    batch_collection[key] = np.stack(data_list)
                else:
                    batch_collection[key] = data_list
        return batch_collection


class PrivateE2EDataset(PrivateMultiModalData):
    r"""
    Dataset for orin 7v.
    """
    def __init__(
        self,
        num_frames_per_sample=5,
        eval_cfg=None,
        gpu_aug=False,
        postcollate_tensorize=False,
        convert_fp32=True,
        seq_mode=False,
        seq_split_num=1,
        fov_boardline=None,  # 前视 fov
        valid_cam=None,  # 热插拔
        gpfs_prefix="/mnt/acceldata/static/",
        data_mode="nori",
        save_rgb=True,  # Default 'rgb' True, adapt for jira 'bgr' False
        **kwargs
    ):
        self.gpu_aug = gpu_aug and torch.cuda.is_available()
        if self.gpu_aug and not postcollate_tensorize:
            warnings.warn(
                "gpu aug is unavailable without postcollate_tensorize, use cpu aug instead.",
            )

        for key, transform in kwargs["pipeline"].items():
            if self.gpu_aug and key in ["undistort", "ida_aug"]:
                transform.update({"gpu_aug": True})
            transform.update({"multiframe": True})

        if "annotation" in kwargs and isinstance(kwargs["annotation"], list):
            warnings.warn(
                "Task list annotations is deprecated for e2e dataset, should use E2EAnnotations instead.",
                DeprecationWarning,
            )
            super(PrivateE2EDataset, self).__init__(**kwargs)
        else:
            annotations_e2e = kwargs.pop("annotation")
            kwargs["annotation"] = annotations_e2e["box"]
            super(PrivateE2EDataset, self).__init__(**kwargs)
            self.annotation = E2EAnnotations(self.loader_output, self.mode, annotations_e2e)
            self.annotation_e2e_cfg = annotations_e2e
            

        self.num_frames_per_sample = num_frames_per_sample

        if isinstance(self.annotation, list):
            for task in self.annotation:
                task.loader_output["calibrated_sensors"] = self.image.loader_output["calibrated_sensors"]
        else:
            try:
                self.annotation.loader_output["calibrated_sensors"] = self.image.loader_output["calibrated_sensors"]
            except Exception:
                assert not getattr(self, "image", False)
                self.annotation.loader_output["calibrated_sensors"] = self.loader_output["calibrated_sensors"]
        self._init_scene_flag()
        self.seq_mode = seq_mode
        self.seq_split_num = seq_split_num
        if self.seq_mode:
            self._set_sequence_group_flag()

        self.postcollate_tensorize = postcollate_tensorize
        self._init_post_collate()

        self._init_preforward(kwargs["pipeline"])

        self.fov_boardline = fov_boardline

        self.valid_cam = valid_cam
        self.gpfs_prefix = gpfs_prefix
        self.data_mode = data_mode
        self.save_rgb = save_rgb

    def _init_scene_flag(self):
        """
        organize data in scene for multi gpu eval
        """
        cumulative_size = self.loader_output["cummulative_sizes"]
        scene_len = np.array([0] + cumulative_size)
        scene_len = scene_len[1:] - scene_len[:-1]
        self.scene_flag = []
        self.scene_frame_idx = []
        for i, s in enumerate(scene_len):
            self.scene_flag.extend([i] * s)
            self.scene_frame_idx.extend(list(range(s)))
        self.scene_flag = np.array(self.scene_flag)  # must be np.array
        self.scene_frame_idx = np.array(self.scene_frame_idx)
        assert len(self.scene_flag) == len(self.scene_frame_idx) == len(self)
        self.scene_order = (
            True  # default as False, need to set True when using GroupEachSampleInBatchSampler and multi gpu eval
        )

    def _set_sequence_group_flag(self):
        """Set each sequence to be a different group"""

        assert self.seq_split_num > 1, "each group must be longer than only one frame!"
        bin_counts = np.bincount(self.scene_flag)

        new_flags = []
        group_frame_idx = []

        curr_new_flag = 0
        for curr_flag in range(len(bin_counts)):
            curr_sequence_length = np.array(
                list(range(0, bin_counts[curr_flag], math.ceil(bin_counts[curr_flag] / self.seq_split_num)))
                + [bin_counts[curr_flag]]
            )
            for sub_seq_idx in curr_sequence_length[1:] - curr_sequence_length[:-1]:
                for frame_idx in range(sub_seq_idx):
                    new_flags.append(curr_new_flag)
                    group_frame_idx.append(frame_idx)
                curr_new_flag += 1

        assert len(new_flags) == len(self.scene_flag)
        assert len(group_frame_idx) == len(self.scene_frame_idx)
        assert len(np.bincount(new_flags)) == len(np.bincount(self.scene_flag)) * self.seq_split_num
        self.scene_flag = np.array(new_flags, dtype=np.int64)
        self.scene_frame_idx = np.array(group_frame_idx, dtype=np.int64)

    def _init_post_collate(self):
        """
        collate data
        """
        self.postcollate_pipeline = []
        if self.postcollate_tensorize:
            assert torch.cuda.is_available()
            self.postcollate_pipeline.append(PrivateE2EDataset.load_tensor2gpu)

        def _postcollate_fn(batch_dict: Dict) -> None:
            for fn in self.postcollate_pipeline:
                fn(batch_dict)

        self.batch_postcollate_fn = _postcollate_fn

    def _init_preforward(self, pipeline):
        # 重构中：gpu的去畸变在pipeline中，和默认去畸变在loader中不一致，统一处理将默认的cpu去畸变同样后移至pipeline进行
        # 除gpu undistort/aug之外，暂时没有其他内容只能放置于preforward，暂时不进行额外配置
        """"""
        self.preforward_pipeline = []
        if self.gpu_aug:
            self.preforward_pipeline.append(
                    ImageUndistortGPU(
                        mode=self.mode,
                    )
                )
            if "ida_aug" in pipeline:
                self.preforward_pipeline.append(
                    ImageAffineTransGPU(
                        mode=self.mode,
                        img_norm=pipeline["ida_aug"]["img_norm"],
                        img_conf=pipeline["ida_aug"]["img_conf"],
                    )
                )

        def _preforward_fn(batch_dict: Dict) -> None:
            for fn in self.preforward_pipeline:
                fn(batch_dict)

        self.batch_preforward_fn = _preforward_fn

    @staticmethod
    def convert_data2tensor(batch_dict) -> None:
        def _cast_to_tensor(inputs: Any, dtype: Optional[np.dtype] = None) -> Any:

            """Recursively convert np.ndarray in inputs into torch.Tensor
            Args:
                inputs: Inputs that to be casted.
                dtype (numpy.dtype): dtype before conversion


            Returns:
                The same recursive structure are remained as inputs, with all contained ndarrays cast to Tensors.
            """

            if isinstance(inputs, torch.Tensor):
                return inputs
            elif isinstance(inputs, np.ndarray):
                # if isinstance(inputs, np.ndarray):
                if dtype is not None:
                    inputs = inputs.astype(dtype)
                if inputs.dtype in [np.uint16]:
                    # unsupported numpy types in torch.tensor
                    inputs = inputs.astype(np.int32)
                return torch.from_numpy(inputs).contiguous()
            elif isinstance(inputs, abc.Mapping):
                return {k: _cast_to_tensor(v, dtype) for k, v in inputs.items()}
            elif isinstance(inputs, str):
                return inputs
            elif isinstance(inputs, abc.Iterable):
                return [_cast_to_tensor(item, dtype) for item in inputs]
            else:
                return inputs

        skip_conversions = ["frame_id", "img_metas", "calib", "image_shape", "gt_bboxes_3d", "map_gt", 
                            "map_gauss", "map_local2global_info", "timestamp", "map_attr", "valid_region_norm",
                            "bda_aug_param_dict"]

        for key, val in batch_dict.items():
            if key in skip_conversions:
                continue
            batch_dict[key] = _cast_to_tensor(val)

    @staticmethod
    def load_tensor2gpu(batch_dict, pin_memory=False):
        # 重构问题：
        # Private数据在什么情况使用BaseInstance3DBoxes？

        """Recursively load cpu tensor to cuda and convert tensor from src_type to dst_type.
        Ensure that data is loaded into the GPU before type conversion
        to avoid pinned memory being converted to pageable memory.
        """

        def _cast_tensors(inputs: Any, cast_fn, dtype: Optional[np.dtype] = None) -> tuple[Any, dict]:
            if isinstance(inputs, torch.Tensor):
                return cast_fn(inputs), {"has_tensor": True}
            elif isinstance(inputs, np.ndarray):
                # if isinstance(inputs, np.ndarray):
                if dtype is not None:
                    inputs = inputs.astype(dtype)
                if inputs.dtype in [np.uint16]:
                    # unsupported numpy types in torch.tensor
                    inputs = inputs.astype(np.int32)
                return cast_fn(torch.from_numpy(inputs,).contiguous()), {"has_tensor": True}
            elif isinstance(inputs, BaseInstance3DBoxes):
                ret, status = _cast_tensors(inputs.tensor, cast_fn, dtype)
                return type(inputs)(ret, box_dim=inputs.box_dim, with_yaw=inputs.with_yaw), status
            elif isinstance(inputs, abc.Mapping):
                ret = {}
                status = {"has_tensor": False}
                for k, v in inputs.items():
                    _ret, _statu = _cast_tensors(v, cast_fn, dtype)
                    if _statu["has_tensor"]:
                        status["has_tensor"] = True
                    ret[k] = _ret
                return ret, status
            elif isinstance(inputs, str):
                return inputs, {"has_tensor": False}
            elif isinstance(inputs, abc.Iterable):
                # NOTE: this conditional must be put after the str check
                ret = []
                status = {"has_tensor": False}
                for item in inputs:
                    _ret, _statu = _cast_tensors(item, cast_fn, dtype)
                    if _statu["has_tensor"]:
                        status["has_tensor"] = True
                    ret.append(_ret)
                return ret, status
            else:
                return inputs, {"has_tensor": False}

        def _cast_tensors_to_cuda(inputs: Any, pin_memory: bool) -> tuple[Any, dict]:
            def _cast_tensor_to_cuda(tensor: torch.Tensor, pin_memory: bool):
                return (tensor.pin_memory() if pin_memory else tensor).to(device="cuda", non_blocking=True)

            return _cast_tensors(inputs, partial(_cast_tensor_to_cuda, pin_memory=pin_memory))

        # Add the following keys to avoid redundant recurrent function invocations
        # NOTE: combined with the `skip_conversions` list of PrivateE2EDataset.convert_data2tensor
        skip_conversions = [
            ## keys no need to be converted to tensors --> skip
            "frame_id", "img_metas", "calib", "image_shape", "map_gt", "map_gauss", "map_local2global_info", "timestamp", "map_attr",
            ## keys no need to be converted to tensors, but may contain a tensor --> has tensor, not skip, need to be moved to GPU
            # "gt_bboxes_3d", "valid_region_norm",
            ## keys does not contain tensors --> no tensor or numpy array, can skip
            "nori_id", "scene_frame_idx", "index_in_dataset",  "map_attr", "map_label_mask", "map_scene_weight", "valid_region", "bda_aug_param_dict"
        ]

        for key, val in batch_dict.items():
            if key in skip_conversions:
                continue
            ret, status = _cast_tensors_to_cuda(val, pin_memory)
            batch_dict[key] = ret
            if not status["has_tensor"]:
                logger.warning(f"{key} does NOT contain any tensor, Please add it to the `skip_conversions` list")

    def get_single_data(self, index: int) -> dict:
        """
        Return dataset's final outputs.
        Note:
            "frame_id":       list of frame index.
            "gt_boxes":       (num_boxes, 7) numpy array, 7 means (x,y,z,l,w,h,angle).
            "gt_labels":      (N, 1) numpy array, box's labels.
            "imgs":           list of multi-view image (in order to compatible with inputs of different shapes).
            "lidar2imgs":     (N, 4, 4) numpy array, camera intrinsic matrix.
            "points":         (N, 4) numpy array, concated lidar points.
            "lidar2ego:       (4, 4) numpy array, lidar to ego extrinsics.
            "ego2global":     (4, 4) numpy array, ego to world extrinsics.
            "bda_mat":        (N, 4, 4) numpy array, loaded by pipeline forward
            "ida_mats":       (N, 4, 4) numpy array, loaded by pipeline forward
        """

        frame_idx = self.loader_output["frame_index"][index]
        json_collections = self.loader_output["json_collection"]
        json_idx = bisect.bisect_right(self.loader_output["frame_data_list"].cumulative_sizes, frame_idx)
        json_path = self.loader_output["json_collection"][json_idx]
        data_dict = {
            "frame_id": frame_idx,
            "gpfs_prefix": self.gpfs_prefix,
            "data_mode": self.data_mode,  # nori or nori
            "save_rgb": self.save_rgb,
            "json_path": json_path
        }

        # image
        if hasattr(self, "image") and "camera_names" in self.sensor_names:
            img_info = self.image.get_images(index, data_dict)   # 这里会改动 self.annotation.loader_output["calibrated_sensors"][0]['cam_front_120']['intrinsic']['K']
            if img_info is None: # : and self.is_train:
                del data_dict
                return None

        # lidar
        if hasattr(self, "lidar") and "lidar_names" in self.sensor_names:
            lidar_info = self.lidar.get_lidars(index, data_dict)
            if lidar_info is None: #  and self.is_train:
                del data_dict
                return None

        # radar
        if hasattr(self, "radar") and "radar_names" in self.sensor_names:
            self.radar.get_radars(index, data_dict)

        if hasattr(self, "scene_flag"):
            group_idx = self.scene_flag[index]
            data_dict["group_idx"] = group_idx
        if hasattr(self, "scene_frame_idx"):
            scene_frame_idx = self.scene_frame_idx[index]
            data_dict["scene_frame_idx"] = scene_frame_idx
        # remove single frame pipeline for E2E dataset
        
        # annotation
        if isinstance(self.annotation, list):
            for task in self.annotation:
                annos = task[index]
                if annos is None and self.is_train:
                    del data_dict
                    return None
        else:
            annos = self.annotation[index]
            if annos is None and self.is_train:
                # print('88888', self.annotation[index])
                del data_dict
                return None

        if annos is not None:  # self.is_train:
            data_dict.update(annos)

        return data_dict

    def generate_track_data_indexes(self, index: int) -> list:
        """Choose the track indexes that are within the same sequence"""
        index_list = [i for i in range(index - self.num_frames_per_sample + 1, index + 1)]

        scene_tokens = [bisect.bisect_right(self.loader_output["cummulative_sizes"], i) for i in index_list]

        tgt_scene_token, earliest_index = scene_tokens[-1], index_list[-1]
        for i in range(self.num_frames_per_sample)[::-1]:
            if scene_tokens[i] == tgt_scene_token:
                earliest_index = index_list[i]
            elif self.mode != "train":
                index_list = index_list[i + 1 :]
                break
            elif self.mode == "train":
                index_list[i] = earliest_index
        return index_list

    def _convert_to_lidar_instance(self, sample_queue: list, key: str) -> list:
        lidar_instances = []
        for i in range(len(sample_queue)):
            if len(sample_queue[i][key]):
                sample_queue[i][key] = np.hstack(
                    (sample_queue[i][key], np.zeros((sample_queue[i][key].shape[0], 2)))
                )  # temp add velo space
            else:
                sample_queue[i][key] = np.zeros((0, 9))
            lidar_instances.append(
                LiDARInstance3DBoxes(
                    sample_queue[i][key], box_dim=sample_queue[i][key].shape[-1], origin=(0.5, 0.5, 0.5)
                ).convert_to(Box3DMode.LIDAR)
            )
        return lidar_instances

    @staticmethod
    def multiframe_to_batch_basic(sample_queue):
        batch = dict()
        for key in sample_queue[0].keys():

            if key in ["frame_id", "nori_id"]:
                batch[key] = [sample_queue[i][key] for i in range(len(sample_queue))]
            elif (
                key in ["imgs", "map1s", "map2s"] or key[-5:] == "_imgs"
            ):  # [List(bs): [List(queue_len): [List(num_cameras): [tensor: H*W*C]]]]
                batch[key] = np.expand_dims(
                    np.stack(
                        [
                            np.stack(
                                sample_queue[i][key],
                                axis=0,
                            )
                            for i in range(len(sample_queue))
                        ],
                        axis=0,
                    ),
                    axis=0,
                )
            elif key in ["radar_points", "timestamp"]:
                batch[key] = [sample_queue[i][key] for i in range(len(sample_queue))]
            elif key in [
                "tran_mats_dict", "map_tran_mats_dict", "bda_aug_param_dict", "post_rot_bda"
            ]:
                batch[key] = [[sample_queue[i][key] for i in range(len(sample_queue))]]
            elif key in ["img_semantic_seg", "sequence_data", "valid_mask_seq", "sequence_data_noise", "lane_bev_segment_map", "bev_manual_mask"]:
                batch[key] = np.expand_dims(
                    np.stack(
                        [
                            np.stack(
                                sample_queue[i][key],
                                axis=0,
                            )
                            for i in range(len(sample_queue))
                        ],
                        axis=0,
                    ),
                    axis=0,
                )
            elif key in ["fork_idx", "end_idx", "index_in_dataset", "scene_frame_idx", "seq_attribute"]:
                batch[key] = [[sample_queue[i][key] for i in range(len(sample_queue))]]
            elif key == "lidar2global":
                batch["l2g"] = [[sample_queue[i][key] for i in range(len(sample_queue))]]
        return batch

    def multiframe_to_batch_train(self, sample_queue):
        batch = dict()
        for key in sample_queue[0].keys():
            if key == "gt_labels":
                batch["gt_labels_3d"] = [[sample_queue[i][key] for i in range(len(sample_queue))]]
            elif key == "gt_boxes":
                batch["gt_bboxes_3d"] = [self._convert_to_lidar_instance(sample_queue, key)]
            elif key in [
                "points",
                "lidar2ego",
                "ego2global",
                "instance_inds",
                "targets",
                "pe_embedding",
                "tran_mats_dict",
                "map_tran_mats_dict"
                "group_idx",
                "scene_frame_idx",
            ]:
                batch[key] = [[sample_queue[i][key] for i in range(len(sample_queue))]]
            elif key == "predict_attribute":
                # gt_forecasting: gt_forecasting_locs/gt_forecasting_masks/gt_forecasting_angles/gt_forecasting_bboxes
                predict_attribute = dict()
                for sub_key in sample_queue[0][key].keys():
                    predict_attribute[sub_key] = [[sample_queue[i][key][sub_key] for i in range(len(sample_queue))]]
                batch[key] = [predict_attribute]
            elif key in [
                "sequence_pe",
                # 'sequence_data',
                # 'sequence_data_noise',
                # 'valid_mask_seq',
                "fork_idx",
                "end_idx",
                "seq_attribute",
            ]:
                batch[key] = [[sample_queue[i][key] for i in range(len(sample_queue))]]

            elif key in ['map_gt', 'map_local2global_info', 'map_gauss', "map_attr", "map_scene_weight", "valid_region"]:   # maptracker
                batch[key] = [[sample_queue[i][key] for i in range(len(sample_queue))]]
            elif key == 'map_label_mask':
                batch["map_label_mask"] = [[sample_queue[i]["map_label_mask"] for i in range(len(sample_queue))]]
            elif key == "non_focus_area":
                batch[key] = [[sample_queue[i][key] for i in range(len(sample_queue))]]

        return batch

    def get_img_metas(self, batch, sample_queue):
        batch["img_metas"] = [
            {
                "ida_mats": [sample_queue[i]["ida_mats"] for i in range(len(sample_queue))]
                if getattr(self, "image", False)
                else [[] for i in range(len(sample_queue))],
                "ida_mats_detail": [sample_queue[i]["ida_mats_detail"] for i in range(len(sample_queue))]
                if getattr(self, "image", False)
                else [[] for i in range(len(sample_queue))],
                "bda_aug_param_dict": [sample_queue[i]["bda_aug_param_dict"] for i in range(len(sample_queue))],  # bda
                "post_rot_bda": [sample_queue[i]["post_rot_bda"] for i in range(len(sample_queue))],  # bda
                "lidar2imgs": [sample_queue[i]["lidar2imgs"] for i in range(len(sample_queue))]
                if getattr(self, "image", False)
                else [[] for i in range(len(sample_queue))],
                "pad_shape": [
                    [
                        tuple(sample_queue[i]["imgs"][j].shape[1:]).__add__((3,))
                        for j in range(len(sample_queue[i]["imgs"]))
                    ]
                    for i in range(len(sample_queue))
                ]
                if getattr(self, "image", False)
                else [[] for i in range(len(sample_queue))],
                "img_shape": [
                    [
                        tuple(sample_queue[i]["imgs"][j].shape[1:]).__add__((3,))
                        for j in range(len(sample_queue[i]["imgs"]))
                    ]
                    for i in range(len(sample_queue))
                ]
                if getattr(self, "image", False)
                else [[] for i in range(len(sample_queue))],
                "box_type_3d": [LiDARInstance3DBoxes for i in range(len(sample_queue))],
                "lidar2ego": [sample_queue[i]["lidar2ego"] for i in range(len(sample_queue))],
                "ego2global": [sample_queue[i]["ego2global"] for i in range(len(sample_queue))],
                "scene_name": [sample_queue[i]["group_idx"] for i in range(len(sample_queue))],
            }
        ]

    def __getitem__(self, index: int) -> dict:
        while True:
            index_list = self.generate_track_data_indexes(index)
            sample_queue = []
            for i in range(len(index_list)):
                if index_list[i] < 0:  # 仅有一个 json 用于训练时，会出现负数 index
                    index = self._rand_index()
                    # print('22222')
                    break
                data_dict = self.get_single_data(index_list[i])
                if data_dict is None:
                    index = self._rand_index()
                    # print('33333', index_list[i], index)
                    break
                else:
                    sample_queue.append(data_dict)
            if len(sample_queue) == len(
                index_list
            ):  # and sum([len(sample_queue[i]["gt_labels"]) for i in range(len(sample_queue))]) != 0:
                break
            else:
                del sample_queue
        # print(index, index_list)
        assert len(sample_queue) == len(index_list), "len(sample_queue) != len(index_list)"
        sample_queue = self.pipeline(sample_queue)
        batch = {}  # reorg multi-frame in dict
        batch.update(self.multiframe_to_batch_basic(sample_queue))

        if self.mode == "train" or self.mode == "val":
            batch.update(self.multiframe_to_batch_train(sample_queue))
            if "img_semantic_seg" in batch:
                batch["img_semantic_seg"] = batch["img_semantic_seg"].astype(bool)
            if "lane_bev_segment_map" in batch:
                batch["lane_bev_segment_map"] = batch["lane_bev_segment_map"].astype(bool)
            if "bev_manual_mask" in batch:
                batch["bev_manual_mask"] = batch["bev_manual_mask"].astype(np.float32)
            if 'maptracker' in self.annotation_e2e_cfg:
                batch.update(self.gen_maptrack_local2global(sample_queue))
        else:
            if "points" in sample_queue[0]:
                batch["points"] = [[sample_queue[i]["points"] for i in range(len(sample_queue))]]
        
        # 热插拔
        if self.valid_cam is not None:
            for cam_id, cam_status in enumerate(self.valid_cam):
                # 热插拔temp, 默认顺序为cam120/cam30/camleft100/camright100
                if cam_status == 0:
                    batch["imgs"][:, :, cam_id, :, :, :] = 0

        batch["ori_imgs"] = copy.deepcopy(batch["imgs"])

        self.get_img_metas(batch, sample_queue)

        if "bda_mat" in sample_queue[0].keys():
            batch["img_metas"][0].update(bda_mat=[sample_queue[i]["bda_mat"] for i in range(len(sample_queue))])

        batch["roi_mask"] = np.asarray(self.roi_mask, dtype=np.float32)[np.newaxis]  # (1, 4)
        if self.fov_boardline is not None:
            batch["fov_boardline"] = np.array(self.fov_boardline)[np.newaxis]
        # NOTE: Method 1: 压缩语义分割数据(int64(8 Byte) -> bool(1 Byte)) 

        return batch

    def evaluate(self, det_annos: list, **kwargs):
        return self.evaluator.evaluate(det_annos, **kwargs)

    def evaluate_tracking(self, det_annos: list, **kwargs):
        return self.evaluator.evaluate_tracking(det_annos, **kwargs)
    
    def gen_maptrack_local2global(self, sample_queue, canvas_size = [60, 100]):
        """生成 map 的 track 信息, 即不同帧中 map 元素的对应关系, 算法逻辑如下:
            1. 对每个单帧 gt, 根据不同类别，每个类别内的 instance 生成 mask
            2. 在每个类别内，使用匈牙利匹配得到前后帧匹配关系，距离函数使用两个 mask 间的 iou
            3. 合并每一帧与上一帧的匹配关系，得到最终结果
        Args:
            sample_queue (list): clip info
            canvas_size (list):  画 mask 时画布大小

        Returns:
            res (dict): 每一帧里，每个类别下 instance local id 对应 的 instance global id
                {"all_local2global_info": [{0: {0:0, 1:1}, 1: {}, 2: {}}, ... ]
        """
        f_num = len(sample_queue)
        pc_range = self.annotation_e2e_cfg['maptracker']['map_lidar_range']
        roi_size = [pc_range[3]-pc_range[0], pc_range[4]-pc_range[1], pc_range[5]-pc_range[2]]
        origin = pc_range[:3]
        
        matchings_seq = []
        vectors_seq = []
        import copy
        for idx in range(f_num):
            if idx == 0:
                prev_data = sample_queue[idx]
            if idx == f_num - 1:  # prev_data is the last frame
                vectors_seq.append(prev_data['map_gt'])
                break
            curr_data = sample_queue[idx+1]
            prev_lidar2global = copy.deepcopy(prev_data['lidar2global'])
            prev_vectors = copy.deepcopy(prev_data['map_gt'])
            cur_lidar2global = copy.deepcopy(curr_data['lidar2global'])
            cur_vectors = copy.deepcopy(curr_data['map_gt'])
            matchings = match_two_consecutive_frames(
                prev_lidar2global, 
                prev_vectors, 
                cur_lidar2global, 
                cur_vectors, 
                roi_size, 
                origin, 
                canvas_size)
            
            matchings_seq.append(matchings)
            vectors_seq.append(prev_data['map_gt'])
            
            prev_data = curr_data
        
        ids_info = assign_global_ids(matchings_seq, vectors_seq)
        res = {"map_local2global_info": [ids_info]}
        
        return res

    @staticmethod
    def collate_fn(data: list, max_radar_num=200) -> dict:
        batch_collection = dict()
        for key, value in data[0].items():
            if key in ["imgs", "map1s", "map2s"] or key[-4:] == "imgs":
                data_list = np.concatenate([iter_data[key] for iter_data in data])
                batch_collection[key] = data_list
            elif key in ["img_semantic_seg", "sequence_data", "sequence_data_noise", "valid_mask_seq", "lane_bev_segment_map", "bev_manual_mask"]:
                data_list = np.concatenate([iter_data[key] for iter_data in data])
                batch_collection[key] = data_list
            elif key == "radar_points":
                data_list = np.concatenate(
                    [
                        np.concatenate(
                            [
                                padding_radar(iter_data[key][fidx], max_radar_num)[np.newaxis, ...]
                                for fidx in range(len(iter_data[key]))
                            ]
                        )[np.newaxis, ...]
                        for iter_data in data
                    ]
                )
                batch_collection[key] = data_list  # (2, 200, 11)
            elif key in ["roi_mask", "fov_boardline"]:
                data_list = np.concatenate([iter_data[key] for iter_data in data], axis=0)  # (bs, 4)
                batch_collection[key] = data_list
            else:
                data_list = []
                for iter_data in data:
                    data_list += iter_data[key]
                batch_collection[key] = data_list

        # NOTE: Method 2: 转化为Tensor数据(pin_memory需要设置为False)
        PrivateE2EDataset.convert_data2tensor(batch_collection)

        return batch_collection


if __name__ == "__main__":
    import mmcv
    from perceptron.Perceptron.perceptron.exps.end2end.private.admap.data_cfg.admap_annos_hf import (
        val_dataset_cfg as DATA_VAL_CFG,
    )

    data_val_cfg = mmcv.Config(DATA_VAL_CFG)
    data_val_cfg["mode"] = "val"
    data_val_cfg["loader"]["datasets_names"] = ["mapbmk_align"]
    data_val_cfg["loader"]["only_key_frame"] = False
    data_val_cfg["annotation"]["box"]["label_key"] = "pre_labels"
    data_val_cfg["annotation"]["box"]["occlusion_threshold"] = -1

    val_dataset = PrivateE2EDataset(
        **data_val_cfg,
    )
    length = len(val_dataset)
    print("length", length, len(val_dataset.loader_output["frame_index"]))
    for idx in range(len(val_dataset)):
        item = val_dataset.__getitem__(idx)
        from IPython import embed

        embed()
        assert None
        print(item.keys())
        exit()

    # train_loader = torch.utils.data.DataLoader(
    #         val_dataset,
    #         batch_size=2,
    #         drop_last=False,
    #         shuffle=False,
    #         collate_fn=PrivateE2EDataset.collate_fn,
    #         sampler=InfiniteSampler(len(val_dataset), seed=0)
    #         if dist.is_distributed()
    #         else None,
    #         pin_memory=True,
    #         num_workers=5,
    #     )

    # train_iter = iter(train_loader)
    # for step in range(len(train_loader)):
    #     try:
    #         data = next(train_iter)
    #     except StopIteration:
    #         train_iter = iter(train_loader)
    #         data = next(train_iter)
    #     if hasattr(val_dataset, "batch_postcollate_fn"):
    #         val_dataset.batch_postcollate_fn(data)
    #     if hasattr(val_dataset, "batch_preforward_fn"):
    #         val_dataset.batch_preforward_fn(data)
    #     from IPython import embed; embed()
    #     exit()
