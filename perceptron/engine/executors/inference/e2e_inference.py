import time
import refile
import pickle
import torch
from tqdm import tqdm
from typing import Sequence
import torch.distributed as dist
from mmcv.runner import get_dist_info
from perceptron.engine.callbacks import Callback
from perceptron.exps.base_exp import BaseExp
from perceptron.utils import torch_dist
from ..base_executor import BaseExecutor

from mmdet.core import encode_mask_results
import itertools

__all__ = ["End2endVisualizator"]


def collect_results_gpu(result_part, size, scene_order):
    rank, world_size = get_dist_info()
    # dump result part to tensor with pickle
    part_tensor = torch.tensor(bytearray(pickle.dumps(result_part)), dtype=torch.uint8, device="cuda")
    # gather all result part tensor shape
    shape_tensor = torch.tensor(part_tensor.shape, device="cuda")
    shape_list = [shape_tensor.clone() for _ in range(world_size)]
    dist.all_gather(shape_list, shape_tensor)
    # padding result part tensor to max length
    shape_max = torch.tensor(shape_list).max()
    part_send = torch.zeros(shape_max, dtype=torch.uint8, device="cuda")
    part_send[: shape_tensor[0]] = part_tensor
    part_recv_list = [part_tensor.new_zeros(shape_max) for _ in range(world_size)]
    # gather all result part
    dist.all_gather(part_recv_list, part_send)

    if rank == 0:
        part_list = []
        for recv, shape in zip(part_recv_list, shape_list):
            part_list.append(pickle.loads(recv[: shape[0]].cpu().numpy().tobytes()))
        # sort the results
        if scene_order:
            ordered_results = list(itertools.chain(*part_list))
        else:
            ordered_results = []
            for res in zip(*part_list):
                ordered_results.extend(list(res))
        # the dataloader may pad some samples
        ordered_results = ordered_results[:size]
        return ordered_results


class End2endVisualizator(BaseExecutor):
    def __init__(self, exp: BaseExp, callbacks: Sequence[Callback], logger=None, eval_interval: int = -1) -> None:
        super().__init__(exp, callbacks, logger)
        self.eval_interval = eval_interval
        # self.model_range = exp.model_range
        # self.model_range_quantize = exp.model_range_quantize

    def infer(self):
        self.epoch += 1
        if self.eval_interval != -1 and self.epoch % self.eval_interval != 0:
            return
        exp = self.exp
        local_rank = torch_dist.get_rank()

        print(f"[DEBUG] Creating validation iterator...")
        self.val_iter = iter(self.val_dataloader)
        print(f"[DEBUG] Validation iterator created")
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()
        torch.set_grad_enabled(False)

        results_det = []
        results_map = []
        dataset = self.val_dataloader.dataset
        print(f"[DEBUG] Starting evaluation loop, total steps: {len(self.val_dataloader)}")
        for step in tqdm(range(len(self.val_dataloader)), disable=(local_rank > 0)):
            # for step in range(len(self.val_dataloader)):
            print(f"[DEBUG] Step {step}: Getting next batch...")
            data = next(self.val_iter)
            print(f"[DEBUG] Step {step}: Batch received, keys: {data.keys()}")
            with torch.no_grad():
                if hasattr(dataset, "batch_postcollate_fn"):
                    print(f"[DEBUG] Step {step}: Running batch_postcollate_fn...")
                    dataset.batch_postcollate_fn(data)
                if hasattr(dataset, "batch_preforward_fn"):
                    print(f"[DEBUG] Step {step}: Running batch_preforward_fn...")
                    dataset.batch_preforward_fn(data)
                print(f"[DEBUG] Step {step}: Calling test_step...")
                result = exp.test_step(data)

                if isinstance(result, dict):
                    result_det = result["pred_dicts"]
                    result_map = result["pred_maps"]

                # ----------------------------det info--------------------------------- #
                if not isinstance(result_det, dict) and result_det and isinstance(result_det[0], tuple):
                    result_det = [
                        (bbox_results, encode_mask_results(mask_results)) for bbox_results, mask_results in result
                    ]
                # This logic is only used in panoptic segmentation test.
                elif result_det and isinstance(result_det[0], dict) and "ins_results" in result_det[0]:
                    for j in range(len(result_det)):
                        bbox_results, mask_results = result_det[j]["ins_results"]
                        result_det[j]["ins_results"] = (bbox_results, encode_mask_results(mask_results))

                # ----------------------------map info--------------------------------- #
                dts_lanes = []
                # for bs_idx in range(len(result_map[0])):
                #     for f_idx in range(len(result_map)):
                #         result = result_map[f_idx][bs_idx]
                #         max_x, min_x, max_y, min_y, max_z, min_z = self.model_range
                #         sequence_data_all_det, fork_ind, end_ind = get_fork_from_dt(
                #             result, self.model_range_quantize, max_x, min_x, max_y, min_y, max_z, min_z
                #         )
                #         dt_lanes = sequence_data_all_det  # out_inter_points(sequence_data_all_det, fork_ind, end_ind)
                #         dts_lanes.append(dt_lanes)

            results_det.extend(result_det)
            results_map.append(dts_lanes)
            self._invoke_callback("after_step", step, {})

        torch_dist.synchronize()
        scene_order = hasattr(self.val_dataloader.dataset, "scene_order") and self.val_dataloader.dataset.scene_order
        results_det = collect_results_gpu(results_det, len(self.val_dataloader.dataset), scene_order=scene_order)
        results_map = collect_results_gpu(results_map, len(self.val_dataloader.dataset), scene_order=scene_order)

        self._invoke_callback("after_eval", det_annos=results_det, map_annos=results_map)
        if local_rank == 0:
            eval_cfg = exp.data_val_cfg.eval_cfg
            kwargs = {}
            kwargs["jsonfile_prefix"] = refile.smart_path_join(
                exp.output_dir,
                refile.SmartPath(self.exp.cfg_path).stem + "_" + "_".join(eval_cfg.eval_ppl),
                time.ctime().replace(" ", "_").replace(":", "_"),
            )

            self.logger.info("eval ...")
            if eval_cfg.eval:
                eval_kwargs = eval_cfg.get("evaluation", {}).copy()
                # hard-code way to remove EvalHook args
                for key in ["interval", "tmpdir", "start", "gpu_collect", "save_best", "rule"]:
                    eval_kwargs.pop(key, None)
                eval_kwargs.update(dict(metric=eval_cfg.eval, **kwargs))
                eval_kwargs.update(dict(eval_cfg=eval_cfg))

                if "tracking" in eval_cfg.eval_ppl:
                    result_str = self.val_dataloader.dataset.evaluate_tracking(results_det, **eval_kwargs)
                else:
                    result_str = self.val_dataloader.dataset.evaluate(results_det, **eval_kwargs)

            self.logger.info(result_str)
