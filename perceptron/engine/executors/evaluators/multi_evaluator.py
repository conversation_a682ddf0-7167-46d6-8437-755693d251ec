from typing import Sequence
import torch
from tqdm import tqdm

from perceptron.engine.callbacks import Callback
from perceptron.exps.base_exp import BaseExp
from perceptron.utils import torch_dist

from ..base_executor import BaseExecutor

from perceptron.engine.executors.evaluators import Det3DEvaluator, Det2DEvaluator

__all__ = ["MultiEvaluator", "Det2DEvaluatorMulti", "Det3DEvaluatorMulti"]


class Det2DEvaluatorMulti(Det2DEvaluator):
    def __init__(
        self,
        exp: BaseExp,
        callbacks: Sequence["Callback"],
        task_name=None,
        logger=None,
        task_exp=None,
    ) -> None:
        super(Det2DEvaluatorMulti, self).__init__(exp, callbacks, logger)
        self.task_exp = task_exp
        self.task_name = task_name
        self.pred_idx = list(self.exp.tasks.keys()).index(self.task_name)

    @torch.no_grad()
    def eval(self):
        exp = self.exp

        dataset_name = exp.tasks[self.task_name]["dataset"]
        val_dataloader, tasks = self.val_dataloader[dataset_name]

        self.val_iter = iter(val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()

        evaluator = self.task_exp.evaluator
        evaluator.reset()

        for step in tqdm(range(len(val_dataloader))):

            data = next(self.val_iter)
            input_data = {"dataset_name": dataset_name, "data": data, "tasks": tasks}
            all_task_preds = exp.test_step(input_data)
            pred_item = all_task_preds[self.pred_idx]
            inputs, outputs = pred_item
            if torch_dist.is_available():
                torch_dist.synchronize()

            evaluator.process(inputs, outputs)

        self.logger.info("\n\n==> Evaluation of task-{} on dataset-{}\n".format(self.task_name, dataset_name))
        results = evaluator.evaluate()
        # An evaluator may return None when not in rank0.
        # Replace it by an empty dict instead to make it easier for downstream code to handle
        if results is None:
            results = {}


class Det3DEvaluatorMulti(Det3DEvaluator):
    def __init__(
        self,
        exp: BaseExp,
        callbacks: Sequence[Callback],
        logger=None,
        task_name=None,
        task_exp=None,
    ) -> None:
        super(Det3DEvaluatorMulti, self).__init__(exp, callbacks, logger)
        self.task_exp = task_exp
        self.task_name = task_name
        self.pred_idx = list(self.exp.tasks.keys()).index(self.task_name)

    def eval(self):
        exp = self.exp
        local_rank = torch_dist.get_rank()
        dataset_name = exp.tasks[self.task_name]["dataset"]
        val_dataloader, tasks = self.val_dataloader[dataset_name]

        self.val_iter = iter(val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()

        dataset = val_dataloader.dataset
        if hasattr(self.task_exp, "module"):
            class_names = self.task_exp.model.module.class_names
        else:
            class_names = self.task_exp.model.class_names

        preds = []
        for step in tqdm(range(len(val_dataloader)), disable=(local_rank > 0)):
            data = next(self.val_iter)
            input_data = {"dataset_name": dataset_name, "data": data, "tasks": tasks}
            all_task_preds = exp.test_step(input_data)
            pred_item = all_task_preds[self.pred_idx]
            if type(pred_item) == dict and "pred_dicts" in pred_item:
                pred_dicts = pred_item["pred_dicts"]
                preds += val_dataloader.dataset.generate_prediction_dicts(data, pred_dicts, class_names)
            # mmdetection e.g. FCOS3D
            elif type(pred_item) == list:
                preds += pred_item
            else:
                raise NotImplementedError

        if torch_dist.is_available():
            torch_dist.synchronize()
        preds = sum(map(list, zip(*torch_dist.all_gather_object(preds))), [])[: len(val_dataloader.dataset)]

        if local_rank == 0:
            self.logger.info("eval ...")
            self.logger.info("\n\n==> Evaluation of task-{} on dataset-{}\n".format(self.task_name, dataset_name))
            result_str, _ = dataset.evaluation(preds, class_names, output_dir=exp.output_dir)
            self.logger.info(result_str)
        self._invoke_callback("after_eval", det_annos=preds)


class MultiEvaluator(BaseExecutor):
    def __init__(
        self,
        exp: BaseExp,
        callbacks: Sequence[Callback],
        logger=None,
        evaluator_map=None,
    ) -> None:
        super().__init__(exp, callbacks, logger)
        self.evaluators = {}
        for task, task_info in self.exp.tasks.items():
            if not task_info["eval"]:
                continue
            task_exp = task_info["exp"]
            self.evaluators[task] = task_info["evaluator"](
                exp=exp,
                callbacks=callbacks,
                logger=logger,
                task_name=task,
                task_exp=task_exp,
            )

    def eval(self):
        for task, task_info in self.exp.tasks.items():
            if task_info["eval"]:
                self.logger.info("\n\n==> Evaluation of task: {}\n".format(task))
                self.evaluators[task].eval()
