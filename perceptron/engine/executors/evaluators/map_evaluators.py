import numpy as np
import torch
import cupy as cp
import os
import cv2
import refile
from typing import Sequence
from tqdm import tqdm
from perceptron.utils import torch_dist as dist
from perceptron.engine.callbacks import Callback
from perceptron.exps.base_exp import BaseExp
from perceptron.engine.executors.base_executor import BaseExecutor
import numpy as np
from perceptron.utils import torch_dist
import pickle
from mmcv.runner import get_dist_info
import itertools

from refile import smart_path_join
from perceptron.utils.maptracker_utils.video_utils import merge_video

'''
Use in /data/Perceptron-e2e/perceptron/engine/cli/e2e_cli.py, def get_evaluator()
'''
class Map_Evaluator(BaseExecutor):
    def __init__(self, exp: BaseExp, callbacks: Sequence["Callback"], 
                 logger=None, eval_interval: int = -1, output_dir=None) -> None:
        super(Map_Evaluator, self).__init__(exp, callbacks, logger)
        self.eval_interval = eval_interval
        self.output_dir = output_dir
        
    def eval(self):
        self.epoch += 1
        if self.eval_interval != -1 and self.epoch % self.eval_interval != 0:
            return
        exp = self.exp
        local_rank = torch_dist.get_rank()

        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()

        dataset = self.val_dataloader.dataset
        print(f"Saving vis results to {self.output_dir}")
        for step in tqdm(range(len(self.val_dataloader)), disable=(local_rank > 0)):
            data = next(self.val_iter)
            if hasattr(dataset, "batch_postcollate_fn"):
                dataset.batch_postcollate_fn(data)
            if hasattr(dataset, "batch_preforward_fn"):
                dataset.batch_preforward_fn(data)
            pred_item = exp.test_step(data) # eval with loss
            self.val_dataloader.dataset.evaluator(step, data, pred_item['pred_maps'], 
                                                  None, self.output_dir)

            self._invoke_callback("after_step", step, {})
            # from IPython import embed; embed()

        dist.synchronize()
        self.logger.info("😎 Map Evaluation Results: ")
        map_class_dict = self.val_dataloader.dataset.evaluator.map_class_dict
        for cls, cls_info in  map_class_dict.items():
            self.summary_result(cls, local_rank)
        self.summary_result('all', local_rank)
        
        self.val_dataloader.dataset.evaluator.init_eval_result()
        self._invoke_callback("after_eval")

    def summary_result(self, cls, local_rank):
        if cls not in ['mask', 'all']:
            num_tp, num_dt, num_gt = self.val_dataloader.dataset.evaluator.summary_single_cls(cls_name=cls)
        elif cls in ['all']:
            num_tp, num_dt, num_gt = self.val_dataloader.dataset.evaluator.summary_all_cls()
        else:
            return 
        num_tp = dist.reduce_sum(torch.tensor(num_tp).cuda()).item()
        num_dt = dist.reduce_sum(torch.tensor(num_dt).cuda()).item()
        num_gt = dist.reduce_sum(torch.tensor(num_gt).cuda()).item()
        # scene_order = hasattr(self.val_dataloader.dataset, "scene_order") and self.val_dataloader.dataset.scene_order
        # preds = collect_results_gpu(preds, len(self.val_dataloader.dataset), scene_order=scene_order)

        if local_rank == 0:
            precision = num_tp / max(num_dt, 1)
            recall = num_tp / max(num_gt, 1)
            f_score = 2 * recall * precision / max(recall + precision, 1e-6)
            self.logger.info(
                "✨ cls {} : TP / DT / GT / P / R / F1 -> {:.0f} / {:.0f} / {:.0f} / {:.4f} / {:.4f} / {:.4f} ".format(cls, num_tp, num_dt, num_gt, precision, recall, f_score)
            )

class Map_Evaluator_Subrange(BaseExecutor):
    def __init__(self, exp: BaseExp, callbacks: Sequence["Callback"], 
                 logger=None, eval_interval: int = -1, output_dir=None) -> None:
        super(Map_Evaluator_Subrange, self).__init__(exp, callbacks, logger)
        self.eval_interval = eval_interval
        self.output_dir = output_dir
        
    def eval(self):
        self.epoch += 1
        if self.eval_interval != -1 and self.epoch % self.eval_interval != 0:
            return
        exp = self.exp
        local_rank = torch_dist.get_rank()

        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()

        dataset = self.val_dataloader.dataset
        for step in tqdm(range(len(self.val_dataloader)), desc=f"{local_rank}"): # disable=(local_rank > 0)
            data = next(self.val_iter)
            if hasattr(dataset, "batch_postcollate_fn"):
                dataset.batch_postcollate_fn(data)
            if hasattr(dataset, "batch_preforward_fn"):
                dataset.batch_preforward_fn(data)
            pred_item = exp.test_step(data) # eval with loss

            self.val_dataloader.dataset.evaluator(step, data, pred_item['pred_maps'], 
                                                    loss_dict=None, output_dir=self.output_dir)

            self._invoke_callback("after_step", step, {})
            # from IPython import embed; embed()

        dist.synchronize()
        # self.val_dataloader.dataset.evaluator.save_dt_data(output_dir=self.output_dir)
        
        rank, world_size = get_dist_info()
        self.val_dataloader.dataset.evaluator.summary_result_subrange(self.logger, dist, local_rank, rank, world_size)
        
        self.val_dataloader.dataset.evaluator.init_eval_result()
        
        self._invoke_callback("after_eval")

        # save_video
        if rank == 0:
            sub_folder = self.exp.data_val_cfg_map["evaluator"]["val_output_dir"]
            pic_dir = smart_path_join(self.output_dir, sub_folder)
            mp4name = pic_dir.split("/")[-4] + pic_dir.split("/")[-3] + "-" + pic_dir.split("/")[-2] + "-" + pic_dir.split("/")[-1]
            mp4name = mp4name.replace(":", "-")
            save_s3_path = "s3://zqt/mtracker/expvis-videos/"  # todo: hardcode
            merge_video(pic_dir, mp4name, save_s3_path)

            
def collect_results_gpu(result_part, size, scene_order):
    rank, world_size = get_dist_info()
    # dump result part to tensor with pickle
    part_tensor = torch.tensor(bytearray(pickle.dumps(result_part)), dtype=torch.uint8, device="cuda")
    # gather all result part tensor shape
    shape_tensor = torch.tensor(part_tensor.shape, device="cuda")
    shape_list = [shape_tensor.clone() for _ in range(world_size)]
    torch.distributed.all_gather(shape_list, shape_tensor)
    # padding result part tensor to max length
    shape_max = torch.tensor(shape_list).max()
    part_send = torch.zeros(shape_max, dtype=torch.uint8, device="cuda")
    part_send[: shape_tensor[0]] = part_tensor
    part_recv_list = [part_tensor.new_zeros(shape_max) for _ in range(world_size)]
    # gather all result part
    torch.distributed.all_gather(part_recv_list, part_send)

    if rank == 0:
        part_list = []
        for recv, shape in zip(part_recv_list, shape_list):
            part_list.append(pickle.loads(recv[: shape[0]].cpu().numpy().tobytes()))
        # sort the results
        if scene_order:
            ordered_results = list(itertools.chain(*part_list))
        else:
            ordered_results = []
            for res in zip(*part_list):
                ordered_results.extend(list(res))
        # the dataloader may pad some samples
        ordered_results = ordered_results[:size]
        return ordered_results