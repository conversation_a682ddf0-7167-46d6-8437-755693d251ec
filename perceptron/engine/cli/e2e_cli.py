import os
import sys
import datetime

from perceptron.exps import global_cfg
from perceptron.utils import torch_dist
from perceptron.engine.callbacks import (
    CheckPointLoader,
    EvalResultsSaver,
    ClearMLCallback,
)
from perceptron.engine.executors import End2endEvaluator, End2endVisualizator
from perceptron.utils.log_utils import setup_logger
from perceptron.utils.misc import PyDecorator
from perceptron.engine.executors.evaluators.map_evaluators import Map_Evaluator

from .base_cli import BaseCli

__all__ = ["End2endCli"]


class End2endCli(BaseCli):
    @PyDecorator.overrides(BaseCli)
    def get_evaluator(self, callbacks=None):
        exp = self.exp
        if self.args.ckpt is None and self.args.eval:
            sys.exit("No checkpoint is specified for evaluation")
        output_dir = self._get_exp_output_dir()
        exp.output_dir = output_dir
        logger_file_name = (
            "eval_" + self.args.ckpt.split("/")[-1].replace("checkpoint_", "").replace(".pth", "") + ".log"
        )
        logger = setup_logger(output_dir, distributed_rank=self.env.global_rank(), filename=logger_file_name)

        self._set_basic_log_message(logger)
        if callbacks is None:
            callbacks = [
                self.env,
                CheckPointLoader(self.args.ckpt, weight_only=True),
                EvalResultsSaver(exp.output_dir),
            ]
        if self.args.clearml:
            callbacks.append(ClearMLCallback())

        if self.args.eval:
            if hasattr(exp.model_cfg, "map_head"):
                exp.model_cfg.pop("map_head")
            evaluator = End2endEvaluator(exp=exp, callbacks=callbacks, logger=logger)
        elif self.args.train_and_eval:
            evaluator = exp.eval_executor_class(exp=exp, callbacks=callbacks, logger=logger)
        elif self.args.eval_map:
            exp.data_val_cfg = exp.data_val_cfg_map
            if hasattr(exp.model_cfg, "det_head"):
                exp.model_cfg.pop("det_head")
            evaluator = Map_Evaluator(exp=exp, callbacks=callbacks, logger=logger, eval_interval=-1)
        else:
            raise NotImplementedError("Train Mode has no evaluator!")
        return evaluator

    def get_infer(self, callbacks=None):
        exp = self.exp
        if self.args.ckpt is None and self.args.eval:
            sys.exit("No checkpoint is specified for evaluation")
        output_dir = self._get_exp_output_dir()
        exp.output_dir = output_dir
        logger_file_name = (
            "eval_" + self.args.ckpt.split("/")[-1].replace("checkpoint_", "").replace(".pth", "") + ".log"
        )
        logger = setup_logger(output_dir, distributed_rank=self.env.global_rank(), filename=logger_file_name)

        self._set_basic_log_message(logger)
        if callbacks is None:
            callbacks = [
                self.env,
                CheckPointLoader(self.args.ckpt, weight_only=True),
                EvalResultsSaver(exp.output_dir),
            ]

        if self.args.infer:
            infer = End2endVisualizator(exp=exp, callbacks=callbacks, logger=logger)
        else:
            raise NotImplementedError("Train Mode has no evaluator!")
        return infer

    def _get_exp_output_dir(self):
        exp_dir = os.path.join(global_cfg.output_root_dir, self.exp.exp_name)
        os.makedirs(exp_dir, exist_ok=True)
        output_dir = None
        if self.args.ckpt:
            if not "s3://" == self.args.ckpt[:5]:
                output_dir = os.path.dirname(os.path.dirname(os.path.abspath(self.args.ckpt)))
            else:
                remote_path = self.args.ckpt[5:]
                remote_format = len(self.args.ckpt[5:].split("/"))
                if remote_format <= 2:
                    ckpt_name = remote_path.split("/")[0]
                elif remote_path.split("/")[-2] == "dump_model":
                    ckpt_name = remote_path.split("/")[-3]
                else:
                    ckpt_name = remote_path.split("/")[-2]
                output_dir = os.path.abspath(("./outputs/" + ckpt_name + "/"))
        elif self.env.global_rank() == 0:
            output_dir = os.path.join(exp_dir, datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S"))
            os.makedirs(output_dir, exist_ok=True)
            # make a symlink "latest"
            symlink, symlink_tmp = os.path.join(exp_dir, "latest"), os.path.join(exp_dir, "latest_tmp")
            if os.path.exists(symlink_tmp):
                os.remove(symlink_tmp)
            os.symlink(os.path.relpath(output_dir, exp_dir), symlink_tmp)
            os.rename(symlink_tmp, symlink)
        output_dir = torch_dist.all_gather_object(output_dir)[0]
        return output_dir
