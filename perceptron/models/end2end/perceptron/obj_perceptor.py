import mmcv
import torch
import torch.nn as nn

import warnings
from typing import Any
from mmdet.models import build_loss
from perceptron.models.multisensor_fusion import BaseMultiSensorFusion, ForceFp32
from perceptron.layers.head.det3d.cmt_head import CMTE2EHead
from perceptron.layers.head.mot3d import SpatialTemporalReasoner
from perceptron.layers.blocks_3d.prediction3d import TrackTransform
from perceptron.layers.losses.e2e3d import Tracking<PERSON>ossCombo
from perceptron.utils.e2e_utils.runtime_tracker import RunTimeTracker
from perceptron.utils.e2e_utils.instance import Instances, TrackInstance, get_query_embeds


class DetHead(nn.Module):
    def __init__(
        self, det_head_cfg: mmcv.Config, train_cfg: mmcv.Config = None, test_cfg: mmcv.Config = None, **kwargs
    ):
        super().__init__()
        self.det_head_cfg = det_head_cfg
        self.train_cfg = train_cfg
        self.test_cfg = test_cfg
        self.dense_head = self.build_dense_head()

    def build_dense_head(self):

        assert "use_dn" in self.det_head_cfg, "use_dn should in self.det_head_cfg"
        if self.det_head_cfg["use_dn"]:
            for k in ["scalar", "noise_scale", "noise_trans", "dn_weight", "split"]:
                if k not in self.det_head_cfg:
                    warnings.warn(f"{k} should be specify in det_head_cfg, use default now")

        if "type" not in self.det_head_cfg or self.det_head_cfg["type"] == "CMTE2EHead":
            dense_head_module = CMTE2EHead(
                norm_bbox=True,
                train_cfg=self.train_cfg,
                test_cfg=self.test_cfg,
                **self.det_head_cfg,
            )
        else:
            raise NotImplementedError(f"type: {self.det_head_cfg['type']} is not Impl.")

        return dense_head_module

    @ForceFp32(
        apply_to=(
            "img_feats",
            "query_feats",
            "img_metas",
            "query_embeds",
            "reference_points",
            "radar_points",
            "radar_output",
            "lidar_feats",
            "query_embedding",
        )
    )
    def forward(
        self,
        img_feats: torch.tensor,
        img_metas: list,
        query_feats: torch.tensor,
        query_embeds: torch.tensor,
        reference_points: torch.tensor,
        tracking: bool,
        radar_points: torch.tensor = None,
        radar_output: torch.tensor = None,
        roi_mask: torch.tensor = None,
        attn_mask: torch.tensor = None,
        mask_dict: dict = None,
        lidar_feats: torch.tensor = None,
        query_embedding: torch.tensor = None,
        fov_boardline: torch.tensor = None,
    ) -> Any:
        bs = query_feats.shape[1]
        if roi_mask is None:
            roi_mask = torch.tensor([-25.6, -80.0, 25.6, 204.8]).reshape(1, -1).expand((bs, -1))
        else:
            assert (roi_mask.shape[0] == bs) and (roi_mask.ndim == 2), f"roi_mask.shape {roi_mask.shape}; bs {bs}"

        forward_ret_dict = self.dense_head(
            img_feats,
            img_metas,
            query_feats,
            query_embeds,
            reference_points,
            attn_mask,
            mask_dict,
            radar_points,
            radar_output,
            lidar_feats=lidar_feats,
            query_embedding=query_embedding,
        )

        losses = None
        if img_metas["ff_gt_bboxes_list"] is not None and not tracking:
            with torch.cuda.amp.autocast(enabled=False):
                losses = self.dense_head.loss(img_metas, forward_ret_dict, roi_mask, fov_boardline)
                return forward_ret_dict, losses
        else:
            return forward_ret_dict, losses


class Obstacle(BaseMultiSensorFusion):
    r"""
    `BEVFusion`: Multi-Task Multi-Sensor Fusion with Unified Bird's-Eye View Representation.

    `Reference`: https://arxiv.org/abs/2205.13542
    """

    def __init__(self, model_cfg) -> Any:
        super(Obstacle, self).__init__()

        # base setting
        self.cfg = model_cfg
        self.num_query = model_cfg.num_query
        self.class_names = model_cfg.class_names
        self.num_classes = model_cfg.num_classes
        self.embed_dims = model_cfg.get("embed_dims", 256)  # TODO: use det_head.hidden_dims?
        self.freeze_bn = model_cfg.get("freeze_bn", False)
        self.use_radar_init = model_cfg.get("use_radar_init", False)

        # multi-task setting
        self.tracking = model_cfg.get("tracking", False)
        self.contrastive = model_cfg.get("contrastive", False)
        self.use_relative_ts = model_cfg.get("use_relative_ts", False)

        # ------------ Task: Det ----------------
        # DN
        if "dn_cfg" in model_cfg:
            self.dn_cfg = model_cfg["dn_cfg"]
        else:
            self.dn_cfg = {"use_dn": False}

        self.use_dn = self.dn_cfg["use_dn"]
        # 保证dn_cfg不在self.cfg.det_head中
        for k in self.dn_cfg:
            if k == "use_dn":
                continue
            assert k not in self.cfg.det_head, f"{k} is already in self.cfg.det_head, {self.cfg.det_head.keys()}"
        # 更新dn_cfg到det_head中
        self.cfg.det_head.update(self.dn_cfg)
        self.det_head = self._configure_det_head()

        # ------------ Task: Track ----------------
        if self.tracking:
            assert self.det_head, "Can not tracking without DET_HEAD!"
            assert self.cfg.get("tracking_module", False)
            self.tracking_module = self._configure_tracking_module()
            self.tracking_criterion: TrackingLossCombo = build_loss(self.cfg.track_loss)
            self.runtime_tracker = RunTimeTracker(**self.cfg.runtime_tracker)  # Inference time tracker

            self.if_update_ego = model_cfg.get("if_update_ego", False)
            self.if_update_query = self.cfg.tracking_module.get("if_update_query", False)
            self.fut_prediction_ref_update = model_cfg.get("fut_prediction_ref_update", False)
            self.with_velocity = self.cfg.tracking_module.get("with_velocity", False)

            self.hist_len = self.cfg.tracking_module.hist_len
            self.fut_len = self.cfg.tracking_module.fut_len
            self.tracking_cfg = (self.hist_len, self.fut_len)
            self.track_transform = TrackTransform(
                self.det_head.dense_head.pc_range, self.tracking_cfg, feat_transform=self.if_update_query
            )

        self.init_params_and_layers()

    def init_params_and_layers(self):
        """Generate the instances for tracking, especially the object queries"""
        # query initialization for detection: reference points, mapping fourier encoding to embed_dims
        self.reference_points = nn.Embedding(self.num_query, 3)
        self.query_embedding = nn.Sequential(
            # nn.Linear(self.det_head.dense_head.hidden_dim * 3 // 2, self.det_head.dense_head.hidden_dim),
            nn.Linear(384, self.det_head.dense_head.hidden_dim),  # for vit
            nn.ReLU(inplace=True),
            nn.Linear(self.det_head.dense_head.hidden_dim, self.det_head.dense_head.hidden_dim),
        )
        nn.init.uniform_(self.reference_points.weight.data, 0, 1)

        # embedding initialization for tracking
        if self.tracking:
            self.query_feat_embedding = nn.Embedding(self.num_query, self.embed_dims)
            nn.init.zeros_(self.query_feat_embedding.weight)

    def forward(
        self,
        **kwargs,
    ) -> Any:
        if self.tracking:
            if self.training:
                return self.forward_track(**kwargs)
            else:
                return self.inference_track(**kwargs)
        else:
            return self.forward_det(**kwargs)

    def forward_det(
        self,
        data_shape=None,
        img_feats=None,
        radar_points=None,
        radar_output=None,
        lidar_feats=None,
        bev_embedding=None,
        img_metas=None,
        roi_mask: torch.tensor = None,
        fov_boardline: torch.tensor = None,
        **kwargs,
    ) -> Any:
        if not self.training:
            roi_mask = None  # 测试过程中, 去掉roi_mask

        batch_size, num_frame, N, C, H, W = data_shape

        # Running over all the frames one by one
        # self.runtime_tracker.empty()
        for frame_idx in range(num_frame):
            if radar_points is not None:
                radar_output, radar_points = radar_output[:, frame_idx, :, :], radar_points[:, frame_idx, :, :]  # bug?
            if lidar_feats is not None:
                lidar_feats_ = lidar_feats[:, frame_idx]
            else:
                lidar_feats_ = None
            # 1. prepare reference_points
            if radar_points is not None and self.use_radar_init:
                reference_points = self.reference_points.weight.clone().unsqueeze(0).repeat(batch_size, 1, 1)
                reference_points = torch.cat([radar_points, reference_points], dim=1)
            else:
                reference_points = self.reference_points.weight.clone().unsqueeze(0).repeat(batch_size, 1, 1)

            if self.training and self.use_dn:
                # reference_points (b, n, c): torch.Size([2, 872, 3])
                try:
                    reference_points, attn_mask, mask_dict = self.det_head.dense_head.prepare_for_dn(
                        batch_size, reference_points, img_metas[frame_idx]
                    )
                except Exception as e:
                    print(e)
                    attn_mask, mask_dict = None, None
            else:
                attn_mask, mask_dict = None, None

            query_embeds = get_query_embeds(
                reference_points,
                radar_points,
                self.query_embedding,
                bev_embedding,
                self.det_head.dense_head.hidden_dim,
            )
            query_feats = torch.zeros_like(query_embeds).transpose(0, 1)

            out, loss_dict = self.det_head(  # 没有lidar
                img_feats[0] if img_feats is not None else None,
                img_metas[frame_idx],
                query_feats,
                query_embeds,
                reference_points,
                self.tracking,
                radar_points=radar_points,
                radar_output=radar_output,
                roi_mask=roi_mask,
                attn_mask=attn_mask,
                mask_dict=mask_dict,
                lidar_feats=lidar_feats_,
                query_embedding=self.query_embedding,
                fov_boardline=fov_boardline,
            )

        if self.training:
            return out, loss_dict, {}
        else:
            return out

    def forward_track(
        self,
        data_shape=None,
        img_feats=None,
        radar_points=None,
        radar_output=None,
        lidar_feats=None,
        labels=None,
        predict_attribute=None,
        bev_embedding=None,
        img_metas=None,
        l2g=None,
        timestamp=None,
        **kwargs,
    ) -> Any:

        # extract base feature
        batch_size, num_frame, N, C, H, W = data_shape
        img_feats = img_feats[0].view(batch_size, num_frame, N, -1, img_feats[0].shape[-2], img_feats[0].shape[-1])

        # Empty the runtime_tracker
        # Perform the perceptron on every frame
        # new sequence
        # perform only at the start of sliding_window,
        if self.runtime_tracker.timestamp is None or abs(timestamp[0] - self.runtime_tracker.timestamp) > (
            10 * 1e6
        ):  # or self.runtime_tracker.frame_index >= 20:
            self.runtime_tracker.timestamp = timestamp[0]
            self.runtime_tracker.track_instances = None
            self.runtime_tracker.l2g = None
            self.runtime_tracker.time_delta = 0
            self.runtime_tracker.frame_index = 0

        outs = list()
        # Running over all the frames one by one
        for frame_idx in range(num_frame):

            self.runtime_tracker.time_delta = timestamp[frame_idx] - self.runtime_tracker.timestamp
            self.runtime_tracker.timestamp = timestamp[frame_idx]

            active_track_instances = self.runtime_tracker.track_instances
            if active_track_instances is not None and frame_idx == 0:
                active_track_instances = active_track_instances.detach()

            if active_track_instances is not None:  # frame_idx < num_frame - 1:
                time_delta = self.runtime_tracker.time_delta  # timestamp[frame_idx + 1] - timestamp[frame_idx]
                """Update the reference points according to the motion prediction/velocities"""
                active_track_instances = self.track_transform.update_reference_points(
                    active_track_instances, time_delta, use_prediction=self.fut_prediction_ref_update, is_infer=False
                )
            if self.if_update_ego and active_track_instances is not None:  # frame_idx < num_frame - 1:
                active_track_instances = self.track_transform.update_ego(
                    active_track_instances, self.runtime_tracker.l2g, l2g[0][frame_idx]
                )
            if self.if_update_query and active_track_instances is not None:  #:
                self.track_transform.transform_hist_embeddings(
                    active_track_instances, self.runtime_tracker.l2g, l2g[0][frame_idx]
                )
            if active_track_instances is not None:  #:
                if radar_points is not None and self.use_radar_init:
                    active_track_instances = self.track_transform.sync_pos_embedding(
                        active_track_instances, radar_points, self.query_embedding, bev_embedding
                    )
                else:
                    active_track_instances = self.track_transform.sync_pos_embedding(
                        active_track_instances, None, self.query_embedding, None
                    )

            # 0. Prepare groud_truth and img_metas
            img_metas_single_frame = img_metas[frame_idx]
            ff_gt_bboxes, ff_gt_labels, ff_instance_ids = labels[frame_idx]

            """ Task1: Detection """
            if radar_points is not None:
                radar_output_i, radar_point_i = radar_output[:, frame_idx, :, :], radar_points[:, frame_idx, :, :]
            else:
                radar_output_i, radar_point_i = None, None

            if lidar_feats is not None:
                lidar_feat = lidar_feats[:, frame_idx]
            else:
                lidar_feat = None

            # 1.1 Generate/Update Track Instances
            if radar_points is not None and self.use_radar_init:
                next_frame_track_instances = TrackInstance.generate_empty_track_instance(
                    self.reference_points,
                    self.query_embedding,
                    self.num_classes,
                    self.tracking,
                    self.tracking_cfg,
                    self.embed_dims,
                    self.query_feat_embedding,
                    radar_point_i[0],
                    bev_embedding,
                    self.det_head.dense_head.hidden_dim,
                )
            else:
                next_frame_track_instances = TrackInstance.generate_empty_track_instance(
                    self.reference_points,
                    self.query_embedding,
                    self.num_classes,
                    self.tracking,
                    self.tracking_cfg,
                    self.embed_dims,
                    self.query_feat_embedding,
                )

            if active_track_instances is not None:
                if self.runtime_tracker.hist_fp_num > 0:
                    active_track_instances = self.runtime_tracker.insert_history_fp(active_track_instances)
                next_frame_track_instances = Instances.cat([next_frame_track_instances, active_track_instances])

            # 1.2 PETR detection head
            track_instances = next_frame_track_instances

            out, loss_dict = self.det_head(
                img_feats[:, frame_idx, :, :, :, :].view(batch_size * N, -1, img_feats.shape[-2], img_feats.shape[-1]),
                img_metas_single_frame,
                track_instances.query_feats.unsqueeze(1).repeat(1, batch_size, 1),
                track_instances.query_embeds.unsqueeze(0).repeat(batch_size, 1, 1),
                track_instances.reference_points.unsqueeze(0).repeat(batch_size, 1, 1),
                self.tracking,
                radar_points=radar_point_i,
                radar_output=radar_output_i,
                lidar_feats=lidar_feat,
                query_embedding=self.query_embedding,
                # roi_mask,
            )

            # 1.3 Record the information into the track instances cache
            out = out[0]
            track_instances = TrackInstance.load_detection_output_into_cache(
                self.query_embedding, track_instances, out, timestamp=timestamp, use_relative_ts=self.use_relative_ts
            )
            out["track_instances"] = track_instances
            # out["points"] = points[0][frame_idx]

            # 1.4 Loss computation for the detection
            out["loss_dict"] = self.tracking_criterion.loss_single_frame(
                frame_idx,
                ff_gt_bboxes,
                ff_gt_labels,
                ff_instance_ids,
                out,
                None,
            )

            """ Task2: Tracking """
            # 2.1 Spatial-temporal reasoning
            track_instances = self.tracking_module(track_instances)

            # 2.2 Loss computation for History Reasoning
            if self.tracking_module.history_reasoning:
                out["loss_dict"] = self.tracking_criterion.loss_mem_bank(
                    frame_idx,
                    out["loss_dict"],
                    ff_gt_bboxes,
                    ff_gt_labels,
                    ff_instance_ids,
                    track_instances,
                )

            # 2.3 Loss computation for Future Reasoning
            if self.tracking_module.future_reasoning:
                active_mask = track_instances.obj_idxes >= 0
                out["loss_dict"] = self.tracking_criterion.loss_prediction(
                    frame_idx,
                    out["loss_dict"],
                    track_instances[active_mask],
                    predict_attribute[0]["gt_forecasting_locs"][0][frame_idx],
                    predict_attribute[0]["gt_forecasting_masks"][0][frame_idx],
                    ff_instance_ids,
                    self.fut_len,
                    loss_key="fut",
                    gt_vel=None
                    if not self.with_velocity
                    else predict_attribute[0]["gt_forecasting_velocity"][0][frame_idx],
                )

            # 2.3.1 Loss computation for contrastive loss
            if self.contrastive:
                active_mask = track_instances.obj_idxes >= 0
                if (
                    self.runtime_tracker.history_track_memory is not None
                    and len(self.runtime_tracker.history_track_memory) >= self.runtime_tracker.hist_fp_memory_len // 10
                    and active_mask.sum() > 0
                ):
                    out["loss_dict"] = self.tracking_criterion.loss_contrastive_loss(
                        frame_idx,
                        out["loss_dict"],
                        track_instances[active_mask].hist_embeds[:, -1, :],
                        track_instances[active_mask].obj_idxes,
                        track_instances[active_mask].hist_embeds[:, -2, :],
                        track_instances[active_mask].obj_idxes,
                        self.runtime_tracker.history_track_memory.query_feats[:-50],
                        self.runtime_tracker.history_track_memory.obj_idxes[:-50],
                        track_instances[active_mask].hist_padding_masks[:, -2],
                    )
                else:
                    out["loss_dict"][f"f{frame_idx}.loss_content_contrast"] = (
                        torch.zeros_like(out["loss_dict"][f"f{frame_idx}.loss_mem_bbox"]) * 0
                    )

            # 2.4 Summarization for Tracking
            track_instances = TrackInstance.tracking_summarization(
                self.runtime_tracker.record_threshold,
                track_instances,
                self.tracking_module.future_reasoning,
                self.fut_len,
                self.det_head.dense_head.pc_range,
                is_infer=False,
            )

            # 4. Prepare for next frame
            active_mask = self.runtime_tracker.get_active_mask(track_instances, training=True)
            track_instances.track_query_mask[active_mask] = True

            self.runtime_tracker.track_instances = track_instances[active_mask]
            self.runtime_tracker.l2g = l2g[0][frame_idx]
            self.runtime_tracker.frame_index += 1
            outs.append(out)
        losses = self.tracking_criterion(outs)

        if self.training:
            return outs, losses, {}
        else:
            return outs

    def inference_track(
        self,
        data_shape=None,
        img_feats=None,
        radar_points=None,
        radar_output=None,
        lidar_feats=None,
        bev_embedding=None,
        img_metas=None,
        l2g=None,
        timestamp=None,
        **kwargs,
    ) -> Any:

        # extract base feature
        batch_size, num_frame, N, C, H, W = data_shape
        img_feats = img_feats[0].view(batch_size, num_frame, N, -1, img_feats[0].shape[-2], img_feats[0].shape[-1])

        # new sequence
        if self.runtime_tracker.timestamp is None or abs(timestamp[0] - self.runtime_tracker.timestamp) > (
            10 * 1e6
        ):  # or self.runtime_tracker.frame_index >= 20:
            self.runtime_tracker.timestamp = timestamp[0]
            self.runtime_tracker.current_seq += 1
            self.runtime_tracker.track_instances = None
            self.runtime_tracker.current_id = 0
            self.runtime_tracker.l2g = None
            self.runtime_tracker.time_delta = 0
            self.runtime_tracker.frame_index = 0
        self.runtime_tracker.time_delta = timestamp[0] - self.runtime_tracker.timestamp
        self.runtime_tracker.timestamp = timestamp[0]

        # Running over all the frames one by one
        # processing the queries from t-1
        prev_active_track_instances = self.runtime_tracker.track_instances
        for frame_idx in range(num_frame):

            # 0. Prepare groud_truth and img_metas
            img_metas_single_frame = img_metas[frame_idx]

            """ Task1: Detection """
            # 1.1 Generate Empty Track Instances
            if radar_points is not None:
                radar_output_i, radar_point_i = radar_output[:, frame_idx, :, :], radar_points[:, frame_idx, :, :]
            else:
                radar_output_i, radar_point_i = None, None

            if lidar_feats is not None:
                lidar_feat = lidar_feats[:, frame_idx]
            else:
                lidar_feat = None

            if radar_points is not None and self.use_radar_init:
                track_instances = TrackInstance.generate_empty_track_instance(
                    self.reference_points,
                    self.query_embedding,
                    self.num_classes,
                    self.tracking,
                    self.tracking_cfg,
                    self.embed_dims,
                    self.query_feat_embedding,
                    radar_point_i[0],
                    bev_embedding,
                    self.det_head.dense_head.hidden_dim,
                )
            else:
                track_instances = TrackInstance.generate_empty_track_instance(
                    self.reference_points,
                    self.query_embedding,
                    self.num_classes,
                    self.tracking,
                    self.tracking_cfg,
                    self.embed_dims,
                    self.query_feat_embedding,
                )

            # 1.2 Update Previous Active Tracks (transform between adjacent frames)
            if prev_active_track_instances is not None:
                time_delta = self.runtime_tracker.time_delta
                """Update the reference points according to the fut prediction/velocities"""
                prev_active_track_instances = self.track_transform.update_reference_points(
                    prev_active_track_instances,
                    time_delta,
                    use_prediction=self.fut_prediction_ref_update,
                    is_infer=True,
                )
                if self.if_update_ego:
                    prev_active_track_instances = self.track_transform.update_ego(
                        prev_active_track_instances, self.runtime_tracker.l2g, l2g[0][frame_idx]
                    )
                if self.if_update_query:
                    prev_active_track_instances = self.track_transform.transform_hist_embeddings(
                        prev_active_track_instances, self.runtime_tracker.l2g, l2g[0][frame_idx]
                    )

                if radar_points is not None and self.use_radar_init:
                    prev_active_track_instances = self.track_transform.sync_pos_embedding(
                        prev_active_track_instances, radar_points, self.query_embedding, bev_embedding
                    )
                else:
                    prev_active_track_instances = self.track_transform.sync_pos_embedding(
                        prev_active_track_instances, None, self.query_embedding, None
                    )

                track_instances = Instances.cat([track_instances, prev_active_track_instances])

            self.runtime_tracker.l2g = l2g[0][frame_idx]
            self.runtime_tracker.timestamp = timestamp[0]

            # 1.3 PETR detection head
            # cx, cy, w, l, cz, h, rot --> cx, cy, cz, w, l, h, rot
            out, loss_dict = self.det_head(
                img_feats[:, frame_idx, :, :, :, :].reshape(
                    batch_size * N, -1, img_feats.shape[-2], img_feats.shape[-1]
                ),
                img_metas_single_frame,
                track_instances.query_feats.unsqueeze(1).repeat(1, batch_size, 1),
                track_instances.query_embeds.unsqueeze(0).repeat(batch_size, 1, 1),
                track_instances.reference_points.unsqueeze(0).repeat(batch_size, 1, 1),
                self.tracking,
                radar_points=radar_point_i,
                radar_output=radar_output_i,
                lidar_feats=lidar_feat,
                query_embedding=self.query_embedding,
                # roi_mask,
            )

            # 1.4 Record the information into the track instances cache
            out = out[0]
            track_instances = TrackInstance.load_detection_output_into_cache(
                self.query_embedding, track_instances, out, timestamp=timestamp, use_relative_ts=self.use_relative_ts
            )
            out["track_instances"] = track_instances

            """ Task2: Tracking """
            # 2.1 Spatial-temporal reasoning
            track_instances = self.tracking_module(track_instances)

            # 2.2 Summarization for Tracking
            track_instances = TrackInstance.tracking_summarization(
                self.runtime_tracker.record_threshold,
                track_instances,
                self.tracking_module.future_reasoning,
                self.fut_len,
                self.det_head.dense_head.pc_range,
                is_infer=True,
            )
            out["all_cls_scores"][-1][0, :] = track_instances.logits
            out["all_bbox_preds"][-1][0, :] = track_instances.bboxes
            out["all_motion_forecasting"] = track_instances.fut_predictions.clone()

            """ Post Process """
            # 4.1 Track class filtering: before decoding bboxes, only leave the objects under tracking categories
            max_cat = torch.argmax(out["all_cls_scores"][-1, 0, :].sigmoid(), dim=-1)
            related_cat_mask = max_cat < 10  # we set the first 7 classes as the tracking classes of nuscenes
            track_instances = track_instances[related_cat_mask]
            out["all_cls_scores"] = out["all_cls_scores"][:, :, related_cat_mask, :]
            out["all_bbox_preds"] = out["all_bbox_preds"][:, :, related_cat_mask, :]
            if out["all_motion_forecasting"] is not None:
                out["all_motion_forecasting"] = out["all_motion_forecasting"][related_cat_mask, ...]

            # 4.2 assign ids
            active_mask = track_instances.scores > self.runtime_tracker.threshold
            active_mask &= (
                track_instances.bboxes[:, 0:2]
                > (self.det_head.dense_head.bbox_coder.post_center_range[:2] + 10.01).to(track_instances.bboxes.device)
            ).all(1) & (
                track_instances.bboxes[:, 0:2]
                < (self.det_head.dense_head.bbox_coder.post_center_range[3:5] - 10.01).to(track_instances.bboxes.device)
            ).all(
                1
            )
            for i in range(len(track_instances)):
                if track_instances.obj_idxes[i] < 0:
                    track_instances.obj_idxes[i] = self.runtime_tracker.current_id
                    self.runtime_tracker.current_id += 1
                    if active_mask[i]:
                        track_instances.track_query_mask[i] = True
            out["track_instances"] = track_instances

            # 4.3 Prepare for the next frame and output
            score_mask = track_instances.scores > self.runtime_tracker.output_threshold
            out["all_masks"] = score_mask.clone()
            out["active_mask"] = active_mask.clone()

            # self.runtime_tracker.update_active_tracks(active_track_instances)
            self.runtime_tracker.update_active_tracks(track_instances, active_mask)

            # each time, only run one frame
            self.runtime_tracker.frame_index += 1
            break

        return out

    def _configure_det_head(self):
        return DetHead(self.cfg.det_head, self.cfg.train_cfg["pts"], self.cfg.test_cfg["pts"])

    def _configure_tracking_module(self):
        return SpatialTemporalReasoner(**self.cfg.tracking_module)
