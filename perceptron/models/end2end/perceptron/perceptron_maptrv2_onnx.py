import mmcv
import torch
import torch.nn as nn
import numpy as np

import copy
from typing import Any, List
from refile import smart_open

from perceptron.models.end2end.perceptron.perceptron_maptrv2 import VisionEncoder
from perceptron.utils.map_deploy_utils.deploy_utils import get_current_exp_class, MapDeployConfig

class MapDeployModel(nn.Module):
    r"""
    `BEVFusion`: Multi-Task Multi-Sensor Fusion with Unified Bird's-Eye View Representation.

    `Reference`: https://arxiv.org/abs/2205.13542
    """

    def __init__(self, model) -> Any:
        super(MapDeployModel, self).__init__()

        self.camera_encoder = model.camera_encoder
        self.map_head = model.map_head
        self.train_backbone=False

    def extract_img_feat(self, img):
        # Extract img_feats
        batch_size, num_frame, N, C, H, W = img.shape
        img = img.view(batch_size * num_frame * N, C, H, W)
        img = img.view(batch_size, num_frame, N, C, H, W)
        img_feats = self.camera_encoder(img, self.train_backbone)
        return img_feats

    def forward(
        self,
        imgs=None,
        lidar2imgs=None,
        ida_mats=None,
        **kwargs,
    ) -> Any:
        # # 0. Prepare groud_truth and img_metas
        bs, num_frame, N, C, H, W = imgs.shape

        # 1. Extract base feature
        img_feats = self.camera_encoder(imgs.contiguous(), self.train_backbone)

        assert num_frame == 1, "infer single mode only support num_frame==1"
        assert bs == 1, "infer single mode only support bs==1"
        f_c, f_h, f_w = img_feats[0].shape[-3:]
        img_feats = img_feats[0].reshape(bs, num_frame, N, f_c, f_h, f_w)
        img_feats = img_feats[:, 0, ...]
        img_feats = img_feats.to(self.map_head.head.bev_embedding.weight.dtype)
        img_metas = {'lidar2imgs': lidar2imgs, 'ida_mats': ida_mats}
        preds_dicts = self.map_head.head.forward_deploy(mlvl_feats=[img_feats],lidar_feat=None,img_metas=img_metas)

        all_cls_scores = preds_dicts['all_cls_scores']  # (num_dec_layer, bs, 50, 3=num_classes)
        all_pts_preds = preds_dicts['all_pts_preds']  # (num_dec_layer, bs, 50, 20, 2)

        # support lane attr
        all_lane_colors = preds_dicts['all_lane_colors']  # (6, bs, 50, 20x3)
        all_lane_dashs = preds_dicts['all_lane_dashs']  # (6, bs, 50, 20x7)
        all_lane_curbs = preds_dicts['all_lane_curbs']  # (6, bs, 50, 20x3)

        line_scores = all_cls_scores.reshape(bs, self.map_head.head.line_query_num_ins, -1) # (bs, num_q, num_classes)
        line_reg_points = all_pts_preds.reshape(bs, self.map_head.head.line_query_num_ins, -1)  # (bs, num_q, 3*num_points)

        scores = line_scores

        if self.map_head.head.loss_cls.use_sigmoid:  # 20241023 版为 True
            scores = scores.sigmoid()
        
        line_reg_points = line_reg_points.view(bs, self.map_head.head.line_query_num_ins, self.map_head.head.line_num_points, 3)

        lane_color_scores = all_lane_colors.reshape(bs, self.map_head.head.line_query_num_ins, self.map_head.head.line_num_points, -1)
        lane_dash_scores = all_lane_dashs.reshape(bs, self.map_head.head.line_query_num_ins, self.map_head.head.line_num_points, -1)
        curb_scores = all_lane_curbs.reshape(bs, self.map_head.head.line_query_num_ins, self.map_head.head.line_num_points, -1)

        lines = (line_reg_points,)
        all_scores = (scores, lane_color_scores, lane_dash_scores, curb_scores)

        results_list = self.process_output_self(bs, lines, all_scores)

        return results_list

    def process_output_self(self, bs, all_pts, all_scores):
        line_reg_points_list, = all_pts  # (num_classes, 50, 20, 3)
        scores, lane_color_scores, lane_dash_scores, curb_scores = all_scores  # (num_classes, 50, 3)
        
        # a. 大类分类
        label_scores, labels = scores.max(-1)
        labels = labels.to(torch.int32)

        line_label_scores = label_scores[:, :50, ...]
        line_labels = labels[:, :50, ...]

        # b. 细分类
        lane_color_scores = lane_color_scores.argmax(-1)
        lane_dash_scores = lane_dash_scores.argmax(-1)
        curb_scores = curb_scores.argmax(-1)

        # [bs, n_q, n_pts, 5]
        attrs_lines = torch.zeros(bs, self.map_head.head.line_query_num_ins, self.map_head.head.line_num_points, 3, dtype=torch.int32, device=lane_color_scores.device)  # 虚实/ 颜色  / 是否栏杆
        attrs_lines[:, :, :, 0] = lane_dash_scores  # 根据属性接口定义，所有属性0表示unknown
        attrs_lines[:, :, :, 1] = lane_color_scores
        attrs_lines[:, :, :, 2] = curb_scores
        
        result = {
            'line_scores': line_label_scores.to(torch.float32),
            'line_labels': line_labels,
            'lines': line_reg_points_list.to(torch.float32),
            'attrs_lines': attrs_lines,
        }

        return result
    
def configure_vision_encoder():
    exp = get_current_exp_class()
    model_config = exp().model_cfg

    model = VisionEncoder(model_config)

    model_path = MapDeployConfig.ckpt_pth
    ckpt_params = torch.load(smart_open(model_path, "rb"), map_location=torch.device("cpu"))["model_state"]
    model.load_state_dict(ckpt_params, strict=True)
    model.eval()
    model.cuda()

    for param in model.parameters():
        param.requires_grad = False

    return model