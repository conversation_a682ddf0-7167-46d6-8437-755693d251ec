# 服务器端MapTRv2卡住问题调试指南

## 🎯 目标
在服务器上定位MapTRv2评估程序卡住的具体位置

## 📋 调试步骤

### 步骤1: 上传调试脚本到服务器

将以下文件上传到服务器的项目目录：
- `minimal_debug_test.py` - 最小化调试脚本
- `test_dataloader_hanging.py` - 数据加载器专项测试
- `debug_maptrv2_hanging.py` - 完整流程调试

### 步骤2: 运行最小化调试脚本

```bash
cd /path/to/Perceptron  # 替换为实际的项目路径
python3 minimal_debug_test.py
```

**预期输出序列:**
```
[HH:MM:SS] 开始最小化调试测试
[HH:MM:SS] 步骤1: 导入基础模块
[HH:MM:SS] PyTorch版本: x.x.x
[HH:MM:SS] CUDA可用: True
[HH:MM:SS] 步骤2: 导入项目模块
[HH:MM:SS] 成功导入Exp类
[HH:MM:SS] 步骤3: 创建实验实例
[HH:MM:SS] 实验实例创建成功
[HH:MM:SS] 步骤4: 获取数据配置
[HH:MM:SS] 数据配置获取成功，包含键: [...]
[HH:MM:SS] 步骤5: 创建数据集实例
[HH:MM:SS] 注意: 这一步可能会花费较长时间，因为需要加载数据...
```

**关键观察点:**
- 如果在步骤5卡住 → 数据集初始化问题
- 如果在步骤6卡住 → 单个样本获取问题  
- 如果在步骤9卡住 → DataLoader迭代问题

### 步骤3: 根据卡住位置进行针对性调试

#### 情况A: 在步骤5卡住（数据集创建）
```bash
# 检查数据路径和权限
ls -la /data/Perceptron/
df -h  # 检查磁盘空间
```

#### 情况B: 在步骤6卡住（样本获取）
运行专项测试：
```bash
python3 test_dataloader_hanging.py
```

#### 情况C: 在步骤9卡住（batch获取）
这是最可能的情况，需要进一步分析

### 步骤4: 使用修改后的原始脚本

如果前面的测试都通过，运行修改后的原始评估脚本：
```bash
python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_fasternetm_nowarp1500w_warp0522data.py --no-clearml -b 1 \
 --ckpt /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_fasternetm_nowarp1500w_nowarp0522data/latest/dump_model/checkpoint_epoch_2.pth --eval_map
```

观察debug输出在哪里停止。

## 🔍 常见卡住位置分析

### 位置1: 数据集初始化
**症状:** 在"创建数据集实例"后卡住
**可能原因:**
- Redis缓存连接问题
- 数据文件访问权限问题
- 网络连接超时

**解决方案:**
```bash
# 检查Redis连接
redis-cli ping

# 检查数据目录权限
ls -la /data/Perceptron/

# 检查网络连接
ping oss.hh-b.brainpp.cn
```

### 位置2: 图像加载
**症状:** 在"开始获取第一个样本"后卡住
**可能原因:**
- Nori数据获取超时
- 图像文件损坏
- 网络I/O阻塞

**解决方案:**
```bash
# 监控网络I/O
iotop -o

# 检查进程状态
ps aux | grep python
```

### 位置3: DataLoader迭代
**症状:** 在"调用 next(data_iter)"后卡住
**可能原因:**
- 多进程死锁
- 内存不足
- CUDA内存问题

**解决方案:**
```bash
# 检查内存使用
free -h
nvidia-smi

# 检查进程树
pstree -p $(pgrep -f maptrv2)
```

## 🛠️ 临时解决方案

### 方案1: 减少资源使用
修改实验配置：
```python
# 在实验文件中添加
self.batch_size_per_device = 1
# 在DataLoader配置中设置
num_workers=0  # 使用单进程
pin_memory=False  # 不使用内存锁定
```

### 方案2: 添加超时机制
```python
import signal

def timeout_handler(signum, frame):
    raise TimeoutError("Operation timed out")

signal.signal(signal.SIGALRM, timeout_handler)
signal.alarm(300)  # 5分钟超时
try:
    # 执行可能卡住的操作
    batch = next(data_iter)
finally:
    signal.alarm(0)
```

### 方案3: 使用更小的数据集
临时修改数据配置，使用更小的数据集进行测试。

## 📊 监控命令

在另一个终端运行以下命令监控系统状态：

```bash
# 监控CPU和内存
top -p $(pgrep -f maptrv2)

# 监控GPU
watch -n 1 nvidia-smi

# 监控网络
netstat -i

# 监控磁盘I/O
iostat -x 1
```

## 📝 收集调试信息

请收集以下信息并反馈：

1. **卡住位置:** 最后一条成功的debug输出
2. **系统状态:** CPU、内存、GPU使用情况
3. **错误信息:** 任何异常或错误消息
4. **环境信息:** Python版本、PyTorch版本、CUDA版本

## 🚀 下一步

根据调试结果，我可以为你提供更精确的解决方案：
- 如果是网络问题 → 配置本地缓存或代理
- 如果是内存问题 → 优化数据加载策略
- 如果是多进程问题 → 改用单进程模式
- 如果是CUDA问题 → 调整GPU内存管理
