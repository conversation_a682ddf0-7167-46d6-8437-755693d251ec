# python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_repvggb1_nowarp2800w_warp0522data_gs.py --no-clearml -b 10 --sync_bn 1 -e 24 --find_unused_parameters \
#     --pretrained_model s3://zqt/exp-bak/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_repvggb1_nowarp_ep40_pretrain_v5_15whd/2025-06-06T01:11:28/dump_model/checkpoint_epoch_39.pth


# NCCL_DEBUG=INFO NCCL_IB_TIMEOUT=7200000 DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 2 --cpu=80 --gpu=8 --memory=800000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
#  --custom-resources=rdma/mlnx_shared=8 --positive-tags=A800 \
#  --mount=gpfs://gpfs1/acceldata:/mnt/acceldata -n mach-generator  --group=generator_gpu -- \
# python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_repvggb1_nowarp2800w_warp0616data.py --no-clearml -b 10 --sync_bn 1 -e 24 --find_unused_parameters \
#     --pretrained_model s3://zqt/exp-bak/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_repvggb1_nowarp_ep40_pretrain_v5_15whd/2025-06-06T01:11:28/dump_model/checkpoint_epoch_39.pth



# NCCL_IB_TIMEOUT=22 NCCL_TIMEOUT=7200000 DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=80 --gpu=8 --memory=800000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
#  --custom-resources=rdma/mlnx_shared=8 --positive-tags=node/mc-gpu-a100-ib-0015.host.machdrive.cn --positive-tags=node/mc-gpu-a100-ib-0029.host.machdrive.cn --positive-tags=node/mc-gpu-a100-ib-0026.host.machdrive.cn \
#  --mount=gpfs://gpfs1/acceldata:/mnt/acceldata -n mach-generator  --group=generator_gpu -- \
# python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_repvggb1_nowarp2800w_warp0620data.py --no-clearml -b 10 --sync_bn 1 -e 24 --find_unused_parameters \
#     --pretrained_model s3://zqt/exp-bak/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_repvggb1_nowarp_ep40_pretrain_v5_15whd/2025-06-06T01:11:28/dump_model/checkpoint_epoch_39.pth


# NCCL_IB_TIMEOUT=22 NCCL_TIMEOUT=7200000 DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 2 --cpu=80 --gpu=8 --memory=800000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
#  --custom-resources=rdma/mlnx_shared=8 --positive-tags=H20 \
#  --mount=gpfs://gpfs1/acceldata:/mnt/acceldata -n mach-generator  --group=generator_gpu -- \
# python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_repvggb1_nowarp2800w_warp0620data.py --no-clearml -b 10 --sync_bn 1 -e 24 --find_unused_parameters \
#     --pretrained_model s3://zqt/exp-bak/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_repvggb1_nowarp_ep40_pretrain_v5_15whd/2025-06-06T01:11:28/dump_model/checkpoint_epoch_39.pth




# NCCL_IB_TIMEOUT=22 NCCL_TIMEOUT=7200000 DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 8 --cpu=80 --gpu=8 --memory=800000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
#  --custom-resources=rdma/mlnx_shared=0 \
#  --positive-tags=node/mc-gpu-a100-ib-0029.host.machdrive.cn \
#  --positive-tags=node/mc-gpu-a100-ib-0015.host.machdrive.cn \
#  --positive-tags=node/mc-gpu-a100-ib-0022.host.machdrive.cn \
#  --positive-tags=node/mc-gpu-a100-ib-0026.host.machdrive.cn \
#  --positive-tags=node/mc-gpu-a100-ib-0003.host.machdrive.cn \
#  --positive-tags=node/mc-gpu-a100-ib-0027.host.machdrive.cn \
#  --positive-tags=node/mc-gpu-a100-ib-0008.host.machdrive.cn \
#  --positive-tags=node/mc-gpu-a100-ib-0007.host.machdrive.cn \
#  --positive-tags=node/mc-gpu-a100-ib-0028.host.machdrive.cn \
#  --mount=gpfs://gpfs1/acceldata:/mnt/acceldata -n mach-generator  --group=generator_gpu -- \
# python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_repvggb1_nowarp2800w_warp0620data.py --no-clearml -b 10 --sync_bn 1 -e 24 --find_unused_parameters \
#     --pretrained_model s3://zqt/exp-bak/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_repvggb1_nowarp_ep40_pretrain_v5_15whd/2025-06-06T01:11:28/dump_model/checkpoint_epoch_39.pth

# DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 4 --cpu=100 --gpu=8 --memory=610000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
#  --custom-resources rdma/mlnx_shared=8 --positive-tags=H20 \
#  --mount=gpfs://gpfs1/acceldata:/mnt/acceldata -n mach-generator  --group=generator_gpu -- \
# python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_ep40_pretrain_v5_21whd_1wgs.py --no-clearml -b 10 --sync_bn 1 -e 24 --find_unused_parameters \
# --pretrained_model s3://zqt/exp-bak/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_ep40_pretrain_v5_15whd/2025-06-02T14:49:52/dump_model/checkpoint_epoch_38.pth


# DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 4 --cpu=100 --gpu=8 --memory=800000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
#  --custom-resources rdma/mlnx_shared=8 --positive-tags=H20 \
#  --mount=gpfs://gpfs1/acceldata:/mnt/acceldata -n mach-generator  --group=generator_gpu -- \
# python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_fasternetm_nowarp2800w_nowarp0623data.py --no-clearml -b 10 --sync_bn 1 -e 24 --find_unused_parameters \
# --pretrained_model 3://zqt/release_map/20250616/e2e_map_lane_20250616.pth



NCCL_IB_TIMEOUT=22 NCCL_TIMEOUT=7200000 DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 5 --cpu=80 --gpu=8 --memory=800000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
 --custom-resources=rdma/mlnx_shared=0 \
--positive-tags=node/mc-gpu-a100-ib-0002.host.machdrive.cn \
 --positive-tags=node/mc-gpu-a100-ib-0029.host.machdrive.cn \
 --positive-tags=node/mc-gpu-a100-ib-0022.host.machdrive.cn \
 --positive-tags=node/mc-gpu-a100-ib-0026.host.machdrive.cn \
 --positive-tags=node/mc-gpu-a100-ib-0027.host.machdrive.cn \
 --positive-tags=node/mc-gpu-a100-ib-0007.host.machdrive.cn \
 --mount=gpfs://gpfs1/acceldata:/mnt/acceldata -n mach-generator  --group=generator_gpu -- \
python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_fasternetm_nowarp2800w_nowarp0623data.py --no-clearml -b 10 --sync_bn 1 -e 24 --find_unused_parameters \
<<<<<<< Updated upstream
--pretrained_model s3://zqt/release_map/20250616/e2e_map_lane_20250616.pth

=======
--pretrained_model s3://zqt/release_map/20250616/e2e_map_lane_20250616.pth
>>>>>>> Stashed changes
