'''
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 2 --cpu=100 --gpu=8 --memory=600000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
 --custom-resources rdma/mlnx_shared=8 --negative-tags node/mc-gpu-h20-0058.host.machdrive.cn \
 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- python3 test_dataloader.py
'''
import torch
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import InfiniteIntervalSampler

from pdb import set_trace as ste
import bisect
import os
from perceptron.data.det3d.modules import (
    MultiFrameImageAffineTransformationWarp,
)
import numpy as np
import cv2

def get_unnorm_img(img_0):
    mean = [123.675, 116.28, 103.53]
    std = [58.395, 57.12, 57.375]
    img_0[:, :, 0] = np.uint8((img_0[:, :, 0] * std[0]) + mean[0])
    img_0[:, :, 1] = np.uint8((img_0[:, :, 1] * std[1]) + mean[1])
    img_0[:, :, 2] = np.uint8((img_0[:, :, 2] * std[2]) + mean[2])
    img_0 = np.uint8(np.ascontiguousarray(img_0))
    return img_0


def get_roi_json(loader_out, roi_idx):
    # roi_idx = 572748
    json_collections = loader_out["json_collection"]
    json_idx = bisect.bisect_right(loader_out["frame_data_list"].cumulative_sizes, roi_idx)
    json_path = loader_out["json_collection"][json_idx]
    print(json_path)
    return json_path

def local_dataset_mp_vis(dataset, ind, dataset_vis_folder):
    print("checking ", ind)
    data_list = dataset.__getitem__(ind)
    wrap_data = dataset.collate_fn([data_list])  # 长度固定为1
    print("{} wrapped".format(ind))
    # wrap_data = numpy2torch(wrap_data)
    
    ste()

    print("{} over".format(ind))
    return ind



if __name__ == "__main__":
    import mmcv
    import random
    from perceptron.utils import torch_dist as dist
    from tqdm import tqdm
    # from perceptron.exps.end2end.private.maptracker.data_cfg.maptracker_annos_hf_4v0r_sq_sa_pl_qs_ses import (
    #     base_dataset_cfg as DATA_TRAIN_CFG,
    # )

    from perceptron.exps.end2end.private.maptracker.data_cfg.maptracker_annos_z10_4v0r_sq_sa_pl_qs_ses import (
        base_dataset_cfg as DATA_TRAIN_CFG,
    )

    batch_size_per_device = 6

    map_ego_range = [0, -30, -6, 100, 30, 6]        # ego [xmin, ymin, zmin, xmax, ymax, zmax]
    map_lidar_range = [-30., 0., -6., 30., 100, 6]

    DATA_TRAIN_CFG['annotation']['maptracker']['map_point_cloud_range'] = map_ego_range
    DATA_TRAIN_CFG['annotation']['maptracker']['map_lidar_range'] = map_lidar_range

    data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)

    # data-train

    data_train_cfg["loader"]["datasets_names"] = ["new-v20250405-debug"]  # ["zqt_prelabel_4w1_filtered_2k"]
    data_train_cfg["image"][
        "cam120_scale"
    ] = 1.3  # codebase去畸变..
    data_train_cfg["gpfs_prefix"] = "/mnt/caiqianxi/"

    data_train_cfg["annotation"]["box"]["label_key"] = "pre_labels"
    data_train_cfg["annotation"]["box"]["occlusion_threshold"] = -1
    data_train_cfg["annotation"]["maptracker"]["warp_tgt_json"] = "s3://tf-map-data-qy/data_rebuild/data_rebuild_mm_wfl_newcp_z10_fixed_calib/jsons/car_z03/ppl_bag_20241211_142550_det_22527_22756.json"
    data_train_cfg["loader"]["only_key_frame"] = False
    
    # change lidar / radar cfg, we do not use them
    data_train_cfg["radar"]["with_virtual_radar"] = False  # 设成 True 的时候，需要 "gt_boxes", map 里没有
    data_train_cfg["lidar"]["lidar_names"] = []  # 不用 lidar 点云 
    data_train_cfg['sensor_names']["lidar_names"] = []   # 不能直接 pop, 因为有关 lidar 的标定是在 get_lidar 时读进来的
    data_train_cfg['sensor_names'].pop("radar_names")

    # change warp matrix cfg
    data_train_cfg["pipeline"]["ida_aug"]["type"] = MultiFrameImageAffineTransformationWarp
    data_train_cfg["pipeline"]["ida_aug"][
        "target_extrinsic"
    ] = "/data/workspace/release_save/extrinsic/target_extrinsic.npz"
    data_train_cfg["pipeline"]["ida_aug"][
        "map_lidar_range"
    ] = data_train_cfg["annotation"]["maptracker"]["map_lidar_range"]

    data_train_cfg["image"][
        "img_warp_maps_dict"
    ] = None # "1115_release/img_warp_maps_dict_all_15.npz"
    data_train_cfg["loader"]["interval"] = 1

    train_dataset = PrivateE2EDataset(
        **data_train_cfg,
    )

    # loader_out = train_dataset.loader_output
    # roi_idx_list = [311147]
    # for roi_idx in roi_idx_list:
    #     roi_json_path = get_roi_json(loader_out, roi_idx)
    #     print(f"{roi_idx} path: {roi_json_path}")
    # ste()
    roi_item = train_dataset.__getitem__(55)
    
    map_gt = roi_item["map_gt"][0][0]
    curb_gt = map_gt[1]
    

    bev_img = np.zeros((1000, 600, 3))

    for curb_ins in curb_gt:
        curb_pts = curb_ins[0]
        for point in curb_pts:
            x_int, y_int = round(point[0] * 600), round(point[1] * 1000)
            cv2.circle(bev_img, (x_int, y_int), 4, (0, 0, 255), -1)
    
    cv2.imwrite("bev.jpg", bev_img)
    ste()



    curb_gt[0]

    roi_item[""]

    ste()



    img_torch = torch.from_numpy(roi_item["imgs"]).clone().squeeze()[0].permute(1, 2, 0).numpy()  # (4, 3, 1080, 1920)
    # img_np = get_unnorm_img(img_torch)

    # ste()

    # train_dataloader = torch.utils.data.DataLoader(
    #     train_dataset,
    #     batch_size=batch_size_per_device,
    #     drop_last=False,
    #     shuffle=False,
    #     collate_fn=PrivateE2EDataset.collate_fn,
    #     # sampler=DistributedSampler(train_dataset) if dist.is_distributed() else None,
    #     sampler=InfiniteIntervalSampler(len(train_dataset), shuffle=False, interval=1) if dist.is_distributed() else None, 
    #     pin_memory=True,
    #     num_workers=0,     # set to 0 when debug
    # )
    # print("trainloader ready")

    # # # single epoch
    # # train_iter = iter(train_dataloader)
    # # for step in tqdm(range(len(train_dataloader))):
    # #     try:
    # #         data = next(train_iter)
    # #         import pdb; pdb.set_trace()
    # #     except StopIteration:
    # #         print("stopped!!!, retry")
    # #         train_iter = iter(train_dataloader)
    # #         data = next(train_iter)


    # # dataset check
    # dataset_vis_folder = "/data/dataset_vis/dataset_release_vis"
    # os.makedirs(dataset_vis_folder, exist_ok=True)

    # ind_list = [i for i in range(len(train_dataset))]
    # roi_list = ind_list

    # mp_mode = False  # 单进程
    # # mp_mode = True  # 多进程

    # if mp_mode:
    #     # dataset mp check
    #     import concurrent
    #     futures = set()
    #     with concurrent.futures.ProcessPoolExecutor(max_workers=50) as executor:
    #         for ind in tqdm(roi_list):
    #             futures.add(executor.submit(local_dataset_mp_vis, train_dataset, ind, dataset_vis_folder))

    #     for future in concurrent.futures.as_completed(futures):
    #         try:
    #             result = future.result()
    #             print(result)
    #         except Exception as e:
    #             print(f"Exception: {e}")
    # else:
    #     for ind in tqdm(roi_list):
    #         local_dataset_mp_vis(train_dataset, ind, dataset_vis_folder)




    # # single epoch
    # train_iter = iter(train_dataloader)
    # for step in tqdm(range(len(train_dataloader))):
    #     try:
    #         data = next(train_iter)
    #     except StopIteration:
    #         print("stopped!!!, retry")
    #         train_iter = iter(train_dataloader)
    #         data = next(train_iter)

    # epoch_num = 100
    # train_iter = iter(train_dataloader)
    # for epoch_id in tqdm(range(epoch_num)):
    #     for step in range(len(train_dataloader)):
    #         try:
    #             data = next(train_iter)
    #         except StopIteration:
    #             print("stopped!!!, retry")
    #             train_iter = iter(train_dataloader)
    #             data = next(train_iter)
