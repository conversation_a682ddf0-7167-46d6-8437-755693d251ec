import os
import os.path as osp
import cv2
from tqdm import tqdm
import numpy as np
from pdb import set_trace as ste
from refile import smart_open, smart_exists, smart_path_join, smart_listdir
import argparse
import json
import copy


if __name__ == "__main__":
    tmp_list = [
        "s3://caiqianxi/json_nori/list/180590_e2e_list.json",
        "s3://caiqianxi/json_nori/list/180596_e2e_list.json",
        "s3://caiqianxi/json_nori/list/180586_e2e_list_v2.json",
        "s3://caiqianxi/json_nori/list/180587_e2e_list_v2.json",
        "s3://caiqianxi/json_nori/list/180588_e2e_list_v2.json"
    ]

    template_data = json.load(smart_open(tmp_list[0], "r"))
    combine_data = copy.deepcopy(template_data)
    
    valid_list = []
    for tmp_path in tmp_list:
        tmp_data = json.load(smart_open(tmp_path, "r"))
        valid_list.extend(tmp_data["paths"])
    print(f"totally {len(valid_list)} paths")
    
    combine_data["paths"] = valid_list
    json.dump(combine_data, smart_open("s3://zqt/car/daily_data/20250303_{}_manual_clips.json".format(len(valid_list)), "w"))

    ste()


