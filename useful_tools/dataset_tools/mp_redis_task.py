'''
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 2 --cpu=100 --gpu=8 --memory=600000 --preemptible=no --max-wait-duration=12h0m0s --set-env DISTRIBUTED_JOB=true \
 --custom-resources rdma/mlnx_shared=8 --negative-tags node/mc-gpu-h20-0058.host.machdrive.cn \
 --group=static_gpu --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- python3 test_dataloader.py
'''
import torch
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import InfiniteIntervalSampler

from pdb import set_trace as ste
import bisect
import os
from perceptron.data.det3d.modules import (
    MultiFrameImageAffineTransformationWarp,
)
import json
from refile import smart_path_join, smart_listdir, smart_exists, smart_open


def get_roi_json(loader_out, roi_idx):
    # roi_idx = 572748
    json_collections = loader_out["json_collection"]
    json_idx = bisect.bisect_right(loader_out["frame_data_list"].cumulative_sizes, roi_idx)
    json_path = loader_out["json_collection"][json_idx]
    print(json_path)
    return json_path

def local_dataset_mp_vis(dataset, ind, dataset_vis_folder):
    print("checking ", ind)
    data_list = dataset.__getitem__(ind)
    wrap_data = dataset.collate_fn([data_list])  # 长度固定为1
    print("{} wrapped".format(ind))
    # wrap_data = numpy2torch(wrap_data)
    
    print("{} over".format(ind))
    return ind


def mp_init(data_train_cfg, start_idx, end_idx, path_list):
    '''
    多进程初始化
    '''
    # 需要强行做切片
    roi_list = path_list[start_idx: end_idx]
    tmp_name = "tmp_{}_{}".format(start_idx, end_idx)

    data_train_cfg["loader"].car.trainset_partial[tmp_name] = roi_list
    data_train_cfg["loader"]["datasets_names"] = [tmp_name]
    
    data_train_cfg["annotation"]["box"]["label_key"] = "pre_labels"
    data_train_cfg["annotation"]["box"]["occlusion_threshold"] = -1
    data_train_cfg["annotation"]["maptracker"]["warp_tgt_json"] = "s3://tf-map-data-qy/data_rebuild/data_rebuild_mm_wfl_newcp_z10_fixed_calib/jsons/car_z03/ppl_bag_20241211_142550_det_22527_22756.json"
    data_train_cfg["loader"]["only_key_frame"] = False
    
    # change lidar / radar cfg, we do not use them
    data_train_cfg["radar"]["with_virtual_radar"] = False  # 设成 True 的时候，需要 "gt_boxes", map 里没有
    data_train_cfg["lidar"]["lidar_names"] = []  # 不用 lidar 点云 
    data_train_cfg['sensor_names']["lidar_names"] = []   # 不能直接 pop, 因为有关 lidar 的标定是在 get_lidar 时读进来的
    data_train_cfg['sensor_names'].pop("radar_names")

    # change warp matrix cfg
    data_train_cfg["pipeline"]["ida_aug"]["type"] = MultiFrameImageAffineTransformationWarp
    data_train_cfg["pipeline"]["ida_aug"][
        "target_extrinsic"
    ] = "/data/workspace/release_save/extrinsic/target_extrinsic.npz"
    data_train_cfg["pipeline"]["ida_aug"][
        "map_lidar_range"
    ] = data_train_cfg["annotation"]["maptracker"]["map_lidar_range"]

    data_train_cfg["image"][
        "img_warp_maps_dict"
    ] = None # "1115_release/img_warp_maps_dict_all_15.npz"
    data_train_cfg["loader"]["interval"] = 1

    train_dataset = PrivateE2EDataset(
        **data_train_cfg,
    )



if __name__ == "__main__":
    import concurrent.futures
    import mmcv
    import random
    from perceptron.utils import torch_dist as dist
    from tqdm import tqdm
    # from perceptron.exps.end2end.private.maptracker.data_cfg.maptracker_annos_hf_4v0r_sq_sa_pl_qs_ses import (
    #     base_dataset_cfg as DATA_TRAIN_CFG,
    # )

    from perceptron.exps.end2end.private.maptracker.data_cfg.maptracker_annos_z10_4v0r_sq_sa_pl_qs_ses import (
        base_dataset_cfg as DATA_TRAIN_CFG,
    )

    import argparse
    parser = argparse.ArgumentParser(description="parse")
    parser.add_argument("--path")  # "s3://zqt/car/20250310_lanechange_train_chunks_6839clips.json"
    parser.add_argument("--chunk_size", type=int)
    parser.add_argument("--num_worker", type=int, default=50)
    args = parser.parse_args()
    full_path = args.path
    chunk_size = args.chunk_size  # 10
    num_worker = args.num_worker
    # full_path = "s3://zqt/dataset/cqx_20250401_clip_finish_301_swan.json"

    batch_size_per_device = 6

    map_ego_range = [0, -30, -6, 100, 30, 6]        # ego [xmin, ymin, zmin, xmax, ymax, zmax]
    map_lidar_range = [-30., 0., -6., 30., 100, 6]

    DATA_TRAIN_CFG['annotation']['maptracker']['map_point_cloud_range'] = map_ego_range
    DATA_TRAIN_CFG['annotation']['maptracker']['map_lidar_range'] = map_lidar_range

    data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
    path_list = json.load(smart_open(full_path, "r"))["paths"]

    data_num = len(path_list)
    start_idx_list = range(0, data_num, chunk_size)

    # for start_idx in start_idx_list:
    #     end_idx = start_idx + chunk_size
    #     mp_init(data_train_cfg, start_idx, end_idx, path_list)
    

    # 多进程
    results = set()
    with concurrent.futures.ProcessPoolExecutor(max_workers=num_worker) as executor:
        for start_idx in start_idx_list:
            end_idx = start_idx + chunk_size
            results.add(executor.submit(mp_init, data_train_cfg, start_idx, end_idx, path_list))

    for future in tqdm(results):
        future.result()

