'''
'''
import json
import numpy as np
from tqdm import tqdm
import copy
from pdb import set_trace as ste
from refile import smart_path_join, smart_listdir, smart_exists, smart_open
import concurrent.futures


def deal_one_json(json_path):
    json_data = json.load(smart_open(json_path, "r"))
    frames = json_data["frames"]

    xmin, xmax = 1e6, -1e6
    ymin, ymax = 1e6, -1e6
    valid_ct = len(frames)
    return valid_ct

if __name__ == "__main__":
    '''
    '''
    prelabel_json_path_list = json.load(smart_open("s3://zqt/dataset_hd/e2e_20250303_clip_finish_129.json", "r"))
    prelabel_json_path_list = prelabel_json_path_list["paths"]

    total_frames_ct = 0
    # for json_path in tqdm(prelabel_json_path_list):
    #     tmp_ct = deal_one_json(json_path)
    #     total_frames_ct += tmp_ct
    
    results = set()
    with concurrent.futures.ProcessPoolExecutor(max_workers=90) as executor:
        for json_path in tqdm(prelabel_json_path_list):
            results.add(executor.submit(deal_one_json, json_path))

    for future in tqdm(results):
        total_frames_ct += (future.result())
    
    print("{} manual jsons, totally {} frames".format(len(prelabel_json_path_list), total_frames_ct))
    
