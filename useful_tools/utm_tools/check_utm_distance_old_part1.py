'''
批量获取所有json到特定utm坐标的距离
'''
import os
import os.path as osp
import cv2
from tqdm import tqdm
import numpy as np
import nori2 as nori
import torch
from skimage import io as skimage_io
import io
import quaternion
from pdb import set_trace as ste
import json
from refile import smart_open, smart_path_join
import concurrent.futures
import time
from pyproj import Proj, transform

def lla_to_utm(lat, lon, alt):
    # 确定 UTM 带号
    utm_zone = int((lon + 180) / 6) + 1
    # 确定南北半球
    utm_letter = 'N' if lat >= 0 else 'S'
    # 创建 UTM 投影
    utm_proj = Proj(proj='utm', zone=utm_zone, ellps='WGS84', south=lat < 0)
    # 创建 WGS84 投影
    wgs84_proj = Proj(proj='latlong', datum='WGS84')
    # 转换坐标
    easting, northing = transform(wgs84_proj, utm_proj, lon, lat)
    return easting, northing, utm_zone, utm_letter

def get_utm_distance(json_path):
    json_data = json.load(smart_open(json_path, "r"))
    start_lla = json_data["frames"][0]["start_lla"]
    # start_lla = (34.052235, -118.243683, 0)  # 纬度, 经度, 高程
    easting, northing, utm_zone, utm_letter = lla_to_utm(*start_lla)
    # print(f"UTM 坐标: ({easting}, {northing}), 区域: {utm_zone}{utm_letter}")
    tmp_x, tmp_y = easting, northing
    tgt_x = 328060.98
    tgt_y = 3357941.00
    distance = np.sqrt((tmp_x - tgt_x) ** 2 + (tmp_y - tgt_y) ** 2)
    print(f"tmp distance: {distance}")

    return {json_path: distance}

if __name__ == "__main__":
    full_json_list_path = "s3://tf-map-data-qy/z10_fixed_finish_with_lidar_img/data_rebuild_mm_wfl_newcp_z10_fixed_calib/list/z10_bev_gt_fixed_bef0217_0306_train.json"
    full_json_list = json.load(smart_open(full_json_list_path, "r"))
    full_list = full_json_list["paths"]
    full_list = full_list[:3000]


    # for json_path in tqdm(full_list):
    #     res_dict = get_utm_distance(json_path)
    
    # 多进程
    results = set()
    with concurrent.futures.ProcessPoolExecutor(max_workers=80) as executor:
        for json_path in tqdm(full_list):
            results.add(executor.submit(get_utm_distance, json_path))
    global_res = dict()
    for future in tqdm(results):
        res_dict = future.result()
        global_res.update(res_dict)
    
    sorted_global_res = dict(sorted(global_res.items(), key=lambda item: item[1]))
    sorted_key_list = list(sorted_global_res.keys())

    json.dump(sorted_global_res, smart_open("s3://zqt/debug_utm/v20250319_old_part1.json", "w"))

    
    # sorted_global_res["s3://caiqianxi/json_nori/road_entrance_e2emulti_fl_newcp_det_finished/E2E_laneline_recycle/180763/car_z13/ppl_bag_20250214_122553_det/v0_250218_094137/v0_250227_122018_ppl_bag_20250214_122553_det_234_717_data_post.json"]
    # sorted_global_res["s3://caiqianxi/json_nori/road_entrance_e2emulti_fl_newcp_det_finished/E2E_laneline_recycle/180597/car_z13/ppl_bag_20250215_111115_det/v0_250219_070505/v0_250227_101445_ppl_bag_20250215_111115_det_12559_13005_data_post.json"]
    
    ste()

