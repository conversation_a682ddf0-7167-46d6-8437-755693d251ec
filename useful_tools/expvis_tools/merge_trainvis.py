'''
合并maptracker的实验可视化
'''

import os
import os.path as osp
import cv2
from tqdm import tqdm
import numpy as np
from pdb import set_trace as ste
from refile import smart_open, smart_exists, smart_path_join, smart_listdir
import argparse

def merge_video(pic_dir, mp4name, save_folder="s3://zqt/mtracker/trainvis-videos/"):
    pic_list = [osp.join(pic_dir, tmp) for tmp in os.listdir(pic_dir)]
    pic_list.sort()

    # h, w, _ = cat_list[0].shape
    h, w = 1024, 3840
    # video infos
    out_size = [h, w]
    fps = 10
    fourcc = cv2.VideoWriter_fourcc("m", "p", "4", "v")
    out_video = cv2.VideoWriter()
    out_video_path = mp4name + ".mp4"
    out_video.open(out_video_path, fourcc, fps, (int(out_size[1]), int(out_size[0])), True)

    for img_path in tqdm(pic_list[::5]):
        img = cv2.imread(img_path)
        cv2.putText(img, "{}".format(img_path.split("/")[-1].split(".")[0]), (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 255), 3)
        # cv2.imwrite("tmp.jpg", img)
        out_frame = img
        # write to video
        out_video.write(np.uint8(out_frame))
    out_video.release()

    trans_cmd = r"ffmpeg -i {} -pix_fmt yuv420p -vcodec libx264 -r 10 {} -hide_banner -y".format(
        out_video_path, out_video_path.replace(".mp4", "_x264.mp4")
    )
    os.system(trans_cmd)
    local_path = (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4")
    s3_path = smart_path_join(save_folder, (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4"))

    cmd = "aws --endpoint-url=http://oss.i.machdrive.cn s3 cp {} {}".format(local_path, s3_path)
    os.system(cmd)

    
if __name__ == "__main__":
    '''
    python merge_trainvis.py --pic_dir /data/Perceptron/outputs/maptracker__maptracker_exp_z10_4v0r_single_cityhighway_plqs_g1_v7_br_vr_sesplit_bevformer/latest/train_vis

    python merge_trainvis.py --pic_dir /data/Perceptron/outputs/maptracker__maptracker_exp_z10_4v0r_single_cityhighway_plqs_g1_v7_br_vr_sesplit_bevformer_fromscratch_overfit/latest/train_vis

    python merge_trainvis.py --pic_dir /data/Perceptron/outputs/maptracker__maptracker_exp_z10_4v0r_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_bmkoverfit/latest/train_vis

    python merge_trainvis.py --pic_dir /data/Perceptron/outputs/maptracker__maptracker_exp_z10_4v0r_single_cityhighway_plqs_g1_v7_br_vr_sesplit_prelabel_2kjson/latest/train_vis

    python merge_trainvis.py --pic_dir /data/Perceptron/outputs/maptracker__maptracker_exp_z10_0v0r1l_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data_lidartiny/latest/train_vis

    python merge_trainvis.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_bda_retrain/2025-02-23T01:59:40/train_vis

    python merge_trainvis.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_bda_retrain/2025-02-23T01:59:56/train_vis

    python merge_trainvis.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_bda_retrain/2025-02-23T02:00:19/train_vis

    python merge_trainvis.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_bda_retrain/2025-02-23T02:05:31/train_vis
    
    python merge_trainvis.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_bda_retrain/2025-02-23T02:05:31/train_vis

    python merge_trainvis.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v3data/2025-02-27T03:06:26/train_vis

    python merge_trainvis.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r/2025-02-27T03:02:58/train_vis

    python merge_trainvis.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v3data_full/latest/train_vis

    '''
    parser = argparse.ArgumentParser()
    parser.add_argument("--pic_dir", type=str, default=None)
    args = parser.parse_args()
    pic_dir = args.pic_dir
    mp4name = pic_dir.split("/")[-3] + "-" + pic_dir.split("/")[-2] + "-" + pic_dir.split("/")[-1]
    merge_video(pic_dir, mp4name)