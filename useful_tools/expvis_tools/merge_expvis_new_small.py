'''
合并maptracker的实验可视化
'''

import os
import os.path as osp
import cv2
from tqdm import tqdm
import numpy as np
from pdb import set_trace as ste
from refile import smart_open, smart_exists, smart_path_join, smart_listdir
import argparse

def merge_video(pic_dir, mp4name, save_folder="s3://zqt/mtracker/expvis-videos/"):
    pic_list = [osp.join(pic_dir, tmp) for tmp in os.listdir(pic_dir)]

    # 重构pic_group
    pic_group_dict = {}
    for pic_path in tqdm(pic_list):
        tmp_group_id = int(pic_path.split("/")[-1].split("_token")[0])
        cam_id = int(pic_path.split("/")[-1].split("_")[-3])
        if tmp_group_id not in pic_group_dict:
            pic_group_dict[tmp_group_id] = {}
        mode = pic_path.split("_")[-1].split(".")[0]  # dt/gt
        view = pic_path.split("_")[-2]  # bev/rv
        if cam_id not in pic_group_dict[tmp_group_id]:
            pic_group_dict[tmp_group_id][cam_id] = {}  
        pic_group_dict[tmp_group_id][cam_id][mode+view] = pic_path
    roi_group_num = len(pic_group_dict)

    key_list = list(pic_group_dict.keys())
    max_key = np.array(key_list).max()

    h, w = 1080, 3394
    # video infos
    out_size = [h, w]
    fps = 10
    fourcc = cv2.VideoWriter_fourcc("m", "p", "4", "v")
    out_video = cv2.VideoWriter()
    out_video_path = mp4name + ".mp4"
    out_video.open(out_video_path, fourcc, fps, (int(out_size[1]), int(out_size[0])), True)

    for group_id in tqdm(range(0, max_key, 1)):
        if group_id not in pic_group_dict:
            continue
        group_val = pic_group_dict[group_id]

        # 检查完整性
        valid = True
        for cam_id in range(4):
            if cam_id not in group_val:
                valid = False
                continue
            if "gtrv" not in group_val[cam_id] or "dtrv" not in group_val[cam_id]:
                valid = False
        if "gtbev" not in group_val[0] or "dtbev" not in group_val[0]:
            valid = False
            continue
                
        if not valid:
            continue

        dt_rv_img_list = []
        gt_rv_img_list = []
        for cam_id in range(2,3):
            tmp_dt_img = cv2.imread(group_val[cam_id]["dtrv"])
            tmp_gt_img = cv2.imread(group_val[cam_id]["gtrv"])
            dt_rv_img_list.append(tmp_dt_img)
            gt_rv_img_list.append(tmp_gt_img)
        
        for cam_id in range(0, 1):
            tmp_dt_img = cv2.imread(group_val[cam_id]["dtrv"])
            tmp_gt_img = cv2.imread(group_val[cam_id]["gtrv"])
            dt_rv_img_list.append(tmp_dt_img)
            gt_rv_img_list.append(tmp_gt_img)
        
        for cam_id in range(3, 4):
            tmp_dt_img = cv2.imread(group_val[cam_id]["dtrv"])
            tmp_gt_img = cv2.imread(group_val[cam_id]["gtrv"])
            dt_rv_img_list.append(tmp_dt_img)
            gt_rv_img_list.append(tmp_gt_img)

        dt_bev_img = cv2.imread(group_val[0]["dtbev"])  # (400, 210)
        gt_bev_img = cv2.imread(group_val[0]["gtbev"])

        dt_rv_cat = np.concatenate(dt_rv_img_list, axis=1)
        gt_rv_cat = np.concatenate(gt_rv_img_list, axis=1)

        dt_cat = np.concatenate((dt_rv_cat, cv2.resize(dt_bev_img, (1029, 1080))), axis=1)
        gt_cat = np.concatenate((gt_rv_cat, cv2.resize(gt_bev_img, (1029, 1080))), axis=1)

        all_cat = np.concatenate((dt_cat, gt_cat), axis=0)

        out_frame = all_cat
        out_frame = cv2.resize(out_frame, (3394, 1080))
        # write to video
        out_video.write(np.uint8(out_frame))
    out_video.release()

    trans_cmd = r"ffmpeg -i {} -pix_fmt yuv420p -vcodec libx264 -r 10 {} -hide_banner -y".format(
        out_video_path, out_video_path.replace(".mp4", "_x264.mp4")
    )
    os.system(trans_cmd)
    local_path = (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4")
    s3_path = smart_path_join(save_folder, (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4"))

    cmd = "aws --endpoint-url=http://oss.i.machdrive.cn s3 cp {} {}".format(local_path, s3_path)
    os.system(cmd)



    
if __name__ == "__main__":
    '''

    rlaunch --cpu=70  --memory=100000 --group=static_gpu --positive-tags=L20 --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- 



    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-valset-v20250321-forward-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-valset-v20250321-lanechange-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-valset-v20250321-left-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-valset-v20250321-right-thr0.3

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_0v0r1l_newdata/2025-04-18T02:28:55/dump_model/checkpoint_epoch_22_new-qy-lidar-v20250417

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_0v0r1l_newdata/2025-04-19T13:39:01/dump_model/checkpoint_epoch_4_new-qy-lidar-v20250418-val
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_0v0r1l_newdata/2025-04-19T13:39:01/dump_model/checkpoint_epoch_18_new-qy-lidar-v20250418-val

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5_hd5w_r50_warp_pretrain/2025-04-26T00:22:26/dump_model/checkpoint_epoch_10_jl-val

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5_hd5w_r50_warp_pretrain/2025-04-26T00:22:26/dump_model/checkpoint_epoch_18_map_z02_bmk

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5_hd5w_fasternetm_warp_pretrain/2025-04-26T00:22:26/dump_model/checkpoint_epoch_36_map_z02_bmk
    
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/2025-04-19T12:55:08/s3:/zqt/exp-bak/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_ep40_pretrain_v5_5whd/2025-04-19T12:55:08/dump_model/checkpoint_epoch_10_map_z02_bmk
    

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5_hd5w_r50_warp_pretrain/2025-04-28T02:29:06/dump_model/checkpoint_epoch_7_map_z02_bmk
    
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5_hd5w_r50_warp_pretrain/2025-04-28T02:29:06/dump_model/checkpoint_epoch_29_map_z02_bmk
    
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5_hd5w_r50_warp_pretrain/2025-04-28T02:29:06/dump_model/checkpoint_epoch_29_map_z02_bmk
    

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip/2025-04-28T14:32:11/checkpoint_epoch_13-valset-v20250321-left-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip/2025-04-28T14:32:11/checkpoint_epoch_13-valset-v20250321-right-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip/2025-04-28T14:32:11/checkpoint_epoch_13-valset-v20250321-lanechange-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip/2025-04-28T14:32:11/checkpoint_epoch_13-valset-v20250321-forward-thr0.3
    
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip/2025-04-28T14:32:11/checkpoint_epoch_22-valset-v20250321-left-thr0.3
    

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip_0429/2025-04-29T10:14:10/checkpoint_epoch_23-valset-v20250321-left-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip_0429/2025-04-29T10:14:10/checkpoint_epoch_23-valset-v20250321-right-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip_0429/2025-04-29T10:14:10/checkpoint_epoch_23-valset-v20250321-lanechange-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip_0429/2025-04-29T10:14:10/checkpoint_epoch_23-valset-v20250321-forward-thr0.3
    
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip_0429_0401pretrain/2025-04-29T15:25:57/checkpoint_epoch_17-valset-v20250321-left-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip_0429_0401pretrain/2025-04-29T15:25:57/checkpoint_epoch_17-valset-v20250321-right-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip_0429_0401pretrain/2025-04-29T15:25:57/checkpoint_epoch_17-valset-v20250321-lanechange-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip_0429_0401pretrain/2025-04-29T15:25:57/checkpoint_epoch_17-valset-v20250321-forward-thr0.3


    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_3w1clip/2025-04-29T10:15:15/checkpoint_epoch_20-valset-v20250321-left-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_3w1clip/2025-04-29T10:15:15/checkpoint_epoch_20-valset-v20250321-right-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_3w1clip/2025-04-29T10:15:15/checkpoint_epoch_20-valset-v20250321-lanechange-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_3w1clip/2025-04-29T10:15:15/checkpoint_epoch_20-valset-v20250321-forward-thr0.3

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/20250401/e2e_map_lane_20250401_warp_r50-valset-v20250321-left-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/20250401/e2e_map_lane_20250401_warp_r50-valset-v20250321-right-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/20250401/e2e_map_lane_20250401_warp_r50-valset-v20250321-lanechange-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/20250401/e2e_map_lane_20250401_warp_r50-valset-v20250321-forward-thr0.3


    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip_0429/2025-04-29T10:14:10/checkpoint_epoch_23-p177-hd-test-thr0.3

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0401setting_1500wpretrain/latest/checkpoint_epoch_2-valset-v20250321-left-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0401setting_1500wpretrain/latest/checkpoint_epoch_2-valset-v20250321-right-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0401setting_1500wpretrain/latest/checkpoint_epoch_2-valset-v20250321-lanechange-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0401setting_1500wpretrain/latest/checkpoint_epoch_2-valset-v20250321-forward-thr0.3

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0401setting_1500wpretrain/2025-04-30T13:15:52/checkpoint_epoch_13-valset-v20250321-left-thr0.3

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5_hd5w_r50_nowarp_pretrain/latest/dump_model/checkpoint_epoch_19_map_z02_bmk

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5_hd5w_r50_nowarp_pretrain/2025-05-02T16:26:27/dump_model/checkpoint_epoch_14_map_z02_bmk

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_repvggd2se_nowarp1500w_warp0425data/2025-05-04T14:56:00/checkpoint_epoch_1-valset-v20250321-left-thr0.3

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_repvggplus_nowarp1500w_warp0425data/latest/checkpoint_epoch_8-valset-v20250321-left-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_repvggplus_nowarp1500w_warp0425data/latest/checkpoint_epoch_8-valset-v20250321-right-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_repvggplus_nowarp1500w_warp0425data/latest/checkpoint_epoch_8-valset-v20250321-lanechange-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_repvggplus_nowarp1500w_warp0425data/latest/checkpoint_epoch_8-valset-v20250321-forward-thr0.3


    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/20250425/e2e_map_lane_20250425_ep10-fork-qy-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/20250425/e2e_map_lane_20250425_ep10-merge-aliyun-thr0.3

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_repvggplus_nowarp1500w_warp0425data/2025-05-04T20:38:11/checkpoint_epoch_14-valset-v20250321-left-thr0.3

    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_fasternetm_nowarp1500w_warp0425data/2025-05-02T17:20:18/checkpoint_epoch_23-valset-v20250321-left-thr0.3


    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0401setting_1500wpretrain/2025-04-30T13:15:52/checkpoint_epoch_23-valset-v20250321-left-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0401setting_1500wpretrain/2025-04-30T13:15:52/checkpoint_epoch_23-valset-v20250321-right-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0401setting_1500wpretrain/2025-04-30T13:15:52/checkpoint_epoch_23-valset-v20250321-lanechange-thr0.3
    python merge_expvis_new_small.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0401setting_1500wpretrain/2025-04-30T13:15:52/checkpoint_epoch_23-valset-v20250321-forward-thr0.3


    '''
    parser = argparse.ArgumentParser()
    parser.add_argument("--pic_dir", type=str, default=None)
    parser.add_argument("--save_s3_path", type=str, default="s3://zqt/mtracker/expvis-videos/")
    args = parser.parse_args()
    pic_dir = args.pic_dir
    mp4name = pic_dir.split("/")[-3] + "-" + pic_dir.split("/")[-2] + "-" + pic_dir.split("/")[-1]
    mp4name = mp4name.replace(":", "-")
    merge_video(pic_dir, mp4name, args.save_s3_path)
