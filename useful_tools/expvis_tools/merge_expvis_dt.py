'''
合并maptracker的实验可视化
'''

import os
import os.path as osp
import cv2
from tqdm import tqdm
import numpy as np
from pdb import set_trace as ste
from refile import smart_open, smart_exists, smart_path_join, smart_listdir
import argparse

def merge_video(pic_dir, mp4name, save_folder="s3://zqt/mtracker/expvis-videos/"):
    pic_list = [osp.join(pic_dir, tmp) for tmp in os.listdir(pic_dir)]

    img_num = len(pic_list) // 10
    # img_num = 200
    # h, w, _ = cat_list[0].shape
    h, w = 540, 4120
    # video infos
    out_size = [h, w]
    fps = 10
    fourcc = cv2.VideoWriter_fourcc("m", "p", "4", "v")
    out_video = cv2.VideoWriter()
    out_video_path = mp4name + ".mp4"
    out_video.open(out_video_path, fourcc, fps, (int(out_size[1]), int(out_size[0])), True)

    for i in tqdm(range(img_num)):
        dt_rv_img_list = []
        gt_rv_img_list = []
        for cam_id in range(4):
            tmp_dt_img = cv2.imread("{}/{}_token_init_{}_0_{}_rv_dt.jpg".format(pic_dir, i, i, cam_id))
            tmp_gt_img = cv2.imread("{}/{}_token_init_{}_0_{}_rv_gt.jpg".format(pic_dir, i, i, cam_id))
            dt_rv_img_list.append(tmp_dt_img)
            gt_rv_img_list.append(tmp_gt_img)
        dt_bev_img = cv2.imread("{}/{}_token_init_{}_0_bev_dt.jpg".format(pic_dir, i, i))  # (400, 210)
        gt_bev_img = cv2.imread("{}/{}_token_init_{}_0_bev_gt.jpg".format(pic_dir, i, i))

        dt_rv_cat = np.concatenate(dt_rv_img_list, axis=1)
        gt_rv_cat = np.concatenate(gt_rv_img_list, axis=1)

        dt_cat = np.concatenate((dt_rv_cat, cv2.resize(dt_bev_img, (567, 1080))), axis=1)
        gt_cat = np.concatenate((gt_rv_cat, cv2.resize(gt_bev_img, (567, 1080))), axis=1)

        # all_cat = np.concatenate((dt_cat, gt_cat), axis=0)
        all_cat = dt_cat

        out_frame = all_cat
        # out_frame = cv2.resize(out_frame, (4120, 1080))
        out_frame = cv2.resize(out_frame, (4120, 540))

        # write to video
        out_video.write(np.uint8(out_frame))
    out_video.release()

    trans_cmd = r"ffmpeg -i {} -pix_fmt yuv420p -vcodec libx264 -r 10 {} -hide_banner -y".format(
        out_video_path, out_video_path.replace(".mp4", "_x264.mp4")
    )
    os.system(trans_cmd)
    local_path = (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4")
    s3_path = smart_path_join(save_folder, (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4"))

    cmd = "aws --endpoint-url=http://oss.i.machdrive.cn s3 cp {} {}".format(local_path, s3_path)
    os.system(cmd)



    
if __name__ == "__main__":
    '''
    python merge_expvis.py --pic_dir /data/Perceptron/outputs/maptracker__maptracker_exp_z10_4v0r_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data/2025-02-10T22:48:42/pl_vis_fewwide_traindata_10fps

    python merge_expvis.py --pic_dir /data/Perceptron/outputs/0122model_vol_g1_v7_br_vr_ses_q400/pl_vis_jira_3542_nowarp

    python merge_expvis.py --pic_dir /data/Perceptron/outputs/0122model_vol_g1_v7_br_vr_ses_q400/pl_vis_jira_3542_nowarp


    python merge_expvis.py --pic_dir /data/Perceptron/outputs/0122model_vol_g1_v7_br_vr_ses_q400/pl_vis_jira3670_nowarp

    python merge_expvis.py --pic_dir /data/Perceptron/outputs/maptracker__maptracker_exp_z10_4v0r_single_cityhighway_plqs_g1_v7_br_vr_sesplit_prelabel_4w_pretrain/2025-02-14T01:25:32/pl_vis_ep19
    
    python merge_expvis.py --pic_dir /data/Perceptron/outputs/20250214/pl_vis
    
    python merge_expvis.py --pic_dir /data/Perceptron/outputs/20250214/pl_vis_10fps_bgr

    python merge_expvis.py --pic_dir /data/Perceptron/outputs/20250214/pl_vis_10fps_rgb

    python merge_expvis.py --pic_dir /data/Perceptron/outputs/20250214/pl_vis


    python merge_expvis_dt.py --pic_dir /data/Perceptron/outputs/20250214/pl_vis_jira5160


    python merge_expvis_dt.py --pic_dir /data/Perceptron/outputs/20250214/pl_vis_jira5159_right        

    python merge_expvis_dt.py --pic_dir /data/Perceptron/outputs/20250214/pl_vis_jira5124
    python merge_expvis_dt.py --pic_dir /data/Perceptron/outputs/2025-02-22T22:50:52/pl_vis
    
    python merge_expvis_dt.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_bda_retrain/2025-02-23T01:59:56/pl_vis_mcap_turnright
    
    
    python merge_expvis_dt.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_bda_retrain/2025-02-23T02:00:19/pl_vis_mcap_turnright_ep3

    python merge_expvis_dt.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_bda_retrain/2025-02-23T02:05:31/pl_vis_bda45_ep3

    python merge_expvis_dt.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_bda_retrain/2025-02-23T01:59:40/pl_vis

    '''
    parser = argparse.ArgumentParser()
    parser.add_argument("--pic_dir", type=str, default=None)
    args = parser.parse_args()
    pic_dir = args.pic_dir
    mp4name = "z18_mcap_turn_bda_5_ep3"
    merge_video(pic_dir, mp4name)