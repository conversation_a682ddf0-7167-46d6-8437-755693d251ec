'''
合并maptracker的实验可视化
'''

import os
import os.path as osp
import cv2
from tqdm import tqdm
import numpy as np
from pdb import set_trace as ste
from refile import smart_open, smart_exists, smart_path_join, smart_listdir
import argparse

def merge_video(pic_dir, mp4name, save_folder="s3://zqt/mtracker/expvis-videos/"):
    pic_list = [osp.join(pic_dir, tmp) for tmp in os.listdir(pic_dir)]

    # 重构pic_group
    pic_group_dict = {}
    for pic_path in tqdm(pic_list):
        tmp_group_id = int(pic_path.split("/")[-1].split("_token")[0])
        cam_id = int(pic_path.split("/")[-1].split("_")[-3])
        if tmp_group_id not in pic_group_dict:
            pic_group_dict[tmp_group_id] = {}
        mode = pic_path.split("_")[-1].split(".")[0]  # dt/gt
        view = pic_path.split("_")[-2]  # bev/rv
        if cam_id not in pic_group_dict[tmp_group_id]:
            pic_group_dict[tmp_group_id][cam_id] = {}  
        pic_group_dict[tmp_group_id][cam_id][mode+view] = pic_path
    roi_group_num = len(pic_group_dict)

    h, w = 1080, 4120
    # video infos
    out_size = [h, w]
    fps = 10
    fourcc = cv2.VideoWriter_fourcc("m", "p", "4", "v")
    out_video = cv2.VideoWriter()
    out_video_path = mp4name + ".mp4"
    out_video.open(out_video_path, fourcc, fps, (int(out_size[1]), int(out_size[0])), True)

    for group_id in tqdm(range(roi_group_num)):
        if group_id not in pic_group_dict:
            continue
        group_val = pic_group_dict[group_id]

        # 检查完整性
        valid = True
        for cam_id in range(4):
            if cam_id not in group_val:
                valid = False
                continue
            if "gtrv" not in group_val[cam_id] or "dtrv" not in group_val[cam_id]:
                valid = False
        if "gtbev" not in group_val[0] or "dtbev" not in group_val[0]:
            valid = False
            continue
                
        if not valid:
            continue

        dt_rv_img_list = []
        gt_rv_img_list = []
        for cam_id in range(4):
            tmp_dt_img = cv2.imread(group_val[cam_id]["dtrv"])
            tmp_gt_img = cv2.imread(group_val[cam_id]["gtrv"])
            dt_rv_img_list.append(tmp_dt_img)
            gt_rv_img_list.append(tmp_gt_img)
        dt_bev_img = cv2.imread(group_val[0]["dtbev"])  # (400, 210)
        gt_bev_img = cv2.imread(group_val[0]["gtbev"])

        dt_rv_cat = np.concatenate(dt_rv_img_list, axis=1)
        gt_rv_cat = np.concatenate(gt_rv_img_list, axis=1)

        dt_cat = np.concatenate((dt_rv_cat, cv2.resize(dt_bev_img, (567, 1080))), axis=1)
        gt_cat = np.concatenate((gt_rv_cat, cv2.resize(gt_bev_img, (567, 1080))), axis=1)

        all_cat = np.concatenate((dt_cat, gt_cat), axis=0)

        out_frame = all_cat
        out_frame = cv2.resize(out_frame, (4120, 1080))
        # write to video
        out_video.write(np.uint8(out_frame))
    out_video.release()

    trans_cmd = r"ffmpeg -i {} -pix_fmt yuv420p -vcodec libx264 -r 10 {} -hide_banner -y".format(
        out_video_path, out_video_path.replace(".mp4", "_x264.mp4")
    )
    os.system(trans_cmd)
    local_path = (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4")
    s3_path = smart_path_join(save_folder, (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4"))

    cmd = "aws --endpoint-url=http://oss.i.machdrive.cn s3 cp {} {}".format(local_path, s3_path)
    os.system(cmd)



    
if __name__ == "__main__":
    '''

    rlaunch --cpu=70  --memory=100000 --group=static_gpu --positive-tags=L20 --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- 

    python merge_expvis.py --pic_dir /data/Perceptron/outputs/maptracker__maptracker_exp_z10_4v0r_single_cityhighway_plqs_g1_v7_br_vr_sesplit_v2data/2025-02-10T22:48:42/pl_vis_fewwide_traindata_10fps

    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_resample_erase/2025-02-26T18:01:48/pl_vis

    rlaunch --cpu=70  --memory=100000 --group=static_gpu --positive-tags=L20 --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- \
    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_resample_erase_no120/2025-02-26T18:02:51/pl_vis


    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_resample_erase/2025-02-26T18:01:48/pl_vis

    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_resample_erase_no120/2025-02-26T18:02:51/pl_vis

    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_resample_erase/2025-02-26T18:01:48/pl_vis_02


    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/released__maptracker_exp_z10_4v0r_single_cityhighway_0214release_bev_new_resample_erase/2025-02-26T18:01:48/pl_vis_01
    
    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v3data/latest/pl_vis_thr04

    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v3data_full/2025-02-28T02:33:24/pl_vis_ep3

    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v3data_full/2025-02-28T02:33:24/pl_vis_ep3_thr01
    
    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v3data_full/2025-02-28T02:33:24/pl_vis_ep3_thr02

    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v3data_full_nowarp_resample_ft_attr_bsl/2025-03-04T05:51:57/pl_vis_turntiny

    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v3data_full_nowarp_resample_ft_attr_bsl/2025-03-04T05:51:57/pl_vis_failturn


    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v3data_full_nowarp_resample_ft_attr_bsl_enhance_right_316_attr01_stage3_rightonly/latest/pl_vis_ep3_jira5122_thr04
    
    
    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v3data_full_nowarp_resample_ft_attr_bsl_enhance_right_316_attr01_stage3_rightonly/latest/pl_vis_ep3_jira5122_thr03
    

    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/20250306/pl_vis_leftfull
    python merge_expvis_new.py --pic_dir /data/Perceptron/outputs/20250306/pl_vis_rightfull
    '''
    parser = argparse.ArgumentParser()
    parser.add_argument("--pic_dir", type=str, default=None)
    args = parser.parse_args()
    pic_dir = args.pic_dir
    mp4name = pic_dir.split("/")[-3] + "-" + pic_dir.split("/")[-2] + "-" + pic_dir.split("/")[-1] + "turn_tiny_10"
    merge_video(pic_dir, mp4name)