'''
合并maptracker的实验可视化
'''

import os
import os.path as osp
import cv2
from tqdm import tqdm
import numpy as np
from pdb import set_trace as ste
from refile import smart_open, smart_exists, smart_path_join, smart_listdir
import argparse

def merge_video(pic_dir, mp4name, save_folder="s3://zqt/mtracker/expvis-videos/"):
    pic_list = [osp.join(pic_dir, tmp) for tmp in os.listdir(pic_dir)]

    # 重构pic_group
    pic_group_dict = {}
    for pic_path in tqdm(pic_list):
        tmp_group_id = int(pic_path.split("/")[-1].split("_token")[0])
        cam_id = int(pic_path.split("/")[-1].split("_")[-3])
        if tmp_group_id not in pic_group_dict:
            pic_group_dict[tmp_group_id] = {}
        mode = pic_path.split("_")[-1].split(".")[0]  # dt/gt
        view = pic_path.split("_")[-2]  # bev/rv
        if cam_id not in pic_group_dict[tmp_group_id]:
            pic_group_dict[tmp_group_id][cam_id] = {}  
        pic_group_dict[tmp_group_id][cam_id][mode+view] = pic_path
    roi_group_num = len(pic_group_dict)

    key_list = list(pic_group_dict.keys())
    max_key = np.array(key_list).max()

    h, w = 540, 3394
    # video infos
    out_size = [h, w]
    fps = 10
    fourcc = cv2.VideoWriter_fourcc("m", "p", "4", "v")
    out_video = cv2.VideoWriter()
    out_video_path = mp4name + ".mp4"
    out_video.open(out_video_path, fourcc, fps, (int(out_size[1]), int(out_size[0])), True)


    for group_id in tqdm(range(0, max_key, 1)):
        if group_id not in pic_group_dict:
            continue
        group_val = pic_group_dict[group_id]

        # 检查完整性
        valid = True
        for cam_id in range(4):
            if cam_id not in group_val:
                valid = False
                continue
            if "gtrv" not in group_val[cam_id] or "dtrv" not in group_val[cam_id]:
                valid = False
        if "gtbev" not in group_val[0] or "dtbev" not in group_val[0]:
            valid = False
            continue
                
        if not valid:
            continue

        dt_rv_img_list = []
        gt_rv_img_list = []
        for cam_id in range(2,3):
            tmp_dt_img = cv2.imread(group_val[cam_id]["dtrv"])
            tmp_gt_img = cv2.imread(group_val[cam_id]["gtrv"])
            dt_rv_img_list.append(tmp_dt_img)
            gt_rv_img_list.append(tmp_gt_img)
        
        for cam_id in range(0, 1):
            tmp_dt_img = cv2.imread(group_val[cam_id]["dtrv"])
            tmp_gt_img = cv2.imread(group_val[cam_id]["gtrv"])
            dt_rv_img_list.append(tmp_dt_img)
            gt_rv_img_list.append(tmp_gt_img)
        
        for cam_id in range(3, 4):
            tmp_dt_img = cv2.imread(group_val[cam_id]["dtrv"])
            tmp_gt_img = cv2.imread(group_val[cam_id]["gtrv"])
            dt_rv_img_list.append(tmp_dt_img)
            gt_rv_img_list.append(tmp_gt_img)

        dt_bev_img = cv2.imread(group_val[0]["dtbev"])  # (400, 210)
        gt_bev_img = cv2.imread(group_val[0]["gtbev"])

        dt_rv_cat = np.concatenate(dt_rv_img_list, axis=1)
        gt_rv_cat = np.concatenate(gt_rv_img_list, axis=1)

        dt_cat = np.concatenate((dt_rv_cat, cv2.resize(dt_bev_img, (1029, 1080))), axis=1)
        gt_cat = np.concatenate((gt_rv_cat, cv2.resize(gt_bev_img, (1029, 1080))), axis=1)

        # all_cat = np.concatenate((dt_cat, gt_cat), axis=0)
        all_cat = dt_cat
        out_frame = all_cat
        # out_frame = cv2.resize(out_frame, (3394, 1080))
        out_frame = cv2.resize(out_frame, (3394, 540))
        # write to video
        out_video.write(np.uint8(out_frame))
    out_video.release()

    trans_cmd = r"ffmpeg -i {} -pix_fmt yuv420p -vcodec libx264 -r 10 {} -hide_banner -y".format(
        out_video_path, out_video_path.replace(".mp4", "_x264.mp4")
    )
    os.system(trans_cmd)
    local_path = (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4")
    s3_path = smart_path_join(save_folder, (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4"))

    cmd = "aws --endpoint-url=http://oss.i.machdrive.cn s3 cp {} {}".format(local_path, s3_path)
    os.system(cmd)



    
if __name__ == "__main__":
    '''

    rlaunch --cpu=70  --memory=100000 --group=static_gpu --positive-tags=L20 --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp -- 

    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/20250310/pl_vis_jira0313
    
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/20250310/pl_vis_jira0313
    
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_ft_swan_lowlr_8m/2025-04-07T23:15:57/checkpoint_epoch_15-jira-15396-thr0.3
    
    python merge_expvis_new_small_dt.py --pic_dir  /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_ft_swan_lowlr/2025-04-07T15:52:10/checkpoint_epoch_15-jira-15396-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir  /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_ft_swan/2025-04-07T15:52:39/checkpoint_epoch_17-jira-15396-thr0.3
    

    python merge_expvis_new_small_dt.py --pic_dir  /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_ft_swan/2025-04-08T12:02:07/checkpoint_epoch_3-jira-15396-thr0.3
    
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/2025-04-02T23:46:20/checkpoint_epoch_23-jira-15396-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_ft_swan/2025-04-08T12:02:07/checkpoint_epoch_9-jira-15396-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_760wpretrain/2025-04-09T01:56:11/checkpoint_epoch_9-jira-15396-thr0.3
    
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_760wpretrain_3e-4/2025-04-09T01:55:59/checkpoint_epoch_9-jira-15396-thr0.3
    
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_ft_swan/2025-04-08T12:02:07/checkpoint_epoch_23-jira-15396-thr0.3
    
    
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_1-jira-15396-thr0.3
   

    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata/latest/checkpoint_epoch_1-jira-15396-thr0.3
   

    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_13-jira-15396-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata/2025-04-10T18:50:15/checkpoint_epoch_13-jira-15396-thr0.3
   

    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-jira-15396-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-jira-16003-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-jira-16203-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-jira-16378-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-jira-16423-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-jira-16427-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-jira-16442-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-jira-16486-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-jira-16494-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-jira-16520-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-jira-16538-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-jira-16553-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_nowarp_divline_swan_divdata_hotfix/2025-04-10T18:50:17/checkpoint_epoch_17-jira-16554-thr0.3
   
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_warp_divline_swan_divdata/2025-04-16T02:02:55/checkpoint_epoch_21-valset-v20250321-forward-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_warp_divline_swan_divdata/2025-04-16T02:02:55/checkpoint_epoch_21-valset-v20250321-lanechange-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_warp_divline_swan_divdata/2025-04-16T02:02:55/checkpoint_epoch_21-valset-v20250321-left-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_v5data_full_fasterm_warp_divline_swan_divdata/2025-04-16T02:02:55/checkpoint_epoch_21-valset-v20250321-right-thr0.3
    
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_0v0r1l_newdata/2025-04-19T13:39:01/dump_model/checkpoint_epoch_23_jira-19618-lidar
    
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_0v0r1l_newdata/2025-04-20T20:48:17/dump_model/checkpoint_epoch_10_jira-19618-lidar
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_0v0r1l_newdata/2025-04-20T20:48:17/dump_model/checkpoint_epoch_17_jira-19618-lidar
    
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_0v0r1l_newdata/2025-04-20T20:48:17/dump_model/checkpoint_epoch_32_jira-19618-lidar
    
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r1l_newdata_fasternetm/2025-04-23T23:37:20/dump_model/checkpoint_epoch_15_jira-19618-lidar
    
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_0v0r1l_newdata_curbonly/2025-04-23T18:06:52/dump_model/checkpoint_epoch_38_jira-19618-lidar
    

    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_r50_warp_0425setting_1w2clip_0429_0401pretrain/latest/checkpoint_epoch_6-jira-15396-thr0.3
    python merge_expvis_new_small_dt.py --pic_dir /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_fasternetm_warp_0425setting_1w2clip_0429/2025-04-30T12:03:37/checkpoint_epoch_23-valset-v20250321-left-thr0.3
    
    '''
    parser = argparse.ArgumentParser()
    parser.add_argument("--pic_dir", type=str, default=None)
    args = parser.parse_args()
    pic_dir = args.pic_dir
    mp4name = pic_dir.split("/")[-3] + "-" + pic_dir.split("/")[-2] + "-" + pic_dir.split("/")[-1]+ "thr03"
    mp4name = mp4name.replace(":", "-")
    merge_video(pic_dir, mp4name)
