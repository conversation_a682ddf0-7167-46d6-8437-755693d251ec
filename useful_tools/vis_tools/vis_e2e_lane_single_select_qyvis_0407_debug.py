'''
批量绘制e2e json可视化
'''
import os
import os.path as osp
import cv2
from tqdm import tqdm
import numpy as np
import nori2 as nori
import torch
from skimage import io as skimage_io
import io
import quaternion
from pdb import set_trace as ste
import json
from refile import smart_open, smart_path_join
import concurrent.futures
import time
import copy
nori_fetcher = nori.Fetcher()

def get_attr_color(tmp_attr):
    '''
    获取可视化颜色
    '''
    if tmp_attr["road_curb"] == 1:
        color = (0, 0, 205) # 路沿 红色
    elif tmp_attr["guardrail"] == 1:
        color = (139, 0, 139) # 栏杆 紫色
    elif tmp_attr["lane_type"]["shape"] == "single" and tmp_attr["lane_type"]["dashed"] == "dotted":
        color = (0, 139, 0) # 虚线 绿色
    elif tmp_attr["lane_type"]["shape"] == "single" and tmp_attr["lane_type"]["dashed"] == "solid":
        color = (139, 0, 0) # 实线 蓝色
    elif tmp_attr["lane_type"]["shape"] == "double" and tmp_attr["lane_type"]["dashed"] == "dotted":
        color = (0, 255, 0) # 双虚线 深绿色
    elif tmp_attr["lane_type"]["shape"] == "double" and tmp_attr["lane_type"]["dashed"] == "solid":
        color = (255, 0, 0) # 双实线 深蓝色
    else:
        color = (0, 165, 255)
    return color

def draw_dashed_lines(img, points, color):
    """
    在给定图像上根据点列绘制虚线。

    :param img: 要绘制虚线的图像
    :param points: 点列，形状为 (n, 2) 的numpy数组,n为点的数量,每个点包含(x, y)坐标
    :param dash_length: 虚线的长度(单位：像素)
    :param gap_length: 虚线间隔的长度(单位:像素)
    """
    eps = 1e-6
    for i in range(len(points) - 1):
        start_point = points[i]
        end_point = points[i + 1]
        distance = np.sqrt(np.sum((end_point - start_point) ** 2))
        num_dashes = 2
        dash_length = distance / num_dashes * 0.5
        gap_length = dash_length
        # num_dashes = int(distance / (dash_length + gap_length))
        # print(points, " num dash: ", num_dashes)
        for j in range(num_dashes):
            start_dash = int(j * (dash_length + gap_length))
            end_dash = int(start_dash + dash_length)
            fraction_start = start_dash / (distance + eps)
            fraction_end = end_dash / (distance + eps)
            dash_start_point = (start_point * (1 - fraction_start) + end_point * fraction_start).astype(int)
            dash_end_point = (start_point * (1 - fraction_end) + end_point * fraction_end).astype(int)
            cv2.line(img, tuple(dash_start_point), tuple(dash_end_point), color, 5)
            # print(points, "dash start: ", dash_start_point, "dash end: ", dash_end_point)
    return img

def get_lidar_ego_rt(calib):
    return get_rt(calib)

def get_rt(calib):
    if "extrinsic" in calib:
        param = calib["extrinsic"]
    else:
        param = calib

    if "transform" in param:
        rota = param["transform"]["rotation"]
        tran = param["transform"]["translation"]
    else:
        rota = param["rotation"]
        tran = param["translation"]
    q = np.quaternion(rota["w"], rota["x"], rota["y"], rota["z"])
    t = np.array([tran["x"], tran["y"], tran["z"]]).astype(np.float32)
    r = quaternion.as_rotation_matrix(q).astype(np.float32)
    rt = np.eye(4)
    rt[:3, :3] = r
    rt[:3, 3] = t
    return rt

def original_img_undistort(img, K, D, mode="fisheye"):
    D, K = np.array(D).reshape(-1).astype(np.float32), np.array(K).reshape(3, 3)
    new_cam_intrinsic = K  # 不动
    # if len(D) == 5:
    #     m = cv2.undistort(img, K, D, newCameraMatrix=new_cam_intrinsic)
    # else:
    #     m = cv2.fisheye.undistortImage(img, K, D, Knew=new_cam_intrinsic)
    
    if mode=="pinhole":
        m = cv2.undistort(img, K, D, newCameraMatrix=new_cam_intrinsic)
    else:
        m = cv2.fisheye.undistortImage(img, K, D, Knew=new_cam_intrinsic)
    image_undistort = m
    return image_undistort

def draw_mapline_on_rv_withattr(cam_name, ordered_pts_np, raw_attr, img, calib, lidar_ego_rt, use_lidar_ego=False, ego_interv=5):
    rt = get_rt(calib[cam_name])
    K = np.array(calib[cam_name]["intrinsic"]["K"]).reshape(3, 3)
    ego_lidar_rt = np.linalg.inv(lidar_ego_rt)

    ego_interv = ego_interv # 默认1个点的间隔进行虚线绘制
    last_point = np.array([ordered_pts_np[-1].tolist()])
    ordered_pts_np = ordered_pts_np[:-1:ego_interv, :]
    ordered_pts_np = np.concatenate((ordered_pts_np, last_point), axis=0)

    # 虚线
    is_dotted = raw_attr["lane_type"]["dashed"] == "dotted"
    # is_curb = (raw_attr["road_curb"] == 1 or raw_attr["guardrail"] == 1)
    # if is_curb:
    #     color = (0, 0, 255)
    # else:
    #     color = (255, 0, 0)
    if str(raw_attr["road_curb"]) == "1" or str(raw_attr["guardrail"]) == "1":
        color = (0, 0, 255)
    else:
        if raw_attr["lane_type"]["dashed"] == "solid":
            color = (255, 0 ,0)
        else:
            color = (0, 255, 0)
        # 黄线
        if raw_attr["lane_type"]["color"] == "yellow":
            color = (0, 255, 255)

    for pts_idx in range(len(ordered_pts_np) - 1):
        ego_line = ordered_pts_np[pts_idx : pts_idx + 2, :]  # [2, 3]
        ego_pts = np.concatenate([ego_line, np.ones((ego_line.shape[0], 1))], axis=1)
        pts_3d = (ego_lidar_rt @ ego_pts.T).T if use_lidar_ego else ego_pts
        # import pdb; pdb.set_trace()
        # print(pts_3d.shape, rt.shape)
        pts_rv = (rt @ pts_3d.T).T
        pts_rv = (K @ pts_rv[:, :3].T).T
        if all(pts_rv[:, 2] > 0):
            # 线段深度为正
            line_img = pts_rv[:, :2] / pts_rv[:, 2:3]
            try:
                if is_dotted:
                    # print(ego_pts, line_img)
                    draw_dashed_lines(img, line_img, color)
                else:
                    # print("solid", ego_pts, line_img)
                    cv2.line(
                        img,
                        (round(line_img[0][0]), round(line_img[0][1])),
                        (round(line_img[1][0]), round(line_img[1][1])),
                        color,
                        thickness=10,
                    )
            except cv2.error as e:
                print(e)
                continue
    return

def draw_mapsign_on_rv_withattr(cam_name, ordered_pts_np, raw_attr, color, img, calib, lidar_ego_rt, use_lidar_ego=False):
    rt = get_rt(calib[cam_name])
    K = np.array(calib[cam_name]["intrinsic"]["K"]).reshape(3, 3)
    ego_lidar_rt = np.linalg.inv(lidar_ego_rt)

    for pts_idx in range(len(ordered_pts_np) - 1):
        ego_line = ordered_pts_np[pts_idx : pts_idx + 2, :]  # [2, 3]
        ego_pts = np.concatenate([ego_line, np.ones((ego_line.shape[0], 1))], axis=1)
        pts_3d = (ego_lidar_rt @ ego_pts.T).T if use_lidar_ego else ego_pts
        # import pdb; pdb.set_trace()
        # print(pts_3d.shape, rt.shape)
        pts_rv = (rt @ pts_3d.T).T
        pts_rv = (K @ pts_rv[:, :3].T).T
        if all(pts_rv[:, 2] > 0):
            # 线段深度为正
            line_img = pts_rv[:, :2] / pts_rv[:, 2:3]
            try:
                # print("solid", ego_pts, line_img)
                cv2.line(
                    img,
                    (round(line_img[0][0]), round(line_img[0][1])),
                    (round(line_img[1][0]), round(line_img[1][1])),
                    color,
                    thickness=5,
                )
            except cv2.error as e:
                print(e)
                continue
    return

def get_real_fork_merge_points(lane_data):
    '''
    获取真实分岔/合并点
    '''
    point_dict, EQL_start_dict, EQL_end_dict = get_start_end_points_pool(lane_data)
    fork_points = get_fork_points(EQL_start_dict)
    merge_points = get_merge_points(EQL_end_dict)
    
    return fork_points, merge_points

def get_start_end_points_pool(bev_lanes):
    lane_ids = list(bev_lanes.keys())

    EQL_start_dict = dict()
    EQL_end_dict = dict()
    point_dict = dict()
    
    for lane_id in lane_ids:
        lane_points = bev_lanes[lane_id]["points"]
        lane_spt = lane_points[0]
        lane_ept = lane_points[-1]
        # print(lane_spt, lane_ept)
        
        # 起点
        if tuple(lane_spt) not in point_dict:
            point_dict[tuple(lane_spt)] = [lane_id]
        else:
            point_dict[tuple(lane_spt)].append(lane_id)
        # 终点
        if tuple(lane_ept) not in point_dict:
            point_dict[tuple(lane_ept)] = [lane_id]
        else:
            point_dict[tuple(lane_ept)].append(lane_id)

        # 共起点
        if tuple(lane_spt) not in EQL_start_dict:
            EQL_start_dict[tuple(lane_spt)] = [lane_id]
        else:
            EQL_start_dict[tuple(lane_spt)].append(lane_id)
        # 共终点
        if tuple(lane_ept) not in EQL_end_dict:
            EQL_end_dict[tuple(lane_ept)] = [lane_id]
        else:
            EQL_end_dict[tuple(lane_ept)].append(lane_id)
    
    return point_dict, EQL_start_dict, EQL_end_dict

def get_fork_points(EQL_start_dict):
    # 根据共起点字典判定真实分叉点
    fork_points = []
    for point_key, val in EQL_start_dict.items():
        if len(val) > 1:
            fork_points.append(list(point_key))  # (egox, egoy, egoz)
    return fork_points

def get_merge_points(EQL_end_dict):
    # 根据共终点字典判定真实分叉点
    merge_points = []
    for point_key, val in EQL_end_dict.items():
        if len(val) > 1:
            merge_points.append(list(point_key))
    return merge_points


def vis_single_cam(frame, calib, cam_name, final_lanes_gt):
    # rv图
    car_id = "z10_xx"
    nori_img_id = frame["sensor_data"][cam_name]["nori_id"]
    vid = int(nori_img_id.split(",")[0])
    nori_path = frame["sensor_data"][cam_name]["nori_path"]
    nori_path = frame["sensor_data"][cam_name]["nori_path"].replace("s3://", "/mnt/caiqianxi/")

    vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
    img = cv2.imdecode(np.frombuffer(vreader.get(nori_img_id), dtype=np.uint8), 1)
    rv_img = img
    lidar_ego_rt = get_lidar_ego_rt(calib["lidar_ego"])

    # update 10.24, 去畸变
    cam_K = calib[cam_name]["intrinsic"]["K"]
    cam_D = calib[cam_name]["intrinsic"]["D"]
    cam_mode = calib[cam_name]["intrinsic"]["distortion_model"]

    rv_img = original_img_undistort(rv_img, cam_K, cam_D, cam_mode)

    # 人工标注
    lane_ids = list(final_lanes_gt.keys())
    for li, lane_id in enumerate(lane_ids):
        pts_ego = np.array(final_lanes_gt[lane_id]["points"])
        tmp_attr = final_lanes_gt[lane_id]["attribute"]
        color = get_attr_color(tmp_attr)  # 获取道路边界/实虚混合显示图像的color
        # rv
        draw_mapline_on_rv_withattr(cam_name, pts_ego, tmp_attr, rv_img, calib, lidar_ego_rt, use_lidar_ego=True, ego_interv=1)

    timestamp = frame["sensor_data"]["cam_front_120"]["timestamp"]
    frame_data_time = time.localtime(int(float(timestamp)))
    detail_time = time.strftime("%Y-%m-%d %H:%M:%S", frame_data_time)

    cv2.putText(rv_img, "car id: {}".format(car_id), (50, 70), cv2.FONT_HERSHEY_SIMPLEX, 2.0, (0, 255, 255), 4)
    cv2.putText(rv_img, "timestamp: {} detail time: {}".format(timestamp, detail_time), (50, 170), cv2.FONT_HERSHEY_SIMPLEX, 2.0, (0, 255, 255), 4)
    cv2.putText(rv_img, "Manual GT", (50, 270), cv2.FONT_HERSHEY_SIMPLEX, 2.0, (0, 255, 255), 4)
    cv2.putText(rv_img, "{}".format(json_path.split('/')[-1]), (50, 370), cv2.FONT_HERSHEY_SIMPLEX, 2.0, (0, 255, 255), 4)
    
    # cv2.imwrite('rv_img_{}.jpg'.format(cam_name), rv_img)
    # ste()
    return rv_img

def my_draw_bev(frame):
    bev_img = np.zeros((600, 1300, 3))  # 左右30, 前100, 后30
    lane_gt = frame["bev"]["lane"]
    for lane_id, lane_item in lane_gt.items():
        lane_points = np.array(lane_item["points"])
        lane_attr = lane_item["attribute"]
        if str(lane_attr["road_curb"]) == "1" or str(lane_attr["guardrail"]) == "1":
            color = (0, 0, 255)
        else:
            if lane_attr["lane_type"]["dashed"] == "solid":
                color = (255, 0 ,0)
            else:
                color = (0, 255, 0)
            # 黄线
            if lane_attr["lane_type"]["color"] == "yellow":
                color = (0, 255, 255)
        for pts_idx in range(len(lane_points) - 1):
            start_pt = (lane_points[pts_idx][:2] - np.array([-30, -30])) * 10
            end_pt = (lane_points[pts_idx + 1][:2] - np.array([-30, -30])) * 10
            cv2.line(bev_img, (int(start_pt[0]), int(start_pt[1])), (int(end_pt[0]), int(end_pt[1])), color, 2)

    # 画mask
    cv2.imwrite("bev.jpg", bev_img)
    # 画mask
    mask_gt = frame["bev"]["mask"]
    overlay = bev_img.copy()
    for mask_id, mask_polygon in mask_gt.items():
        # 对每个顶点进行平移和缩放
        mask_polygon_np = np.array(mask_polygon["points"])[:,:2]
        mask_polygon_np = (mask_polygon_np - np.array([-30, -30])) * 10
        # 使用多边形填充函数绘制mask
        cv2.fillPoly(overlay, [mask_polygon_np.astype(np.int32)], (0, 165, 255))  # 假设mask用绿色填充
    # 设置透明度
    alpha = 0.5  # 透明度参数，范围为0到1
    cv2.addWeighted(overlay, alpha, bev_img, 1 - alpha, 0, bev_img)
    # cv2.imwrite("bev.jpg", bev_img)
    bev_img = cv2.flip(bev_img, 0)
    return bev_img
    
def vis_json(json_path, s3_save_folder, local_save_folder="/data/vis_e2e"):
    json_data = json.load(smart_open(json_path, "r"))
    frames = json_data["frames"]
    calib = json_data["calibrated_sensors"]
    lidar_ego_rt = get_lidar_ego_rt(calib["lidar_ego"])
    car_id = json_data["car_id"]

    # video infos
    out_size = [1080, 2568]
    fps = 10
    fourcc = cv2.VideoWriter_fourcc("m", "p", "4", "v")
    out_video = cv2.VideoWriter()

    mp4name = json_path.replace("s3://zqt/", "").replace("/", "-").split(".")[0]
    out_video_path = mp4name + ".mp4"
    os.makedirs(local_save_folder, exist_ok=True)
    out_video_path = smart_path_join(local_save_folder, out_video_path)
    out_video.open(out_video_path, fourcc, fps, (int(out_size[1]), int(out_size[0])), True)

    for frame_idx, frame in enumerate(tqdm(frames[::1])):
        # 先判定分叉合并
        final_lanes_gt = frame["bev"]["lane"]
        fork_points, merge_points = get_real_fork_merge_points(final_lanes_gt)

        # 找到curb
        final_curb_gt = copy.deepcopy(final_lanes_gt)
        for lane_id, lane_item in final_lanes_gt.items():
            lane_attr = lane_item["attribute"]
            if lane_attr["road_curb"] == "NAN":
                final_curb_gt.pop(lane_id)
                continue
            
            lane_points = np.array(lane_item["points"])
            ego_x_min = lane_points[:, 0].min()
            ego_y_min = lane_points[:, 1].min()
            ego_x_max = lane_points[:, 0].max()
            ego_y_max = lane_points[:, 1].max()
            if ego_x_min > 100 or ego_x_max < 0:
                final_curb_gt.pop(lane_id)
        
        # 分析每个curb
        for lane_id, lane_item in final_curb_gt.items():
            lane_points = np.array(lane_item["points"])
            lane_kept_x = np.logical_and(lane_points[:, 0] > 0, lane_points[:, 0] < 100)
            lane_kept_y = np.logical_and(lane_points[:, 1] > -30, lane_points[:, 1] < 30)
            lane_kept = np.logical_and(lane_kept_x, lane_kept_y)
            lane_points = lane_points[lane_kept]
            ego_x_min = lane_points[:, 0].min()
            ego_y_min = lane_points[:, 1].min()
            ego_x_max = lane_points[:, 0].max()
            ego_y_max = lane_points[:, 1].max()
            print("lane_id : {} xmin {} ymin {} xmax {} ymax {}".format(lane_id, ego_x_min, ego_y_min, ego_x_max, ego_y_max))
        ste()


        # if len(merge_points) > 0 or len(fork_points) > 0:
        #     pass
        # else:
        #     continue
        roi_cam_list = [
            "cam_front_120",
            "cam_front_30",
            "cam_front_left_100",
            "cam_front_right_100"
        ]
        rv_list = []
        for cam_name in roi_cam_list:
            tmp_rv = vis_single_cam(frame, calib, cam_name, final_lanes_gt)
            rv_img = cv2.resize(tmp_rv, (1920, 1080))
            rv_list.append(rv_img)
        rv_cat_front = np.concatenate(rv_list[:2], axis=1)  # (1080, 3840)
        rv_cat_leftright = np.concatenate(rv_list[2:], axis=1)  # (1080, 3840)
        
        rv_cat = np.concatenate((rv_cat_front, rv_cat_leftright), axis=0)  # (2160, 3840)
        rv_cat = cv2.resize(rv_cat, (1920, 1080))
        cv2.imwrite("rv.jpg", rv_cat)
        ste()

        # 绘制bev
        bev_img = my_draw_bev(frame)
        bev_img = cv2.rotate(bev_img, cv2.ROTATE_90_COUNTERCLOCKWISE)
        bev_img = cv2.resize(bev_img, (648, 1080))

        cat_img = np.concatenate((rv_cat, bev_img), axis=1)
        out_video.write(np.uint8(cat_img))
    out_video.release()
    print(out_video_path)
    trans_cmd = r"ffmpeg -i {} -pix_fmt yuv420p -vcodec libx264 -r 10 {} -hide_banner -y".format(
        out_video_path, out_video_path.replace(".mp4", "_x264.mp4")
    )
    print(trans_cmd)
    os.system(trans_cmd)
    cmd = "aws --endpoint-url=http://oss.i.machdrive.cn s3 cp {} {}".format(out_video_path.replace(".mp4", "_x264.mp4"), smart_path_join(s3_save_folder, (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4")))
    os.system(cmd)

    # trans_cmd_1 = r"rm {} ".format(out_video_path)
    # trans_cmd_2 = r"rm {} ".format(out_video_path.replace(".mp4", "_x264.mp4"))
    # os.system(trans_cmd_1)
    # os.system(trans_cmd_2)
    return
        

if __name__ == "__main__":
    # json_path = "s3://zqt/dataset/cqx_20250312_clip_finish_2164.json"
    json_path = "s3://zqt/dataset/cqx_bmk_clip_finish_142.json"

    json_data = json.load(smart_open(json_path, "r"))
    json_list = json_data["paths"] if isinstance(json_data, dict) else json_data
    s3_save_folder = "s3://zqt/car/debug_vis_maskbev_0407_debug2/"
    local_save_folder = "/data/vis_e2e"

    json_list = ["s3://caiqianxi/json_nori/road_entrance_e2emulti_fl_newcp_det_finished/E2E_laneline_recycle/180586/car_z13/ppl_bag_20250216_094642_det/v0_250219_154753/v0_250227_074743_ppl_bag_20250216_094642_det_14647_15084_data_post.json",]

    # 单进程
    for json_path in tqdm(json_list):
        vis_json(json_path, s3_save_folder, local_save_folder)

    # # 多进程
    # results = set()
    # with concurrent.futures.ProcessPoolExecutor(max_workers=100) as executor:
    #     for json_path in tqdm(json_list):
    #         results.add(executor.submit(vis_json, json_path, s3_save_folder, local_save_folder))
    # for future in tqdm(results):
    #     future.result()


    # json_path = "s3://zqt/car/20250306_train_right_175_clips.json"
    # json_data = json.load(smart_open(json_path, "r"))
    # json_list = json_data["paths"] if isinstance(json_data, dict) else json_data

    # # # 单进程
    # # for json_path in tqdm(json_list):
    # #     vis_json(json_path)

    # # 多进程
    # results = set()
    # with concurrent.futures.ProcessPoolExecutor(max_workers=100) as executor:
    #     for json_path in tqdm(json_list):
    #         results.add(executor.submit(vis_json, json_path))
    # for future in tqdm(results):
    #     future.result()