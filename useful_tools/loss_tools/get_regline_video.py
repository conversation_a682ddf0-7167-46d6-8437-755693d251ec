'''
合并maptracker的实验可视化
'''

import os
import os.path as osp
import cv2
from tqdm import tqdm
import numpy as np
from pdb import set_trace as ste
from refile import smart_open, smart_exists, smart_path_join, smart_listdir
import argparse

def merge_video(pic_dir, mp4name, save_folder="s3://zqt/mtracker/debugvis-videos/"):
    pic_list = [osp.join(pic_dir, tmp) for tmp in os.listdir(pic_dir)]
    pic_list.sort()
    img_num = len(pic_list)
    # img_num = 1000
    # h, w, _ = cat_list[0].shape
    demo_img = cv2.imread(pic_list[0])
    h, w, _ = demo_img.shape
    # video infos
    out_size = [h, w]
    fps = 10
    fourcc = cv2.VideoWriter_fourcc("m", "p", "4", "v")
    out_video = cv2.VideoWriter()
    out_video_path = mp4name + ".mp4"
    out_video.open(out_video_path, fourcc, fps, (int(out_size[1]), int(out_size[0])), True)

    for img_path in tqdm(pic_list):
        print(img_path)
        out_frame = cv2.imread(img_path)
        # write to video
        out_video.write(np.uint8(out_frame))
    out_video.release()

    trans_cmd = r"ffmpeg -i {} -pix_fmt yuv420p -vcodec libx264 -r 10 {} -hide_banner -y".format(
        out_video_path, out_video_path.replace(".mp4", "_x264.mp4")
    )
    os.system(trans_cmd)
    local_path = (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4")
    s3_path = smart_path_join(save_folder, (out_video_path.split("/")[-1]).replace(".mp4", "_x264.mp4"))

    cmd = "aws --endpoint-url=http://oss.i.machdrive.cn s3 cp {} {}".format(local_path, s3_path)
    os.system(cmd)



    
if __name__ == "__main__":
    '''
    python get_regline_video.py --pic_dir /data/regline_0312

    '''
    parser = argparse.ArgumentParser()
    parser.add_argument("--pic_dir", type=str, default=None)
    args = parser.parse_args()
    pic_dir = args.pic_dir
    mp4name = "regline_0312_newdata_top1000"
    merge_video(pic_dir, mp4name)