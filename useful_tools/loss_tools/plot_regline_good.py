'''
绘制高loss的bev pred/gt
'''
import json
import numpy as np
from tqdm import tqdm
import copy
from pdb import set_trace as ste
from refile import smart_path_join, smart_listdir, smart_exists, smart_open
import concurrent.futures
import cv2
import os


def plot_once(json_path, local_save_dir="/data/regline_0312_good"):
    '''
    json path
    '''
    json_data = json.load(smart_open(json_path, "r"))

    bev_h = 1000
    bev_w = 600
    bev_img = np.zeros((bev_h, bev_w, 3), dtype=np.uint8)
    line_scores_np = np.array(json_data["line_scores"])
    lines_np = np.array(json_data["lines"])

    line_attr = np.array(json_data["attrs_lines"])
    roi_index = np.where(line_scores_np > 0.3)[0]

    for idx in roi_index:
        tmp_line = lines_np[idx]
        tmp_attr = line_attr[idx]
        for point_idx in range(len(tmp_line)-1):
            x_int, y_int = round(tmp_line[point_idx][0] * bev_w), round(tmp_line[point_idx][1] * bev_h)
            cv2.circle(bev_img, (x_int, y_int), 4, (255, 255, 255), -1)
            
            tmp_curb_attr = tmp_attr[point_idx][2] # curb
            if tmp_curb_attr > 0:
                tmp_color = (0, 0, 255)
            else:
                tmp_color = (255, 0, 0)
            cv2.line(bev_img, (x_int, y_int), (round(tmp_line[point_idx+1][0] * bev_w), round(tmp_line[point_idx+1][1] * bev_h)), tmp_color, 2)
    
    bev_img_gt = np.zeros((bev_h, bev_w, 3), dtype=np.uint8)
    line_gt = json_data["map_gt"]["0"]
    curb_gt = json_data["map_gt"]["1"]

    for line_tuple in line_gt:
        tmp_line = line_tuple[0]
        for point_idx in range(len(tmp_line)-1):
            x_int, y_int = round(tmp_line[point_idx][0] * bev_w), round(tmp_line[point_idx][1] * bev_h)
            cv2.circle(bev_img_gt, (x_int, y_int), 1, (255, 255, 255), -1)
            cv2.line(bev_img_gt, (x_int, y_int), (round(tmp_line[point_idx+1][0] * bev_w), round(tmp_line[point_idx+1][1] * bev_h)), (255, 0, 0), 2)

    for line_tuple in curb_gt:
        tmp_line = line_tuple[0]
        for point_idx in range(len(tmp_line)-1):
            x_int, y_int = round(tmp_line[point_idx][0] * bev_w), round(tmp_line[point_idx][1] * bev_h)
            cv2.circle(bev_img_gt, (x_int, y_int), 1, (255, 255, 255), -1)
            cv2.line(bev_img_gt, (x_int, y_int), (round(tmp_line[point_idx+1][0] * bev_w), round(tmp_line[point_idx+1][1] * bev_h)), (0, 0, 255), 2)
    
    bev_img = cv2.flip(bev_img, 0)
    bev_img_gt = cv2.flip(bev_img_gt, 0)


    orange_color = (0, 165, 255)  # 橙色
    cv2.rectangle(bev_img, (0, 0), (bev_w - 1, bev_h - 1), orange_color, 2)
    cv2.rectangle(bev_img_gt, (0, 0), (bev_w - 1, bev_h - 1), orange_color, 2)

    cat_img = np.concatenate([bev_img, bev_img_gt], axis=1)
    
    raw_name = json_path.split("/")[-1].split(".")[0]
    os.makedirs(local_save_dir, exist_ok=True)
    cv2.putText(cat_img, f"{raw_name}", (50, 60), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    reg_line_loss = json_data["reg_lines_loss"]
    cv2.putText(cat_img, "regloss: {:.2f}".format(reg_line_loss), (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    # loss * 100排序
    loss_100x = round(reg_line_loss*100)
    cv2.imwrite(smart_path_join(local_save_dir, "loss_{:0>4d}_frame_{}.jpg".format(loss_100x, raw_name)), cat_img)
    print(f"{json_path} ready!")
    return 


if __name__ == "__main__":
    regline_res_path = "s3://zqt/debug_loss/fasternetm_detail/20250312/top1000_regline_good.json"
    regline_dict = json.load(smart_open(regline_res_path, "r"))

    # for json_path, regline_loss in tqdm(regline_dict):
    #     plot_once(json_path)

    results = set()
    with concurrent.futures.ProcessPoolExecutor(max_workers=100) as executor:
        for json_path, regline_loss in tqdm(regline_dict):
            results.add(executor.submit(plot_once, json_path))

    global_dict = dict()
    for future in tqdm(results):
        future.result()


