'''
找到训练集前k大的loss, 保存数据集中的index, 获取对应的预测和gt
'''
import json
import numpy as np
from tqdm import tqdm
import copy
from pdb import set_trace as ste
from refile import smart_path_join, smart_listdir, smart_exists, smart_open
import concurrent.futures


def get_json_data(json_path):
    try:
        json_data = json.load(smart_open(json_path, "r"))

        reg_lines_loss = json_data["reg_lines_loss"]
        res_dict = {json_path: reg_lines_loss}
        print(json_path, reg_lines_loss)
    except:
        res_dict = None
    return res_dict


if __name__ == "__main__":
    loss_debug_folder = "s3://zqt/debug_loss/fasternetm"
    json_list = [smart_path_join(loss_debug_folder, tmp) for tmp in smart_listdir(loss_debug_folder)]
    
    # for json_path in tqdm(json_list):
    #     get_json_data(json_path)

    results = set()
    with concurrent.futures.ProcessPoolExecutor(max_workers=120) as executor:
        for json_path in tqdm(json_list):
            results.add(executor.submit(get_json_data, json_path))

    global_dict = dict()

    for future in tqdm(results):
        res_dict = future.result()
        if res_dict is not None:
            global_dict.update(res_dict)
    
    sorted_global_dict = sorted(global_dict.items(), key=lambda item: item[1], reverse=False)

    # 1w8, 前5%，约1000帧

    json.dump(sorted_global_dict[:1000], smart_open("s3://zqt/debug_loss/fasternetm_detail/20250312/top1000_regline_good.json", "w"))

    
    



