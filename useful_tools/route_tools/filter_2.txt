totally 93444 prelabel jsons
min drive radius: 12.772567114664005
ts: 1736389390.448568000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.504244086332966
ts: 1736389390.548568000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.933856668248225
ts: 1736389390.648568000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 14.107464693295281
ts: 1736389390.748568000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 14.589035459926315
ts: 1736389390.848420000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 15.766259263185729
ts: 1736389390.948420000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.716025887613014
ts: 1736389391.048420000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.861998382197847
ts: 1736389391.148420000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 19.23014213205758
ts: 1736389391.248420000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 14.639273397826248
ts: 1736389391.348420000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.142605570304758
ts: 1736389391.448420000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 10.513592145075775
ts: 1736389391.648420000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.959543661402725
ts: 1736389391.748420000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.63395152989708
ts: 1736389391.848288000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.57278724280811
ts: 1736389391.948288000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.071821854819389
ts: 1736389392.048288000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.983736717436686
ts: 1736389396.547750000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 22.092532614056868
ts: 1736389398.747483000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 18.85342251485245
ts: 1736389398.847483000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.936705406569804
ts: 1736389398.947483000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.199477763081514
ts: 1736389399.047351000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 15.186537521797431
ts: 1736389399.147351000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.3105557843392
ts: 1736389399.247351000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.633672157744673
ts: 1736389399.347351000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.715418623200572
ts: 1736389399.447351000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 14.900769084064763
ts: 1736389399.547351000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 18.318224969394503
ts: 1736389399.647351000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 23.007487492425973
ts: 1736389399.747351000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 20.2429573426817
ts: 1736389403.846820000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.499701754465086
ts: 1736389403.946820000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.474023923839988
ts: 1736389404.046821000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.471261192657447
ts: 1736389404.146683000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.900334345273192
ts: 1736389404.246683000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.923208015007562
ts: 1736389404.346683000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.380579010025805
ts: 1736389404.446683000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.042156483577177
ts: 1736389404.546683000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.150915765076956
ts: 1736389404.646683000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.176951540948108
ts: 1736389404.746683000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.404232982765816
ts: 1736389404.946683000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 23.723599631458697
ts: 1736389405.546509000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 19.193942173293145
ts: 1736389405.646510000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 19.220108664783222
ts: 1736389405.746510000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 19.423995113962196
ts: 1736389405.846510000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.200989792657747
ts: 1736389405.946510000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.67413489122905
ts: 1736389406.046510000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.01347558096957
ts: 1736389406.146510000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 15.455204680142488
ts: 1736389406.246335000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 15.204328854412713
ts: 1736389406.346336000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.665975002575383
ts: 1736389406.446336000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.780030078376456
ts: 1736389406.546336000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.612893023495129
ts: 1736389406.646336000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.728373555800882
ts: 1736389406.946336000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.137397110828537
ts: 1736389407.046336000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 15.195889859703692
ts: 1736389407.146336000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.02621699597602
ts: 1736389407.246170000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.773453998278654
ts: 1736389407.346170000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.136230053683347
ts: 1736389407.446170000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.013574081466652
ts: 1736389407.546170000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.891022787575361
ts: 1736389407.646170000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.699107876282977
ts: 1736389407.746170000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 18.078197972583162
ts: 1736389407.846170000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 19.98759032753521
ts: 1736389407.946170000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20250109_101304_det/v0_250110_202531/0019/fused_e2e_map_prelabel_result/0019.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.113214761498362
ts: 1734317238.085191000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 22.44528298624771
ts: 1734317238.185318000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 21.83532156700026
ts: 1734317243.486000000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.380915599506846
ts: 1734317243.586000000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.286121952566834
ts: 1734317243.686000000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 23.535375532071907
ts: 1734317243.786000000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 22.099194004682666
ts: 1734317244.986136000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.601994993292015
ts: 1734317245.086136000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 10.836147108730662
ts: 1734317245.186136000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 10.141470301957163
ts: 1734317247.686549000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.50129952041474
ts: 1734317247.886549000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.266788143443716
ts: 1734317247.986549000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.803733010370783
ts: 1734317248.086549000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.498974675334132
ts: 1734317248.186549000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 14.06893654660315
ts: 1734317248.286549000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.820313386272556
ts: 1734317248.386549000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.595023955745498
ts: 1734317248.486685000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.81961190543464
ts: 1734317248.586685000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.560191169170333
ts: 1734317248.686685000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.153419293261399
ts: 1734317248.786685000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.35622500736414
ts: 1734317248.886686000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.847334351729666
ts: 1734317248.986686000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 14.627089662435324
ts: 1734317249.086686000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 14.815105290623151
ts: 1734317249.186686000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.450042248498196
ts: 1734317249.286686000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.196211157916057
ts: 1734317249.386686000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 19.032371824991582
ts: 1734317249.486817000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 19.917712807306188
ts: 1734317249.586817000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 20.522867802863175
ts: 1734317249.686817000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0007/fused_e2e_map_prelabel_result/0007.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
totally 93444 prelabel jsons
totally 93444 prelabel jsons
min drive radius: 24.3449507562245
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.853757305432755
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.9161957053049
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.092504653981022
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.371802279825049
ts: 1734317186.583625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 10.141051069334491
ts: 1734317186.683625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.772456558015493
ts: 1734317188.983604000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.837570202741384
ts: 1734317189.083595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 15.675262974503275
ts: 1734317189.183595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.902023272237553
ts: 1734317189.283595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.015271492353435
ts: 1734317189.383595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.659518377419342
ts: 1734317189.483595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 18.16884437838367
ts: 1734317189.583595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.383869592166853
ts: 1734317189.683595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.58174681567601
ts: 1734317189.783595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.151160174467822
ts: 1734317189.883595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.486036319713453
ts: 1734317189.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.211792440484142
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 15.467145133203694
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 14.875571916996929
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.898408318870379
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.005106100980559
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.034270782558485
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 10.971547689645446
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
totally 93444 prelabel jsons
min drive radius: 24.3449507562245
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.853757305432755
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.9161957053049
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.092504653981022
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.371802279825049
ts: 1734317186.583625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 10.141051069334491
ts: 1734317186.683625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 11.772456558015493
ts: 1734317188.983604000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.837570202741384
ts: 1734317189.083595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 15.675262974503275
ts: 1734317189.183595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.902023272237553
ts: 1734317189.283595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.015271492353435
ts: 1734317189.383595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.659518377419342
ts: 1734317189.483595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 18.16884437838367
ts: 1734317189.583595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.383869592166853
ts: 1734317189.683595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.58174681567601
ts: 1734317189.783595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 17.151160174467822
ts: 1734317189.883595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.486036319713453
ts: 1734317189.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 16.211792440484142
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 15.467145133203694
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 14.875571916996929
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.898408318870379
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 13.005106100980559
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 12.034270782558485
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
min drive radius: 10.971547689645446
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True}
totally 93444 prelabel jsons
min drive radius: 24.3449507562245
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.853757305432755
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 12.9161957053049
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.092504653981022
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.371802279825049
ts: 1734317186.583625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 10.141051069334491
ts: 1734317186.683625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.772456558015493
ts: 1734317188.983604000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.837570202741384
ts: 1734317189.083595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.675262974503275
ts: 1734317189.183595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.902023272237553
ts: 1734317189.283595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.015271492353435
ts: 1734317189.383595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.659518377419342
ts: 1734317189.483595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 18.16884437838367
ts: 1734317189.583595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.383869592166853
ts: 1734317189.683595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.58174681567601
ts: 1734317189.783595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.151160174467822
ts: 1734317189.883595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.486036319713453
ts: 1734317189.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.211792440484142
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.467145133203694
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 14.875571916996929
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.898408318870379
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.005106100980559
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 12.034270782558485
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 10.971547689645446
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
totally 93444 prelabel jsons
> /data/Perceptron/useful_tools/route_tools/filter_turn_order2.py(281)deal_one_json()
-> if sign_changes >= 2 and np.max(np.abs(curvatures)) < 0.05:
(Pdb) array([0.00735155, 0.00752278, 0.00769874, 0.00787957, 0.00806542,
       0.00825643, 0.00845275, 0.00865452, 0.00886191, 0.00907506,
       0.00929414, 0.00951931, 0.00975074, 0.00998858, 0.01023303,
       0.01048423, 0.01074238, 0.01100765, 0.0112802 , 0.01156023,
       0.01184791, 0.01214341, 0.01244692, 0.01275861, 0.01307867,
       0.01340725, 0.01374453, 0.01409069, 0.01444586, 0.01481023,
       0.01518392, 0.01556709, 0.01595986, 0.01636235, 0.01677467,
       0.01719691, 0.01762916, 0.01807148, 0.0185239 , 0.01898645,
       0.01945914, 0.01994192, 0.02043476, 0.02093756, 0.02145021,
       0.02197256, 0.02250442, 0.02304557, 0.02359573, 0.02415459,
       0.02472179, 0.02529691, 0.02587948, 0.02646898, 0.02706484,
       0.02766641, 0.02827299, 0.02888382, 0.02949806, 0.03011482,
       0.03073314, 0.03135199, 0.03197028, 0.03258685, 0.03320047,
       0.03380988, 0.03441372, 0.0350106 , 0.03559908, 0.03617768,
       0.03674486, 0.03729907, 0.03783873, 0.03836225, 0.03886801,
       0.03935442, 0.0398199 , 0.04026288, 0.04068183, 0.04107528])
(Pdb) totally 93444 prelabel jsons
> /data/Perceptron/useful_tools/route_tools/filter_turn_order2.py(282)deal_one_json()
-> if sign_changes >= 2 and np.max(np.abs(curvatures)) < 0.05:
(Pdb) 
totally 93444 prelabel jsons
min drive radius: 24.3449507562245
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.853757305432755
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 12.9161957053049
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.092504653981022
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.371802279825049
ts: 1734317186.583625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 10.141051069334491
ts: 1734317186.683625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.772456558015493
ts: 1734317188.983604000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.837570202741384
ts: 1734317189.083595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.675262974503275
ts: 1734317189.183595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.902023272237553
ts: 1734317189.283595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.015271492353435
ts: 1734317189.383595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.659518377419342
ts: 1734317189.483595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 18.16884437838367
ts: 1734317189.583595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.383869592166853
ts: 1734317189.683595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.58174681567601
ts: 1734317189.783595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.151160174467822
ts: 1734317189.883595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.486036319713453
ts: 1734317189.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.211792440484142
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.467145133203694
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 14.875571916996929
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.898408318870379
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.005106100980559
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
> /data/Perceptron/useful_tools/route_tools/filter_turn_order2.py(301)deal_one_json()
-> return
(Pdb) ['1734317168.083790000', '1734317168.183790000', '1734317168.283790000', '1734317168.383790000', '1734317168.483790000', '1734317168.583780000', '1734317168.683780000', '1734317168.783780000', '1734317168.883780000', '1734317168.983780000', '1734317169.083780000', '1734317169.183780000', '1734317169.283780000', '1734317169.383780000', '1734317169.483780000', '1734317169.583771000', '1734317169.683771000', '1734317169.783771000', '1734317169.883771000', '1734317169.983771000', '1734317170.083771000', '1734317170.183771000', '1734317170.283771000', '1734317170.383771000', '1734317170.483771000', '1734317170.583771000', '1734317170.683761000', '1734317170.783761000', '1734317170.883761000', '1734317170.983761000', '1734317171.083761000', '1734317171.183761000', '1734317171.283761000', '1734317171.383761000', '1734317171.483761000', '1734317171.583761000', '1734317171.683752000', '1734317171.783752000', '1734317171.883752000', '1734317171.983752000', '1734317172.083752000', '1734317172.183752000', '1734317172.283752000', '1734317172.383752000', '1734317172.483752000', '1734317172.583752000', '1734317172.683743000', '1734317172.783743000', '1734317172.883743000', '1734317172.983743000', '1734317173.083743000', '1734317173.183743000', '1734317173.283743000', '1734317173.383743000', '1734317173.483743000', '1734317173.583743000', '1734317173.683733000', '1734317173.783733000', '1734317173.883733000', '1734317173.983733000', '1734317174.083733000', '1734317174.183733000', '1734317174.283734000', '1734317174.383734000', '1734317174.483734000', '1734317174.583734000', '1734317174.683734000', '1734317174.783724000', '1734317174.883724000', '1734317174.983724000', '1734317175.083724000', '1734317175.183724000', '1734317175.283724000', '1734317175.383724000', '1734317175.483724000', '1734317175.583724000', '1734317175.683724000', '1734317175.783715000', '1734317175.883715000', '1734317175.983715000', '1734317176.083715000', '1734317176.183715000', '1734317176.283715000', '1734317176.383715000', '1734317176.483716000', '1734317176.583716000', '1734317176.683716000', '1734317176.783706000', '1734317176.883706000', '1734317176.983706000', '1734317177.083706000', '1734317177.183706000', '1734317177.283706000', '1734317177.383706000', '1734317177.483706000', '1734317177.583706000', '1734317177.683704000', '1734317177.783697000', '1734317177.883697000', '1734317177.983697000', '1734317178.083697000', '1734317178.183697000', '1734317178.283697000', '1734317178.383697000', '1734317178.483697000', '1734317178.583697000', '1734317178.683697000', '1734317178.783697000', '1734317178.883687000', '1734317178.983687000', '1734317179.083687000', '1734317179.183687000', '1734317179.283687000', '1734317179.383688000', '1734317179.483687000', '1734317179.583687000', '1734317179.683688000', '1734317179.783688000', '1734317179.883678000', '1734317179.983678000', '1734317180.083678000', '1734317180.183678000', '1734317180.283678000', '1734317180.383678000', '1734317180.483678000', '1734317180.583678000', '1734317180.683678000', '1734317180.783678000', '1734317180.883668000', '1734317180.983669000', '1734317181.083669000', '1734317181.183669000', '1734317181.283669000', '1734317181.383669000', '1734317181.483669000', '1734317181.583669000', '1734317181.683669000', '1734317181.783669000', '1734317181.883660000', '1734317181.983660000', '1734317182.083660000', '1734317182.183660000', '1734317182.283660000', '1734317182.383660000', '1734317182.483660000', '1734317182.583660000', '1734317182.683660000', '1734317182.783660000', '1734317182.883651000', '1734317182.983651000', '1734317183.083651000', '1734317183.183651000', '1734317183.283651000', '1734317183.383651000', '1734317183.483651000', '1734317183.583651000', '1734317183.683651000', '1734317183.783651000', '1734317183.883651000', '1734317183.983642000', '1734317184.083641000', '1734317184.183642000', '1734317184.283642000', '1734317184.383642000', '1734317184.483642000', '1734317184.583642000', '1734317184.683642000', '1734317184.783642000', '1734317184.883642000', '1734317184.983642000', '1734317185.083642000', '1734317185.183642000', '1734317185.283642000', '1734317185.383642000', '1734317185.483642000', '1734317185.583642000', '1734317185.683642000', '1734317185.783642000', '1734317185.883642000', '1734317185.983625000', '1734317186.083625000', '1734317186.*********', '1734317186.*********', '1734317186.*********', '1734317186.*********', '1734317186.583625000', '1734317186.683625000', '1734317186.783625000', '1734317186.883625000', '1734317186.983613000', '1734317187.083613000', '1734317187.183613000', '1734317187.283613000', '1734317187.383613000', '1734317187.483613000', '1734317187.583613000', '1734317187.683613000', '1734317187.783613000', '1734317187.883613000', '1734317187.983613000', '1734317188.083604000', '1734317188.183604000', '1734317188.283604000', '1734317188.383604000', '1734317188.483604000', '1734317188.583604000', '1734317188.683604000', '1734317188.783604000', '1734317188.883604000', '1734317188.983604000', '1734317189.083595000', '1734317189.183595000', '1734317189.283595000', '1734317189.383595000', '1734317189.483595000', '1734317189.583595000', '1734317189.683595000', '1734317189.783595000', '1734317189.883595000', '1734317189.*********', '1734317190.*********', '1734317190.*********', '1734317190.*********', '1734317190.*********', '1734317190.*********', '1734317190.*********', '1734317190.*********', '1734317190.*********', '1734317190.*********', '1734317190.*********', '1734317191.*********', '1734317191.*********', '1734317191.*********', '1734317191.*********', '1734317191.*********', '1734317191.*********', '1734317191.*********', '1734317191.*********', '1734317191.*********', '1734317191.*********']
(Pdb) *** SyntaxError: invalid syntax
(Pdb) *** SyntaxError: invalid syntax
(Pdb) totally 93444 prelabel jsons
min drive radius: 24.3449507562245
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.853757305432755
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 12.9161957053049
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.092504653981022
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.371802279825049
ts: 1734317186.583625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 10.141051069334491
ts: 1734317186.683625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.772456558015493
ts: 1734317188.983604000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.837570202741384
ts: 1734317189.083595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.675262974503275
ts: 1734317189.183595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.902023272237553
ts: 1734317189.283595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.015271492353435
ts: 1734317189.383595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.659518377419342
ts: 1734317189.483595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 18.16884437838367
ts: 1734317189.583595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.383869592166853
ts: 1734317189.683595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.58174681567601
ts: 1734317189.783595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.151160174467822
ts: 1734317189.883595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.486036319713453
ts: 1734317189.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.211792440484142
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.467145133203694
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 14.875571916996929
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.898408318870379
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.005106100980559
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
> /data/Perceptron/useful_tools/route_tools/filter_turn_order2.py(301)deal_one_json()
-> return
(Pdb) totally 93444 prelabel jsons
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
min drive radius: 24.3449507562245
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
182
min drive radius: 17.853757305432755
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
183
min drive radius: 12.9161957053049
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
184
min drive radius: 13.092504653981022
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
185
min drive radius: 11.371802279825049
ts: 1734317186.583625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
186
min drive radius: 10.141051069334491
ts: 1734317186.683625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
min drive radius: 11.772456558015493
ts: 1734317188.983604000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
210
min drive radius: 13.837570202741384
ts: 1734317189.083595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
211
min drive radius: 15.675262974503275
ts: 1734317189.183595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
212
min drive radius: 16.902023272237553
ts: 1734317189.283595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
213
min drive radius: 16.015271492353435
ts: 1734317189.383595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
214
min drive radius: 17.659518377419342
ts: 1734317189.483595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
215
min drive radius: 18.16884437838367
ts: 1734317189.583595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
216
min drive radius: 17.383869592166853
ts: 1734317189.683595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
217
min drive radius: 17.58174681567601
ts: 1734317189.783595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
218
min drive radius: 17.151160174467822
ts: 1734317189.883595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
219
min drive radius: 16.486036319713453
ts: 1734317189.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
220
min drive radius: 16.211792440484142
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
221
min drive radius: 15.467145133203694
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
222
min drive radius: 14.875571916996929
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
223
min drive radius: 13.898408318870379
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
224
min drive radius: 13.005106100980559
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
> /data/Perceptron/useful_tools/route_tools/filter_turn_order2.py(302)deal_one_json()
-> return
(Pdb) 
totally 93444 prelabel jsons
min drive radius: 24.3449507562245
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.853757305432755
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 12.9161957053049
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.092504653981022
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.371802279825049
ts: 1734317186.583625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 10.141051069334491
ts: 1734317186.683625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.772456558015493
ts: 1734317188.983604000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.837570202741384
ts: 1734317189.083595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.675262974503275
ts: 1734317189.183595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.902023272237553
ts: 1734317189.283595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.015271492353435
ts: 1734317189.383595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.659518377419342
ts: 1734317189.483595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 18.16884437838367
ts: 1734317189.583595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.383869592166853
ts: 1734317189.683595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.58174681567601
ts: 1734317189.783595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.151160174467822
ts: 1734317189.883595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.486036319713453
ts: 1734317189.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.211792440484142
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.467145133203694
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 14.875571916996929
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.898408318870379
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.005106100980559
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
240
> /data/Perceptron/useful_tools/route_tools/filter_turn_order2.py(302)deal_one_json()
-> return
(Pdb) totally 93444 prelabel jsons
min drive radius: 24.3449507562245
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.853757305432755
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 12.9161957053049
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.092504653981022
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.371802279825049
ts: 1734317186.583625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 10.141051069334491
ts: 1734317186.683625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.772456558015493
ts: 1734317188.983604000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.837570202741384
ts: 1734317189.083595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.675262974503275
ts: 1734317189.183595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.902023272237553
ts: 1734317189.283595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.015271492353435
ts: 1734317189.383595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.659518377419342
ts: 1734317189.483595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 18.16884437838367
ts: 1734317189.583595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.383869592166853
ts: 1734317189.683595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.58174681567601
ts: 1734317189.783595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.151160174467822
ts: 1734317189.883595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.486036319713453
ts: 1734317189.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.211792440484142
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.467145133203694
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 14.875571916996929
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.898408318870379
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.005106100980559
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
240
> /data/Perceptron/useful_tools/route_tools/filter_turn_order2.py(302)deal_one_json()
-> return
(Pdb) totally 93444 prelabel jsons
min drive radius: 24.3449507562245
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.853757305432755
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 12.9161957053049
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.092504653981022
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.371802279825049
ts: 1734317186.583625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 10.141051069334491
ts: 1734317186.683625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.772456558015493
ts: 1734317188.983604000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.837570202741384
ts: 1734317189.083595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.675262974503275
ts: 1734317189.183595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.902023272237553
ts: 1734317189.283595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.015271492353435
ts: 1734317189.383595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.659518377419342
ts: 1734317189.483595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 18.16884437838367
ts: 1734317189.583595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.383869592166853
ts: 1734317189.683595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.58174681567601
ts: 1734317189.783595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.151160174467822
ts: 1734317189.883595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.486036319713453
ts: 1734317189.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.211792440484142
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.467145133203694
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 14.875571916996929
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.898408318870379
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.005106100980559
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
240
> /data/Perceptron/useful_tools/route_tools/filter_turn_order2.py(302)deal_one_json()
-> return
(Pdb) totally 93444 prelabel jsons
min drive radius: 24.3449507562245
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.853757305432755
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 12.9161957053049
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.092504653981022
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.371802279825049
ts: 1734317186.583625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 10.141051069334491
ts: 1734317186.683625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.772456558015493
ts: 1734317188.983604000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.837570202741384
ts: 1734317189.083595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.675262974503275
ts: 1734317189.183595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.902023272237553
ts: 1734317189.283595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.015271492353435
ts: 1734317189.383595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.659518377419342
ts: 1734317189.483595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 18.16884437838367
ts: 1734317189.583595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.383869592166853
ts: 1734317189.683595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.58174681567601
ts: 1734317189.783595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.151160174467822
ts: 1734317189.883595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.486036319713453
ts: 1734317189.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.211792440484142
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.467145133203694
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 14.875571916996929
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.898408318870379
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.005106100980559
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
240
> /data/Perceptron/useful_tools/route_tools/filter_turn_order2.py(302)deal_one_json()
-> return
(Pdb) 
totally 93444 prelabel jsons
min drive radius: 24.3449507562245
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.853757305432755
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 12.9161957053049
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.092504653981022
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.371802279825049
ts: 1734317186.583625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 10.141051069334491
ts: 1734317186.683625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.772456558015493
ts: 1734317188.983604000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.837570202741384
ts: 1734317189.083595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.675262974503275
ts: 1734317189.183595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.902023272237553
ts: 1734317189.283595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.015271492353435
ts: 1734317189.383595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.659518377419342
ts: 1734317189.483595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 18.16884437838367
ts: 1734317189.583595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.383869592166853
ts: 1734317189.683595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.58174681567601
ts: 1734317189.783595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.151160174467822
ts: 1734317189.883595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.486036319713453
ts: 1734317189.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.211792440484142
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.467145133203694
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 14.875571916996929
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.898408318870379
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.005106100980559
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
240
> /data/Perceptron/useful_tools/route_tools/filter_turn_order2.py(302)deal_one_json()
-> return
(Pdb) array([[ 0.00000000e+00,  0.00000000e+00, -3.30000000e-01],
       [-4.11087198e-02, -8.01798905e-02, -3.28233721e-01],
       [-9.39779711e-02, -1.57314789e-01, -3.26949431e-01],
       [-1.58607754e-01, -2.31404696e-01, -3.26147129e-01],
       [-2.34998068e-01, -3.02449611e-01, -3.25826815e-01],
       [-3.23148914e-01, -3.70449535e-01, -3.25988489e-01],
       [-4.23060292e-01, -4.35404466e-01, -3.26632151e-01],
       [-5.34732201e-01, -4.97314406e-01, -3.27757802e-01],
       [-6.58164641e-01, -5.56179354e-01, -3.29365441e-01],
       [-7.93357613e-01, -6.11999310e-01, -3.31455068e-01],
       [-9.40311117e-01, -6.64774275e-01, -3.34026683e-01],
       [-1.09902515e+00, -7.14504247e-01, -3.37080286e-01],
       [-1.26949972e+00, -7.61189228e-01, -3.40615878e-01],
       [-1.45173482e+00, -8.04829217e-01, -3.44633458e-01],
       [-1.64573045e+00, -8.45424215e-01, -3.49133026e-01],
       [-1.85148661e+00, -8.82974220e-01, -3.54114582e-01],
       [-2.06900330e+00, -9.17479234e-01, -3.59578126e-01],
       [-2.29828053e+00, -9.48939256e-01, -3.65523659e-01],
       [-2.53931828e+00, -9.77354286e-01, -3.71951180e-01],
       [-2.79211657e+00, -1.00272432e+00, -3.78860689e-01],
       [-3.05667539e+00, -1.02504937e+00, -3.86252186e-01],
       [-3.33299474e+00, -1.04432943e+00, -3.94125671e-01],
       [-3.62107462e+00, -1.06056449e+00, -4.02481145e-01],
       [-3.92091503e+00, -1.07375456e+00, -4.11318607e-01],
       [-4.23251598e+00, -1.08389964e+00, -4.20638057e-01],
       [-4.55587746e+00, -1.09099973e+00, -4.30439495e-01],
       [-4.89099947e+00, -1.09505482e+00, -4.40722921e-01],
       [-5.23788200e+00, -1.09606493e+00, -4.51488336e-01],
       [-5.59652508e+00, -1.09403004e+00, -4.62735739e-01],
       [-5.96692868e+00, -1.08895016e+00, -4.74465130e-01],
       [-6.34909281e+00, -1.08082529e+00, -4.86676509e-01],
       [-6.74301748e+00, -1.06965543e+00, -4.99369877e-01],
       [-7.14870268e+00, -1.05544057e+00, -5.12545232e-01],
       [-7.56614841e+00, -1.03818073e+00, -5.26202576e-01],
       [-7.99535467e+00, -1.01787589e+00, -5.40341908e-01],
       [-8.43632146e+00, -9.94526057e-01, -5.54963228e-01],
       [-8.88904878e+00, -9.68131235e-01, -5.70066537e-01],
       [-9.35353664e+00, -9.38691422e-01, -5.85651834e-01],
       [-9.82978503e+00, -9.06206616e-01, -6.01719118e-01],
       [-1.03177939e+01, -8.70676819e-01, -6.18268391e-01],
       [-1.08175634e+01, -8.32102030e-01, -6.35299653e-01],
       [-1.13290934e+01, -7.90482249e-01, -6.52812902e-01],
       [-1.18523839e+01, -7.45817477e-01, -6.70808140e-01],
       [-1.23874349e+01, -6.98107712e-01, -6.89285366e-01],
       [-1.29342465e+01, -6.47352956e-01, -7.08244580e-01],
       [-1.34928186e+01, -5.93553208e-01, -7.27685782e-01],
       [-1.40631513e+01, -5.36708469e-01, -7.47608973e-01],
       [-1.46452444e+01, -4.76818737e-01, -7.68014151e-01],
       [-1.52390981e+01, -4.13884014e-01, -7.88901318e-01],
       [-1.58447124e+01, -3.47904299e-01, -8.10270473e-01],
       [-1.64620871e+01, -2.78879592e-01, -8.32121617e-01],
       [-1.70912224e+01, -2.06809893e-01, -8.54454748e-01],
       [-1.77321183e+01, -1.31695203e-01, -8.77269868e-01],
       [-1.83847746e+01, -5.35355209e-02, -9.00566976e-01],
       [-1.90491915e+01,  2.76691530e-02, -9.24346072e-01],
       [-1.97253689e+01,  1.11918819e-01, -9.48607156e-01],
       [-2.04133069e+01,  1.99213476e-01, -9.73350229e-01],
       [-2.11130054e+01,  2.89553126e-01, -9.98575289e-01],
       [-2.18244644e+01,  3.82937767e-01, -1.02428234e+00],
       [-2.25476839e+01,  4.79367399e-01, -1.05047138e+00],
       [-2.32826640e+01,  5.78842024e-01, -1.07714240e+00],
       [-2.40294046e+01,  6.81361640e-01, -1.10429541e+00],
       [-2.47879058e+01,  7.86926249e-01, -1.13193042e+00],
       [-2.55581674e+01,  8.95535849e-01, -1.16004741e+00],
       [-2.63401897e+01,  1.00719044e+00, -1.18864638e+00],
       [-2.71339724e+01,  1.12189002e+00, -1.21772735e+00],
       [-2.79395157e+01,  1.23963460e+00, -1.24729031e+00],
       [-2.87568195e+01,  1.36042417e+00, -1.27733525e+00],
       [-2.95858838e+01,  1.48425873e+00, -1.30786218e+00],
       [-3.04267087e+01,  1.61113828e+00, -1.33887110e+00],
       [-3.12792941e+01,  1.74106282e+00, -1.37036201e+00],
       [-3.21436400e+01,  1.87403235e+00, -1.40233490e+00],
       [-3.30197464e+01,  2.01004688e+00, -1.43478978e+00],
       [-3.39076134e+01,  2.14910640e+00, -1.46772666e+00],
       [-3.48072410e+01,  2.29121091e+00, -1.50114552e+00],
       [-3.57186290e+01,  2.43636041e+00, -1.53504637e+00],
       [-3.66417776e+01,  2.58455490e+00, -1.56942920e+00],
       [-3.75766867e+01,  2.73579439e+00, -1.60429403e+00],
       [-3.85233564e+01,  2.89007886e+00, -1.63964084e+00],
       [-3.94817865e+01,  3.04740833e+00, -1.67546964e+00]])
(Pdb) totally 93444 prelabel jsons
min drive radius: 24.3449507562245
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.853757305432755
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 12.9161957053049
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.092504653981022
ts: 1734317186.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.371802279825049
ts: 1734317186.583625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 10.141051069334491
ts: 1734317186.683625000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 11.772456558015493
ts: 1734317188.983604000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.837570202741384
ts: 1734317189.083595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.675262974503275
ts: 1734317189.183595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.902023272237553
ts: 1734317189.283595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.015271492353435
ts: 1734317189.383595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.659518377419342
ts: 1734317189.483595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 18.16884437838367
ts: 1734317189.583595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.383869592166853
ts: 1734317189.683595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.58174681567601
ts: 1734317189.783595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 17.151160174467822
ts: 1734317189.883595000 , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.486036319713453
ts: 1734317189.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 16.211792440484142
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 15.467145133203694
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 14.875571916996929
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.898408318870379
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
min drive radius: 13.005106100980559
ts: 1734317190.********* , json s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json find! {'uturn': False, 'leftturn': False, 'rightturn': True, 'lane_change': False}
240
> /data/Perceptron/useful_tools/route_tools/filter_turn_order2.py(302)deal_one_json()
-> return
(Pdb) array([[  0.        ,   0.        ,   0.        ],
       [ -0.46766561,  -0.59824454,   0.21202193],
       [ -0.99157375,  -0.9429485 ,   0.58874902],
       [ -1.5281735 ,  -1.17581284,   0.65951461],
       [ -2.1243016 ,  -1.35527446,   0.41853646],
       [ -2.69663656,  -1.48643074,   0.37908867],
       [ -3.28386654,  -1.8143211 ,   0.44210938],
       [ -3.89485531,  -1.85630662,   0.05137248],
       [ -4.52514769,  -1.92615515,  -0.23141689],
       [ -5.08332131,  -2.00202891,  -0.31073077],
       [ -5.71449635,  -2.05803632,  -0.48987996],
       [ -6.28118554,  -2.364108  ,  -0.61229922],
       [ -6.91265063,  -2.40473802,  -0.6175712 ],
       [ -7.52659351,  -2.41462806,  -0.85936996],
       [ -8.17065338,  -2.43123277,  -1.1336631 ],
       [ -8.75898094,  -2.41056572,  -1.28996177],
       [ -9.38877828,  -2.60346281,  -1.23726287],
       [-10.00863187,  -2.56630474,  -1.10593743],
       [-10.6646443 ,  -2.51018662,  -0.90849572],
       [-11.28698844,  -2.35792616,  -0.82007249],
       [-11.96404772,  -2.39017666,  -0.744288  ],
       [-12.65557277,  -2.11158202,  -0.70352897],
       [-13.39509666,  -1.77262651,  -0.65643698],
       [-14.06481007,  -1.45291593,  -0.68893728],
       [-14.78044165,  -1.35153788,  -0.73162984],
       [-15.481198  ,  -1.02221781,  -0.7272697 ],
       [-16.22269589,  -0.65430067,  -0.71695157],
       [-16.89405095,  -0.30449296,  -0.74107002],
       [-17.61292619,  -0.16977525,  -0.70156796],
       [-18.31668271,   0.19232521,  -0.74230796],
       [-19.05472705,   0.57463373,  -0.73753128],
       [-19.71891133,   0.91489748,  -0.87042123],
       [-20.43652642,   1.04790238,  -1.01686946],
       [-21.10593099,   1.42900704,  -1.03459865],
       [-21.84875724,   1.86106465,  -1.00656187],
       [-22.52990618,   1.99658777,  -1.03302772],
       [-23.24802108,   2.34480757,  -0.96963786],
       [-23.86508   ,   2.53802821,  -0.97295405],
       [-24.50304055,   2.35964325,  -1.16162141],
       [-25.06936502,   2.33781403,  -1.25767824],
       [-25.69916122,   2.31997062,  -1.30818506],
       [-26.28104489,   2.05331103,  -1.31579894],
       [-26.9059492 ,   2.01913938,  -1.30628736],
       [-27.46968278,   1.99459574,  -1.28771069],
       [-28.07945032,   1.71509898,  -1.24931597],
       [-28.67806354,   1.70295247,  -1.24840042],
       [-29.30390102,   1.67226339,  -1.24858408],
       [-29.8861844 ,   1.40299043,  -1.31936676],
       [-30.51637231,   1.38970286,  -1.334688  ],
       [-31.11295488,   1.36854609,  -1.34753758],
       [-31.72161511,   1.08269369,  -1.32976635],
       [-32.31834109,   1.06660407,  -1.27266829],
       [-32.93200241,   0.80399175,  -1.2183031 ],
       [-33.52771736,   0.78337179,  -1.16889916],
       [-34.15769585,   0.77070553,  -1.16818305],
       [-34.7365845 ,   0.48605918,  -1.24835215],
       [-35.36309812,   0.44397546,  -1.49256544],
       [-35.96055369,   0.42247388,  -1.58633176],
       [-36.57780868,   0.16726245,  -1.66519354],
       [-37.18208471,   0.17731749,  -1.72065066]])
(Pdb) 