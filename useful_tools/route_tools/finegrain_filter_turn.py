'''
粗筛出来很多只有变道, 并不包含真正的左右转
尝试卡更严格的转弯半径进行筛选
'''


import numpy as np
from pdb import set_trace as ste
import os
import os.path as osp
import cv2
from tqdm import tqdm
from pdb import set_trace as ste
from refile import smart_open, smart_exists, smart_path_join, smart_listdir
import argparse
import json
import quaternion
import math
from scipy.spatial.transform import Rotation as R
from bezier_utils_3d import BezierCurve3D
import copy

def detect_lane_change(curvatures, threshold=0.04):
    """
    通过曲率符号变化检测变道
    """
    sign_changes = np.sum(np.diff(np.sign(curvatures)) != 0)
    max_curvature = np.max(np.abs(curvatures))
    return sign_changes >= 2 and max_curvature < threshold

def cubic_sample_curvatures_vectorized(num_samples, P0, P1, P2, P3):
    t = np.linspace(0, 1, num_samples)[:, np.newaxis]
    
    # 向量化导数计算
    B_prime = 3 * (
        (1-t)**2 * (P1 - P0) 
        + 2*t*(1-t)*(P2 - P1)
        + t**2 * (P3 - P2)
    )
    
    B_double_prime = 6 * (
        (1-t)*(P2 - 2*P1 + P0)
        + t*(P3 - 2*P2 + P1)
    )
    
    # 向量化叉积和模长
    cross = np.abs(B_prime[:,0] * B_double_prime[:,1] - B_prime[:,1] * B_double_prime[:,0])
    speed = np.linalg.norm(B_prime, axis=1)
    
    # 处理除零
    speed = np.where(speed < 1e-6, np.inf, speed)
    return cross / (speed ** 3)

def get_rt(extrinsic):
    param = extrinsic
    if "transform" in param:
        rota = param["transform"]["rotation"]
        tran = param["transform"]["translation"]
    else:
        rota = param["rotation"]
        tran = param["translation"]
    q = np.quaternion(rota["w"], rota["x"], rota["y"], rota["z"])
    t = np.array([tran["x"], tran["y"], tran["z"]]).astype(np.float32)
    r = quaternion.as_rotation_matrix(q).astype(np.float32)
    rt = np.eye(4)
    rt[:3, :3] = r
    rt[:3, 3] = t
    return rt

def get_dist(pt_1, pt_2):
    """
    计算点距离
    """
    return math.sqrt(math.pow(pt_1[0] - pt_2[0], 2) + math.pow(pt_1[1] - pt_2[1], 2))

def get_carpose_matrix(input_odom_data):
    if "localization" in input_odom_data:
        odom_data = input_odom_data["localization"]
    trans = odom_data["position"]  # dict
    quan = odom_data["orientation"]  # dict

    trans = np.array([trans["x"], trans["y"], trans["z"]], dtype=np.float32)
    quan = np.array([quan["x"], quan["y"], quan["z"], quan["w"]], dtype=np.float32)
    rot_mat = R.from_quat(quan).as_matrix()

    rt_mat = np.eye(4)
    rt_mat[:3, :3] = rot_mat
    rt_mat[:3, 3] = trans
    return rt_mat


def resample_route_points(tmp_ego_trajs_3d, resample_points_num=80):
    '''
    对3d轨迹点进行3阶bezier拟合, 并重新生成采样点.
    训练时根据采样点的变换进行gt的重新生成
    '''
    z_bias = 0.33
    cand_3d_points = np.array(tmp_ego_trajs_3d) + z_bias
    my_bz = BezierCurve3D(order=3, num_sample_points=resample_points_num)  # 三阶, 80个重采样点
    # 先进行3d点的归一化
    eps_z = 0.5
    norm_x = 30  # todo:hardcode
    norm_y = 25  # todo: hardcode
    norm_z = max(np.max(np.abs(cand_3d_points[:, 2])), eps_z)  # todo: hardcode, 避免归一化系数太小
    normalized_cand_3d_points = copy.deepcopy(cand_3d_points)
    normalized_cand_3d_points[:, 0] = normalized_cand_3d_points[:, 0] / norm_x
    normalized_cand_3d_points[:, 1] = normalized_cand_3d_points[:, 1] / norm_y
    normalized_cand_3d_points[:, 2] = normalized_cand_3d_points[:, 2] / norm_z
    # 获取归一化贝塞尔曲线的控制点以及平均拟合误差
    # control_points, delta_mean, delta_max = my_bz.get_control_points(normalized_cand_3d_points[:, 0], normalized_cand_3d_points[:, 1], normalized_cand_3d_points[:, 2])
    control_points, delta_mean, delta_max = my_bz.get_control_points_special(normalized_cand_3d_points[:, 0], normalized_cand_3d_points[:, 1], normalized_cand_3d_points[:, 2])
    resample_points = my_bz.get_sample_point(n=resample_points_num)

    # 采样点/控制点/拟合误差反归一化
    scaled_resample_points = copy.deepcopy(resample_points)
    scaled_resample_points[:, 0] = resample_points[:, 0] * norm_x
    scaled_resample_points[:, 1] = resample_points[:, 1] * norm_y
    scaled_resample_points[:, 2] = resample_points[:, 2] * norm_z - z_bias
    scaled_control_points = copy.deepcopy(control_points)
    scaled_control_points[:, 0] = control_points[:, 0] * norm_x
    scaled_control_points[:, 1] = control_points[:, 1] * norm_y
    scaled_control_points[:, 2] = control_points[:, 2] * norm_z - z_bias
    scaled_delta_mean = [delta_mean[0] * norm_x, delta_mean[1] * norm_y, delta_mean[2] * norm_z]
    scaled_delta_max = [delta_max[0] * norm_x, delta_max[1] * norm_y, delta_max[2] * norm_z]
    return scaled_resample_points, scaled_control_points, scaled_delta_mean, scaled_delta_max


def deal_one_json(json_path, task_id):
    '''
    找到当前帧对应的所有转弯片段
    '''
    # param
    len_thres = 40  # 40m
    frame_interv_thres = 10  # 大于10帧才做贝塞尔拟合
    wide_thres = 6  # 变道横向位移小于6m

    json_data = json.load(smart_open(json_path, "r"))
    # 先过一步场景粗筛
    roi = False
    for tag in json_data["scene_tags"]:
        if "路口" in tag and "右转" in tag:
            roi = True
        if "路口" in tag and "左转" in tag:
            roi = True
    if not roi:
        return []

    calib = json_data["calibrated_sensors"]
    lidar_ego_rt = get_rt(calib["lidar_ego"]["extrinsic"])
    ego2lidar = np.linalg.inv(lidar_ego_rt)
    ins2ego = get_rt(calib["gnss"]["gnss_ego"])
    ins2lidar = ego2lidar @ ins2ego
    
    ego2ins = np.linalg.inv(ins2ego)
    frames = json_data["frames"]

    # 获取当前clip的总长度
    start_frame = frames[0]
    ins_data = start_frame["ins_data"]
    start_ins2world = get_carpose_matrix(ins_data)
    start_world2ins = np.linalg.inv(start_ins2world)
    homo_start_pt = np.array([0, 0, 0, 1])
    homo_world_start_pt = (start_ins2world @ homo_start_pt.T).T

    # 将所有帧全部转换至第0帧的ins坐标下, startins坐标
    trajs = []
    for frame in tqdm(frames):
        ins_data = frame["ins_data"]
        tmp_ins2world = get_carpose_matrix(ins_data)
        tmp_world2ins = np.linalg.inv(tmp_ins2world)
        homo_ins_tmp_pt = (tmp_world2ins @ homo_world_start_pt.T).T
        trajs.append(-homo_ins_tmp_pt)
    trajs_np = np.array(trajs)[:, :3]  # 全局轨迹点
    
    # 计算轨迹的前缀和
    prefix_traj = np.zeros((len(trajs)))
    tmp_sum = 0
    for frame_id in range(1, len(trajs)):
        tmp_piece_len = get_dist(trajs[frame_id-1], trajs[frame_id])  # 计算相邻帧的轨迹长度
        tmp_sum += tmp_piece_len
        prefix_traj[frame_id] = tmp_sum

    valid_ts = []
    valid_pos = np.zeros(len(frames))
    save_dict = dict()
    for frame_id, frame in enumerate(tqdm(frames)):
        cam120_ts = frame["sensor_data"]["cam_front_120"]["timestamp"]
        ins_data = frame["ins_data"]
        ref_ins2world = get_carpose_matrix(ins_data)

        # 找到当前帧对应的结束点
        frame["start_pos"] = frame_id
        frame["traj_valid"] = False
        end_pos = frame_id
        for i in range(frame_id+1, len(frames)):
            end_pos = i
            trajlen_cursum = prefix_traj[i] - prefix_traj[frame_id]
            if trajlen_cursum > len_thres:
                break
        if trajlen_cursum > len_thres and end_pos - frame_id > frame_interv_thres:
            frame["traj_valid"] = True  # 轨迹够长
            frame["traj_time_length"] = end_pos - frame_id

        if not frame["traj_valid"]:
            # 非法轨迹, 太短
            continue
        
        frame["end_pos"] = end_pos
        # 找到当前帧对应的未来40m的轨迹(start-ins坐标系)
        startins_pts = trajs_np[frame_id: end_pos]
        # 将start-ins坐标系转换至world坐标系再转回当前ins坐标系
        homo_startins_pts = np.concatenate([startins_pts, np.ones((len(startins_pts), 1))], axis=1)
        homo_world_pts = (start_ins2world @ homo_startins_pts.T).T
        homo_tmpins_pts = (np.linalg.inv(ref_ins2world) @ homo_world_pts.T).T
        # 将ins坐标系转回当前ego坐标系
        tmpego_pts = (ins2ego @ homo_tmpins_pts.T).T
        tmpego_pts = tmpego_pts - tmpego_pts[0]  # 转为当前ego坐标系下的轨迹
        tmp_ego_trajs_3d = tmpego_pts[:, :3]
        
        # 三阶3d-bezier拟合
        resample_points, control_points, delta_mean, delta_max = resample_route_points(tmp_ego_trajs_3d, resample_points_num=80)
        
        # 只取xy进行曲率计算
        control_points_xy = control_points[:, :2]
        
        curvatures = cubic_sample_curvatures_vectorized(
            80, 
            control_points_xy[0], 
            control_points_xy[1],
            control_points_xy[2],
            control_points_xy[3]
        )
        # 剔除变道
        curvature_diff = np.diff(curvatures)
        sign_changes = np.sum(np.diff(np.sign(curvatures)) != 0)

        drive_radius = 1 / (abs(curvatures) + 1e-6)
        min_drive_radius = np.min(drive_radius)

        drive_tag = dict(
            uturn=False,
            leftturn=False,  # 左转
            rightturn=False, # 右转
            lane_change=False  # 新增变道标签
        )
        frame["drive_tag"] = drive_tag

        # 转弯半径在10m以内, 判定为掉头tag
        if min_drive_radius < 10:
            frame["drive_tag"]["uturn"] = True

        # 最小转弯半径存在10-25m, 判定为左右转
        if min_drive_radius > 10 and min_drive_radius < 25:
            # 变道检测条件, 先# 检测横向位移
            if np.max(resample_points[:, 1]) - np.min(resample_points[:, 1]) < wide_thres:
                # 再检测曲率符号
                if sign_changes >= 2 and np.max(np.abs(curvatures)) < 0.05:
                    drive_tag["lane_change"] = True
            else:
                if resample_points[-1][1] > resample_points[0][1]:
                    # 左转
                    frame["drive_tag"]["leftturn"] = True
                else:
                    # 右转
                    frame["drive_tag"]["rightturn"] = True
                # print("ts: {} , json {} find! {}".format(cam120_ts, json_path, frame["drive_tag"]))
                # 记录左转右转的合法下标
                valid_pos[frame_id:end_pos] = 1

                # 记录当前合法时间戳对应的结尾时间戳
                save_dict[cam120_ts] = dict(
                    end_ts=frames[end_pos-1]["sensor_data"]["cam_front_120"]["timestamp"],
                    json_path=json_path
                )
    if np.sum(valid_pos) == 0:
        return []
    # 保存save_dict
    save_path = "s3://zqt/filter_0224/{:0>5d}.json".format(task_id)
    print(f"{save_path} saved!")
    json.dump(save_dict, smart_open(save_path, "w"))
    return [json_path]

if __name__ == "__main__":
    import concurrent.futures
    coarse_folder = "s3://zqt/filter_0224"
    json_list = [smart_path_join(coarse_folder, tmp) for tmp in smart_listdir(coarse_folder)]

    sub_prelabel_list = []    
    for json_path in tqdm(json_list):
        json_data = json.load(smart_open(json_path, "r"))
        ts_key_list = list(json_data.keys())
        sub_prelabel_list.append(json_data[ts_key_list[0]]["json_path"])

    print("totally {} prelabel jsons".format(len(sub_prelabel_list)))
    ste()
    # prelabel_list = ["s3://tf-rhea-data-bpp/e2e_cdx_urban/prelabel-urban/car_z02/ppl_bag_20241216_103739_det/v0_241224_171805/0005/fused_e2e_map_prelabel_result/0005.json"]
    # for task_id, json_path in enumerate(tqdm(prelabel_list)):
    #     deal_one_json(json_path, task_id)
    
    final_list = []
    results = set()
    with concurrent.futures.ProcessPoolExecutor(max_workers=90) as executor:
        for task_id, json_path in enumerate(tqdm(prelabel_list)):
            results.add(executor.submit(deal_one_json, json_path, task_id))

    for future in tqdm(results):
        final_list.extend(future.result())
    
    print("{} manual jsons from 9w".format(len(final_list)))



    