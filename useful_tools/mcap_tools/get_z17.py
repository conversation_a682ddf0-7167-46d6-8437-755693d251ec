'''

'''
from refile import smart_open, smart_path_join, smart_listdir
from pdb import set_trace as ste
import json
import numpy as np
from tqdm import tqdm
import copy
from pdb import set_trace as ste
from refile import smart_path_join, smart_listdir, smart_exists, smart_open
import concurrent.futures
import os
import os.path as osp
import cv2
from tqdm import tqdm
import numpy as np
import nori2 as nori
import torch
from skimage import io as skimage_io
import io
import quaternion
from pdb import set_trace as ste
import json
from refile import smart_open, smart_path_join, smart_exists
import concurrent.futures
import time
from numpy.lib import recfunctions as rfn
import copy

from copy import deepcopy
from PIL import Image


def read_image(image_path):
    with smart_open(image_path, 'rb') as f:
        im = Image.open(f)  # RGB
        img = np.array(deepcopy(im), dtype=np.uint8)
        img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
    return img


def get_undistort_image(img, map1, map2):
    """obtain undistort image for current frame index.
    Returns:
        undistort_img: undistort image
    """
    undistort_img = cv2.remap(img, map1, map2, cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT)
    return undistort_img

def get_sensor_tran_matrix(sensor_extrinsic):
    trans = [sensor_extrinsic["transform"]["translation"][key] for key in ["x", "y", "z"]]
    quats = [sensor_extrinsic["transform"]["rotation"][key] for key in ["x", "y", "z", "w"]]
    trans_matrix = np.eye(4, 4)
    rotation = R.from_quat(quats).as_matrix()
    trans_matrix = np.eye(4)
    trans_matrix[:3, :3] = rotation
    trans_matrix[:3, 3] = trans
    return trans_matrix

def calc_undistort_mapping(cam: dict, dim: tuple, undistort_alpha: None, cam_name: str):
    intrinsic_K = np.array(cam["K"]).reshape(3, 3)
    intrinsic_D = np.array(cam["D"])
    assert cam["distortion_model"] in [
        "fisheye",
        "pinhole",
    ], "distortion model {} not supported".format(cam["distortion_model"])

    if isinstance(undistort_alpha, type(None)):
        new_intrinsic_K = intrinsic_K
    else:
        x = 1.3
        new_intrinsic_K = copy.deepcopy(intrinsic_K)
        new_intrinsic_K[0,0] = new_intrinsic_K[0,0]* x
        new_intrinsic_K[1,1] = new_intrinsic_K[1,1]* x

    if cam["distortion_model"] == "fisheye":
        map1, map2 = cv2.fisheye.initUndistortRectifyMap(
            intrinsic_K, intrinsic_D, np.eye(3), new_intrinsic_K, dim, cv2.CV_16SC2
        )
    else:
        map1, map2 = cv2.initUndistortRectifyMap(
            intrinsic_K, intrinsic_D, np.eye(3), new_intrinsic_K, dim, cv2.CV_16SC2
        )
    
    return (map1, map2), new_intrinsic_K



def get_fake_json(json_path, calib_dev_int, calib_dev_ext, save_folder_name, nori_path):
    json_data = json.load(smart_open(json_path, "r"))
    split_list = json_path.split("/")
    postfix = "/".join(split_list[-3:])
    car_id = split_list[-7]
    date = split_list[-6][8:16]
    calib = json_data["calibrated_sensors"]
    frames = json_data["frames"]

    roi_name_list = [
        'cam_front_120',
        'cam_front_30',
        'cam_front_right_100',
        'cam_front_left_100'
    ]

    
    for frame in tqdm(frames[:1]):
        frame["bev"] = dict(
            lane={},
            arrow={},
            crosswalk={},
            stopline={},
        )
        sensor_data = frame["sensor_data"]
        for cam_name in roi_name_list:
            nori_id = sensor_data[cam_name]["nori_id"]
            ts = sensor_data[cam_name]["timestamp"]
            # nori_path = "/mnt/tf-rhea-data-bpp/zqt/megsim-data/parsed_data/car_z18/20250216/ppl_bag_20250216_143908_test/ppl_bag_20250216_143908_0/v1_250216_191645/all_nori/image.nori"
            nori_path = nori_path.replace("s3://megsim-data", "/mnt/tf-rhea-data-bpp/megsim-data")
            vid = int(nori_id.split(",")[0])
            vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
            img = cv2.imdecode(np.frombuffer(vreader.get(nori_id), dtype=np.uint8), 1)
            print("cam name: {} , cam shape: {}".format(cam_name, img.shape))
            ori_resolution = tuple(calib_dev_int[cam_name]["resolution"])  # w, h
            img = cv2.resize(img, (ori_resolution[0], ori_resolution[1]), interpolation=cv2.INTER_LINEAR)

            # 去畸变
            # if cam_name != "cam_front_120":
            #     maps, new_K = calc_undistort_mapping(calib_dev[cam_name], ori_resolution, None, cam_name)

            if cam_name == "cam_front_120":
                maps, new_K = calc_undistort_mapping(calib_dev_int[cam_name], ori_resolution, 1, cam_name)
                new_120_13k = new_K
                img = get_undistort_image(img, maps[0], maps[1])

            # 存图
            zqt_img_folder = "/data/tf-rhea-data-bpp/zqt/{}".format(save_folder_name)
            os.makedirs(zqt_img_folder, exist_ok=True)
            s3_path = os.path.join(zqt_img_folder, "{}_{}.jpg".format(cam_name, ts))
            sensor_data[cam_name]["s3_path"] = s3_path.replace("/data/", "/mnt/")
            cv2.imwrite(s3_path, img)
            print(img.shape, cam_name)

    json_data["frames"] = json_data["frames"][:1]

    # 修改calib_sensor
    
    for cam_name in roi_name_list:
        if cam_name == "cam_front_120":
            json_data["calibrated_sensors"][cam_name]["intrinsic"] = calib_dev_int[cam_name]
            json_data["calibrated_sensors"][cam_name]["intrinsic"]["distortion_model"] = "pinhole"
            json_data["calibrated_sensors"][cam_name]["intrinsic"]["D"] = [[0.],[0.],[0.],[0.],[0.]]
            json_data["calibrated_sensors"][cam_name]["intrinsic"]["K"] = new_120_13k.tolist()
        else:
            json_data["calibrated_sensors"][cam_name]["intrinsic"] = calib_dev_int[cam_name]

        json_data["calibrated_sensors"][cam_name]["extrinsic"] = calib_dev_ext[cam_name]
    ste()
        
    # 截断frames
    # json_data["frames"] = frames

    # 保存json
    save_json_path = "s3://zqt/z17_temp_0308/{}_fix.json".format(save_folder_name)
    json.dump(json_data, smart_open(save_json_path, "w"))
    print(save_json_path, " dumped!")


if __name__ == "__main__":
    '''
    '''
    json_path = "s3://megsim-data/laneline_map/car_z18/20250218/ppl_bag_20250218_211926_test/222213_222253/0_391.json"
    # json_path = "s3://caiqianxi/tmp/z_10/road_entrance_e2emulti_fl_newcp_det_fix_calib/178000/car_z19/ppl_bag_20250211_163040_det/v0_250211_185007/ppl_bag_20250211_163040_det.json"
    nori_path = "s3://megsim-data/parsed_data/car_z18/20250218/ppl_bag_20250218_211926_test/222213_222253/v0_250220_010911/all_nori/image.nori"

    cam120_int = json.load(smart_open("/data/calibresult/car_00z17/camera_params/cam_front_120_intrinsic.json", "r"))
    cam30_int = json.load(smart_open("/data/calibresult/car_00z17/camera_params/cam_front_30_intrinsic.json", "r"))
    camleft100_int = json.load(smart_open("/data/calibresult/car_00z17/camera_params/cam_front_left_100_intrinsic.json", "r"))
    camright100_int = json.load(smart_open("/data/calibresult/car_00z17/camera_params/cam_front_right_100_intrinsic.json", "r"))

    cam120_ext = json.load(smart_open("/data/calibresult/car_00z17/camera_params/cam_front_120_extrinsic.json", "r"))
    cam30_ext = json.load(smart_open("/data/calibresult/car_00z17/camera_params/cam_front_30_extrinsic.json", "r"))
    camleft100_ext = json.load(smart_open("/data/calibresult/car_00z17/camera_params/cam_front_left_100_extrinsic.json", "r"))
    camright100_ext = json.load(smart_open("/data/calibresult/car_00z17/camera_params/cam_front_right_100_extrinsic.json", "r"))


    calib_dev_int = dict(
        cam_front_120 = cam120_int,
        cam_front_30 = cam30_int,
        cam_front_left_100 = camleft100_int,
        cam_front_right_100 = camright100_int
    )

    calib_dev_ext = dict(
        cam_front_120 = cam120_ext,
        cam_front_30 = cam30_ext,
        cam_front_left_100 = camleft100_ext,
        cam_front_right_100 = camright100_ext
    )


    save_folder_name = "-".join(json_path.split("/")[-3:-1])

    get_fake_json(json_path, calib_dev_int, calib_dev_ext, save_folder_name, nori_path)

