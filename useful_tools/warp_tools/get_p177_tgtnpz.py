import cv2
import argparse
import numpy as np
import nori2 as nori
import json, refile
from refile import smart_exists, smart_path_join, smart_open
from scipy.spatial.transform import Rotation as R
from perceptron.utils.file_io import load_pkl


if __name__ == "__main__":
    tgt_path = "s3://zqt/jira-data/zqt/manual/z10-rhea-2504-data/parsed_data/car_p1010/20250425/ppl_bag_20250425_150615_det/v0_250426_102507.json"
    func(args.src_path, args.tgt_path, args.save_root)