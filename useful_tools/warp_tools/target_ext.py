'''
获取目标外参文件
'''
import cv2
import argparse
import numpy as np
import nori2 as nori
import json, refile
from refile import smart_exists, smart_path_join, smart_open
from scipy.spatial.transform import Rotation as R
from perceptron.utils.file_io import load_pkl
from pdb import set_trace as ste


# calculate the img_warp_matrix
def get_sensor_tran_matrix(sensor_extrinsic):
    """
    相机外参转换为矩阵形式
    """
    trans = [sensor_extrinsic["transform"]["translation"][key] for key in ["x", "y", "z"]]
    quats = [sensor_extrinsic["transform"]["rotation"][key] for key in ["x", "y", "z", "w"]]
    trans_matrix = np.eye(4, 4)
    rotation = R.from_quat(quats).as_matrix()
    trans_matrix = np.eye(4)
    trans_matrix[:3, :3] = rotation
    trans_matrix[:3, 3] = trans
    return trans_matrix

def _fisheye_map(K, D, new_K, dim):
    return cv2.fisheye.initUndistortRectifyMap(
        K,
        D,
        np.eye(3),
        new_K,
        dim,
        cv2.CV_16SC2,
    )

def _fisheye_map2(K, D, new_K, dim):
    return cv2.fisheye.initUndistortRectifyMap(
        K,
        D,
        np.eye(3),
        new_K,
        dim,
        cv2.CV_32FC1,
    )
def _fisheye_map2_fov(K, D, new_K, dim):
    return cv2.fisheye.initUndistortRectifyMap(
        K,
        D,
        np.eye(3),
        new_K,
        dim,
        cv2.CV_32FC1,
    )

def get_lidar_to_pixel(sensor_info, intrinsic_k):
    """
    相机参数矩阵，包含内参和外参
    """
    transform = get_sensor_tran_matrix(sensor_info["extrinsic"])
    lidar2pix = np.eye(4)
    lidar2pix[:3, :3] = intrinsic_k
    lidar2pix = (lidar2pix @ transform)[:3].tolist()
    return lidar2pix


def _pinhole_undistort(camera_calib, scale, size):
    intrinsic_K = np.array(camera_calib["intrinsic"]["K"]).reshape(3, 3)
    intrinsic_D = np.array(camera_calib["intrinsic"]["D"])
    # print(intrinsic_D.shape)
    new_intrinsic_K = intrinsic_K
    new_intrinsic_K[:2] = new_intrinsic_K[:2] / scale
    map1, map2 = cv2.initUndistortRectifyMap(
        intrinsic_K,
        intrinsic_D,
        np.eye(3),
        new_intrinsic_K,
        size,
        cv2.CV_32FC1,
    )
    # print(map1.shape, map2.shape)
    return map1, map2, new_intrinsic_K

def _undistort_map(camera_calib, scale, size):
    intrinsic_K = np.array(camera_calib["intrinsic"]["K"]).reshape(3, 3)
    intrinsic_D = np.array(camera_calib["intrinsic"]["D"])
    new_intrinsic_K = intrinsic_K # new_intrinsic_K = intrinsic_K.copy() --> 不加scale的方式不对
    # print(new_intrinsic_K)
    new_intrinsic_K[:2] = new_intrinsic_K[:2] / scale
    undistort_map0, undistort_map1 = _fisheye_map2(intrinsic_K, intrinsic_D, new_intrinsic_K, size)
    return undistort_map0, undistort_map1, new_intrinsic_K

def _undistort_map_fov(camera_calib, scale, size, fov_tag):
    intrinsic_K = np.array(camera_calib["intrinsic"]["K"]).reshape(3, 3)
    intrinsic_D = np.array(camera_calib["intrinsic"]["D"])
    intrinsic_K[:2] = intrinsic_K[:2] / scale
    fovs_intrinsic_k_dict = load_pkl("s3://camera-perceptron/resources/calib/fovs_intrinsic_k_dict.pkl")
    new_intrinsic_K = fovs_intrinsic_k_dict[fov_tag].copy()
    new_intrinsic_K[:2] = new_intrinsic_K[:2] / scale
    undistort_map0, undistort_map1 = _fisheye_map2_fov(intrinsic_K, intrinsic_D, new_intrinsic_K, size)
    return undistort_map0, undistort_map1, new_intrinsic_K


if __name__ == "__main__":

    # # debug
    # tgt_path = "/data/workspace/release_save/extrinsic/target_extrinsic.npz"
    # ref_dict = dict(np.load(tgt_path))
    # # ste()


    target_dim = (1920, 1080) #(3840, 2160) #(1920, 1080)
    IMAGE_RESOLUTION = {"200w": (1920, 1080), "800w": (3840, 2160), "800w_org": (3840, 2165)}
    _CAMERA_LIST = {
        "cam_front_120": "800w",   # 地图需要, 这里需要放【第1个】，用来和 rv 监督对齐
        # "cam_front_120_sim_fov70": "800w",
        "cam_front_30": "800w",
        # "cam_back_120_sim_fov70": "800w",
        "cam_front_left_100": "200w",
        "cam_front_right_100": "200w",
        # "cam_back_left_120": "200w",
        # "cam_back_right_120": "200w",
    }

    # tgt_path = "/data/workspace/release_save/extrinsic/target_extrinsic.npz"
    # path_tar = "s3://zqt/jira-data/zqt/manual/z10-rhea-2504-data/parsed_data/car_p1010/20250425/ppl_bag_20250425_150615_det/v0_250426_102507.json"


    path_tar = "s3://tf-map-data-qy/data_rebuild/data_rebuild_mm_wfl_newcp_z10_fixed_calib/jsons/car_z03/ppl_bag_20241211_142550_det_22527_22756.json"


    with refile.smart_open(path_tar, "r") as f:
        json_data_tar = json.load(f)


    target_extrinsic_all = {}
    for camera_name_tar, value in _CAMERA_LIST.items():
        source_dim = IMAGE_RESOLUTION[value]
        print(camera_name_tar)
        #camera_name_tar = "cam_front_120_sim_fov30"
        # camera_name = "cam_front_120"
        if "_sim_fov" in camera_name_tar:
            camera_name = camera_name_tar.split("_sim_fov")[0]
            fov_tag = camera_name_tar.split("_")[-1]
        else:
            camera_name = camera_name_tar
            fov_tag = None
        
        camera_calib_tar = json_data_tar["calibrated_sensors"][camera_name]

        # calculate the undistort_map
        if "_sim_fov" not in camera_name_tar:
            if camera_calib_tar["intrinsic"]["distortion_model"] != "fisheye":
                undistort_map0_tar, undistort_map1_tar, new_intrinsic_K_tar = _pinhole_undistort(camera_calib_tar, source_dim[0] / target_dim[0], target_dim)
            else:
                undistort_map0_tar, undistort_map1_tar, new_intrinsic_K_tar = _undistort_map(camera_calib_tar, scale = source_dim[0] / target_dim[0], size = target_dim)
        else:
            undistort_map0_tar, undistort_map1_tar, new_intrinsic_K_tar = _undistort_map_fov(camera_calib_tar, scale = source_dim[0] / target_dim[0], size = target_dim, fov_tag=fov_tag)


        lidar2pix_tar = get_lidar_to_pixel(camera_calib_tar, new_intrinsic_K_tar)
        lidar2img_tar = np.concatenate([np.array(lidar2pix_tar), np.array([[0.0, 0.0, 0.0, 1.0]])])
        target_extrinsic_all[camera_name_tar] = lidar2img_tar
    
    target_extrinsic_all["cam_front_left_120"] = target_extrinsic_all["cam_front_left_100"]
    target_extrinsic_all["cam_front_right_120"] = target_extrinsic_all["cam_front_right_100"]
    target_extrinsic_all.pop("cam_front_left_100")
    target_extrinsic_all.pop("cam_front_right_100")

    save_root = "/data/"
    save_path = smart_path_join(save_root, "target_extrinsic_z03_ld.npz")
    np.savez(save_path, **target_extrinsic_all)##
    print(save_path, " dumped!")