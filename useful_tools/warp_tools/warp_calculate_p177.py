import cv2
import argparse
import numpy as np
import nori2 as nori
import json, refile
from refile import smart_exists, smart_path_join, smart_open
from scipy.spatial.transform import Rotation as R
from perceptron.utils.file_io import load_pkl

def show_img(img):
    import matplotlib.pyplot as plt
    # img = img.transpose(1, 2, 0)
    plt.figure()
    arrayShow = img[..., ::-1] # transfer tensor to array
    plt.imshow((arrayShow).astype('uint8'))	#show image
    
def compute_perspective_remap_0(M, size):
    """
    计算用于透视变换的映射表 map1 和 map2。
    
    :param M: 透视变换矩阵 (3x3)
    :param size: 目标图像的尺寸 (宽度, 高度)
    :return: (map1, map2) 映射表
    """
    width, height = size
    map1 = np.zeros((height, width), dtype=np.float32)
    map2 = np.zeros((height, width), dtype=np.float32)

    # 计算映射表
    for y in range(height):
        for x in range(width):
            # 计算透视变换
            pt = np.array([x, y, 1.0])
            src_pt = np.dot(np.linalg.inv(M), pt)
            src_pt = src_pt / src_pt[2]
            map1[y, x] = src_pt[1]
            map2[y, x] = src_pt[0]

    return map1, map2

def compute_perspective_remap(M, size):
    """
    计算用于透视变换的映射表 map1 和 map2。
    
    :param M: 透视变换矩阵 (3x3)
    :param size: 目标图像的尺寸 (宽度, 高度)
    :return: (map1, map2) 映射表
    """
    width, height = size
    map1 = np.zeros((height, width), dtype=np.float32)
    map2 = np.zeros((height, width), dtype=np.float32)

    # 创建坐标网格
    x_indices, y_indices = np.meshgrid(np.arange(width), np.arange(height))

    # 计算透视变换
    pts = np.stack((x_indices.ravel(), y_indices.ravel(), np.ones_like(x_indices.ravel())), axis=1)
    src_pts = np.dot(np.linalg.inv(M), pts.T).T
    src_pts /= src_pts[:, 2, np.newaxis]  # 归一化

    map2 = src_pts[:, 0].reshape(height, width)
    map1 = src_pts[:, 1].reshape(height, width)

    return map1, map2

def combine_remap(mapx1, mapy1, mapx2, mapy2):
    # mapx1 and mapy1 are the mapping matrices for the first remap
    # mapx2 and mapy2 are the mapping matrices for the second remap

    # Get the shape of the mappings
    h, w = mapx1.shape

    # Create empty matrices for the combined map
    mapy_combined = np.zeros((h, w), dtype=np.float32)
    mapx_combined = np.zeros((h, w), dtype=np.float32)

    # Compute the combined map
    # mapy_combined = mapy2[mapy1.astype(np.int32), mapx1.astype(np.int32)]
    # mapx_combined = mapx2[mapy1.astype(np.int32), mapx1.astype(np.int32)]
    #
    mapy_combined = cv2.remap(mapy2, mapx1.astype(np.float32), mapy1.astype(np.float32), interpolation=cv2.INTER_LINEAR)
    mapx_combined = cv2.remap(mapx2, mapx1.astype(np.float32), mapy1.astype(np.float32), interpolation=cv2.INTER_LINEAR)

    return mapy_combined, mapx_combined

def _fisheye_map(K, D, new_K, dim):
    return cv2.fisheye.initUndistortRectifyMap(
        K,
        D,
        np.eye(3),
        new_K,
        dim,
        cv2.CV_16SC2,
    )
def _fisheye_map2(K, D, new_K, dim):
    return cv2.fisheye.initUndistortRectifyMap(
        K,
        D,
        np.eye(3),
        new_K,
        dim,
        cv2.CV_32FC1,
    )
def _fisheye_map2_fov(K, D, new_K, dim):
    return cv2.fisheye.initUndistortRectifyMap(
        K,
        D,
        np.eye(3),
        new_K,
        dim,
        cv2.CV_32FC1,
    )
def _undistort_map(camera_calib, scale, size):
    intrinsic_K = np.array(camera_calib["intrinsic"]["K"]).reshape(3, 3)
    intrinsic_D = np.array(camera_calib["intrinsic"]["D"])
    new_intrinsic_K = intrinsic_K # new_intrinsic_K = intrinsic_K.copy() --> 不加scale的方式不对
    # print(new_intrinsic_K)
    new_intrinsic_K[:2] = new_intrinsic_K[:2] / scale
    undistort_map0, undistort_map1 = _fisheye_map2(intrinsic_K, intrinsic_D, new_intrinsic_K, size)
    return undistort_map0, undistort_map1, new_intrinsic_K

def _undistort_map_fov(camera_calib, scale, size, fov_tag):
    intrinsic_K = np.array(camera_calib["intrinsic"]["K"]).reshape(3, 3)
    intrinsic_D = np.array(camera_calib["intrinsic"]["D"])
    intrinsic_K[:2] = intrinsic_K[:2] / scale
    fovs_intrinsic_k_dict = load_pkl("s3://camera-perceptron/resources/calib/fovs_intrinsic_k_dict.pkl")
    new_intrinsic_K = fovs_intrinsic_k_dict[fov_tag].copy()
    new_intrinsic_K[:2] = new_intrinsic_K[:2] / scale
    undistort_map0, undistort_map1 = _fisheye_map2_fov(intrinsic_K, intrinsic_D, new_intrinsic_K, size)
    return undistort_map0, undistort_map1, new_intrinsic_K

def _pinhole_undistort(camera_calib, scale, size):
    intrinsic_K = np.array(camera_calib["intrinsic"]["K"]).reshape(3, 3)
    intrinsic_D = np.array(camera_calib["intrinsic"]["D"])
    # print(intrinsic_D.shape)
    new_intrinsic_K = intrinsic_K
    new_intrinsic_K[:2] = new_intrinsic_K[:2] / scale
    map1, map2 = cv2.initUndistortRectifyMap(
        intrinsic_K,
        intrinsic_D,
        np.eye(3),
        new_intrinsic_K,
        size,
        cv2.CV_32FC1,
    )
    # print(map1.shape, map2.shape)
    return map1, map2, new_intrinsic_K

target_dim = (1920, 1080) #(3840, 2160) #(1920, 1080)
IMAGE_RESOLUTION = {"200w": (1920, 1080), "800w": (3840, 2160), "800w_org": (3840, 2165)}
_CAMERA_LIST = {
    "cam_front_120": "800w",   # 地图需要, 这里需要放【第1个】，用来和 rv 监督对齐
    "cam_front_120_sim_fov70": "800w",
    "cam_front_30": "800w",
    "cam_back_120_sim_fov70": "800w",
    "cam_front_left_120": "200w",
    "cam_front_right_120": "200w",
    "cam_back_left_120": "200w",
    "cam_back_right_120": "200w",
}

def func(path, path_tar,save_root):

    source_extrinsic_all = {}
    target_extrinsic_all = {}
    img_warp_matrix_dict_all = {}
    img_warp_maps_dict_all = {}
    for camera_name_tar, value in _CAMERA_LIST.items():
        source_dim = IMAGE_RESOLUTION[value]
        print(camera_name_tar)
        #camera_name_tar = "cam_front_120_sim_fov30"
        camera_name = "cam_front_120"
        if "_sim_fov" in camera_name_tar:
            camera_name = camera_name_tar.split("_sim_fov")[0]
            fov_tag = camera_name_tar.split("_")[-1]
        else:
            camera_name = camera_name_tar
            fov_tag = None

        # source car calib
        nori_fetcher = nori.Fetcher()
        # path = "s3://tf-rhea-data-bpp/new_track_gjh/prelabeled_data/car_15/20241002_dp-track/ppl_bag_20241002_101704_det/v0_241004_082720/splited_video_prelabels_tracking/0091.json"
        # path = "s3://tf-rhea-data-bpp/new_track_gjh/prelabeled_data/car_14/20241006_dp-track/ppl_bag_20241006_064208_det/v0_241007_190703/splited_video_prelabels_tracking/0000.json"
        with refile.smart_open(path, "r") as f:
            json_data = json.load(f)
        camera_calib = json_data["calibrated_sensors"][camera_name]
        nori_id = json_data["frames"][100]["sensor_data"][camera_name]["nori_id"]
        # calculate the undistort_map
        if "_sim_fov" not in camera_name_tar:
            if camera_calib["intrinsic"]["distortion_model"] != "fisheye":
                undistort_map0, undistort_map1, new_intrinsic_K = _pinhole_undistort(camera_calib, source_dim[0] / target_dim[0], target_dim)
            else:
                undistort_map0, undistort_map1, new_intrinsic_K = _undistort_map(camera_calib, scale = source_dim[0] / target_dim[0], size = target_dim)
        else:
            undistort_map0, undistort_map1, new_intrinsic_K = _undistort_map_fov(camera_calib, scale = source_dim[0] / target_dim[0], size = target_dim, fov_tag=fov_tag)


        # target car calib
        # path_tar = "s3://tf-rhea-data-bpp/new_track_gjh/prelabeled_data/car_15/20241002_dp-track/ppl_bag_20241002_101704_det/v0_241004_082720/splited_video_prelabels_tracking/0091.json"
        with refile.smart_open(path_tar, "r") as f:
            json_data_tar = json.load(f)
        camera_calib_tar = json_data_tar["calibrated_sensors"][camera_name]
        # calculate the undistort_map
        if "_sim_fov" not in camera_name_tar:
            if camera_calib["intrinsic"]["distortion_model"] != "fisheye":
                undistort_map0_tar, undistort_map1_tar, new_intrinsic_K_tar = _pinhole_undistort(camera_calib_tar, source_dim[0] / target_dim[0], target_dim)
            else:
                undistort_map0_tar, undistort_map1_tar, new_intrinsic_K_tar = _undistort_map(camera_calib_tar, scale = source_dim[0] / target_dim[0], size = target_dim)
        else:
            undistort_map0_tar, undistort_map1_tar, new_intrinsic_K_tar = _undistort_map_fov(camera_calib_tar, scale = source_dim[0] / target_dim[0], size = target_dim, fov_tag=fov_tag)

        # calculate the img_warp_matrix
        def get_sensor_tran_matrix(sensor_extrinsic):
            """
            相机外参转换为矩阵形式
            """
            trans = [sensor_extrinsic["transform"]["translation"][key] for key in ["x", "y", "z"]]
            quats = [sensor_extrinsic["transform"]["rotation"][key] for key in ["x", "y", "z", "w"]]
            trans_matrix = np.eye(4, 4)
            rotation = R.from_quat(quats).as_matrix()
            trans_matrix = np.eye(4)
            trans_matrix[:3, :3] = rotation
            trans_matrix[:3, 3] = trans
            return trans_matrix

        def get_lidar_to_pixel(sensor_info, intrinsic_k):
            """
            相机参数矩阵，包含内参和外参
            """
            transform = get_sensor_tran_matrix(sensor_info["extrinsic"])
            lidar2pix = np.eye(4)
            lidar2pix[:3, :3] = intrinsic_k
            lidar2pix = (lidar2pix @ transform)[:3].tolist()
            return lidar2pix

        lidar2pix = get_lidar_to_pixel(camera_calib, new_intrinsic_K)
        lidar2img = np.concatenate([np.array(lidar2pix), np.array([[0.0, 0.0, 0.0, 1.0]])])
        #print(lidar2img)
        lidar2pix_tar = get_lidar_to_pixel(camera_calib_tar, new_intrinsic_K_tar)
        lidar2img_tar = np.concatenate([np.array(lidar2pix_tar), np.array([[0.0, 0.0, 0.0, 1.0]])])
        #print(lidar2img_tar)
        
        def calc_warp_between_imgs(campose_geom_trans_matrix, K, ego2cam_proj, W, H, ego_voxels=None):
            try:
                if ego_voxels is None:
                    x_coord, y_coord, z_coord = np.mgrid[-15:15, -100:100, -10:10]
                    ego_voxels = np.stack((x_coord, y_coord, z_coord), axis=-1).reshape(-1, 3)
                    ego_voxels = np.c_[ego_voxels, np.ones(len(ego_voxels))]

                cam_voxels = (ego2cam_proj @ ego_voxels.T).T  # (n, 4)
                tgt_cam_voxels = (campose_geom_trans_matrix @ cam_voxels.T).T

                src_pixels = (K @ cam_voxels[:, :3].T).T
                dst_pixels = (K @ tgt_cam_voxels[:, :3].T).T

                src_pixels[:, :2] = src_pixels[:, :2] / src_pixels[:, 2:3]
                dst_pixels[:, :2] = dst_pixels[:, :2] / dst_pixels[:, 2:3]

                depth_filter = (src_pixels[:, 2] > 0) & (dst_pixels[:, 2] > 0)
                width_filter = ((src_pixels[:, 0] > 0) & (src_pixels[:, 0] < W)) & (
                    (dst_pixels[:, 0] > 0) & (dst_pixels[:, 0] < W)
                )
                height_filter = ((src_pixels[:, 1] > 0) & (src_pixels[:, 1] < H)) & (
                    (dst_pixels[:, 1] > 0) & (dst_pixels[:, 1] < H)
                )
                filters = depth_filter & width_filter & height_filter

                src_pixels = src_pixels[filters].astype(np.float32)[:, :2]
                dst_pixels = dst_pixels[filters].astype(np.float32)[:, :2]

                warp_matrix, valid_pts = cv2.findHomography(src_pixels, dst_pixels, cv2.RANSAC, 2.0)
                warp_matrix_inv = np.linalg.inv(warp_matrix)  # 通过求逆来判断是否合格
                if np.count_nonzero(np.isnan(warp_matrix_inv)):
                    return np.eye(3)
                # print(sum(valid_pts), dst_pixels.shape)
                return warp_matrix
            except Exception:
                return np.eye(3)

        # ---------------------- two stage ----------------------------- #
        img = cv2.imdecode(np.frombuffer(nori_fetcher.get(nori_id), dtype=np.uint8), 1)
        print(img.shape)
        # resize(3840, 2160) --> (1920, 1080) + undistort + warp to target extrinsic
        show_img(img)
        # resize(3840, 2160)
        img = cv2.resize(img, (1920, 1080), interpolation=cv2.INTER_LINEAR)
        # show_img(img)
        # warp to target extrinsic
        # img_warp_matrix = dict(np.load("/home/<USER>/perceptron/Perceptron/img_warp_matrix_dict_all.npz"))["cam_front_120"]
        campose_geom_trans_matrix = lidar2img_tar @ np.linalg.inv(lidar2img)
        img_warp_matrix = calc_warp_between_imgs(
            campose_geom_trans_matrix,
            np.eye(3),  # sample_queue[0]["tran_mats_dict"]["cam2img"][i],
            lidar2img,  # trans_ego2cam[-1],
            img.shape[1],
            img.shape[0],
        )
        im_h, im_w, _ = img.shape
        img = cv2.warpPerspective(
            img, img_warp_matrix, (im_w, im_h)
        )
        #show_img(img)
        # undistort
        img = cv2.remap(img, undistort_map0, undistort_map1, cv2.INTER_LINEAR)
        show_img(img)
        img_old = img.copy()
        
        # merge undistort and warp
        # ---------------------- one stage ----------------------------- #
        # show_img(img)
        # convert warp_matrix to maps
        # img_warp_matrix = dict(np.load("/home/<USER>/perceptron/Perceptron/img_warp_matrix_dict_all.npz"))["cam_front_120"]
        source_extrinsic_all[camera_name] = lidar2img
        target_extrinsic_all[camera_name_tar] = lidar2img_tar
        img_warp_matrix_dict_all[camera_name_tar] = img_warp_matrix
        map1, map2 = compute_perspective_remap(img_warp_matrix, size = target_dim)
        img_warp_maps_dict_all[camera_name_tar] = map1.astype(np.float32), map2.astype(np.float32)
        # undistort_map0 = np.load("/home/<USER>/perceptron/Perceptron/cam_front_120_undistort_map0.npz")["arr_0"]
        # undistort_map1 = np.load("/home/<USER>/perceptron/Perceptron/cam_front_120_undistort_map1.npz")["arr_0"]
        # print(undistort_map0.shape)
        map1_new, map2_new = combine_remap(undistort_map0, undistort_map1, map1, map2)
        # map1_new_perceptron, map2_new_perceptron = map1_new.copy(), map2_new.copy()
        # print(map1_new_perceptron.shape)
        
        #undistort_map0_new = np.stack((map1_new, map2_new), axis=-1)
        img = cv2.imdecode(np.frombuffer(nori_fetcher.get(nori_id), dtype=np.uint8), 1)
        # resize(3840, 2160)
        img = cv2.resize(img, (1920, 1080), interpolation=cv2.INTER_LINEAR)
        #show_img(img)
        # remap
        # print(map1_new.shape, map1_new.dtype)
        # print(map2_new.shape, map1_new.dtype)
        img = cv2.remap(img, map1_new.astype(np.float32), map2_new.astype(np.float32), cv2.INTER_LINEAR)
        show_img(img)
        img_perceptron = img.copy()


    np.savez(smart_path_join(save_root, "target_extrinsic_all_hf15_new.npz"), **target_extrinsic_all)##
    # np.savez("/home/<USER>/temp/img_warp_matrix_dict_all_hf15to15npz", **img_warp_matrix_dict_all)##
    np.savez(smart_path_join(save_root, "img_warp_maps_dict_all_hf14to15_new.npz"), **img_warp_maps_dict_all)

if __name__ == "__main__":
    '''
    '''
    parser = argparse.ArgumentParser()
    parser.add_argument('--task_name',default='key_frame_num')
    parser.add_argument(
        "--src_path", type=str, default="s3://tf-rhea-data-bpp/new_track_gjh/prelabeled_data/car_14/20241006_dp-track/ppl_bag_20241006_064208_det/v0_241007_190703/splited_video_prelabels_tracking/0000.json", help="the data path"
    )
    parser.add_argument(
        "--tgt_path", type=str, default="s3://tf-rhea-data-bpp/new_track_gjh/prelabeled_data/car_15/20241002_dp-track/ppl_bag_20241002_101704_det/v0_241004_082720/splited_video_prelabels_tracking/0091.json", help="the data path"
    )
    parser.add_argument(
        "--save_root", type=str, default="/home/<USER>/temp/", help="the save path"
    )
    args = parser.parse_args()

    func(args.src_path, args.tgt_path, args.save_root)