'''
批量检查高精jpg是否打入gpfs
'''
import os
import os.path as osp
import cv2
from tqdm import tqdm
import numpy as np
import nori2 as nori
import torch
from skimage import io as skimage_io
import io
import quaternion
from pdb import set_trace as ste
import json
from refile import smart_open, smart_path_join, smart_exists
import concurrent.futures
import time
import copy



def check_gpfs(json_path):
    json_data = json.load(smart_open(json_path, "r"))
    for frame in tqdm(json_data["frames"]):
        for cam_name, cam_val in frame["sensor_data"].items():
            if cam_name not in ['cam_front_120', 'cam_front_30', 'cam_front_left_100', 'cam_front_right_100']:
                continue
            mnt_path = cam_val["s3_path"].replace("s3://", "/mnt/")
            static_path = cam_val["s3_path"].replace("s3://", "/mnt/acceldata/static/")
            raw_path = cam_val["s3_path"]
            if (not smart_exists(mnt_path)) and (not smart_exists(static_path)):
                print(f"{json_path} not exist, {raw_path}")
                return [json_path]
    return []


if __name__ == "__main__":
    json_path = "s3://zqt/dataset/hd_20250410_clip_finish_15982.json"
    json_data = json.load(smart_open(json_path, "r"))
    json_list = json_data["paths"] if isinstance(json_data, dict) else json_data

    # json_list = ["s3://dmq-qy-test/wql/dataset/hdmap/20250410/trough/z10-rhea-data/hdmap_crosscheck_result_langge/json/z10-rhea-data/hdmap_3d2d_optimized_result_langge/car_z08/20250120/ppl_bag_20250120_220742_det/v0_250330_131320/model_m10/v0_250329_000000/17_3d2d/17_3d2d.json",]
    json_list = ["s3://dmq-qy-test/wql/dataset/hdmap/20250410/trough_in_junction/z10-rhea-data/hdmap_3d2d_optimized_result_langge/car_z04/20250203/ppl_bag_20250203_153250_det/v0_250301_191456/model_m8/v1_250318_231547/32/json/32_3d2d.json"]
    # 单进程
    for json_path in tqdm(json_list):
        check_gpfs(json_path)

    # # 多进程
    # bad_list = []
    # results = set()
    # with concurrent.futures.ProcessPoolExecutor(max_workers=100) as executor:
    #     for json_path in tqdm(json_list):
    #         results.add(executor.submit(check_gpfs, json_path))
    # for future in tqdm(results):
    #     bad_list.extend(future.result())
    # json.dump(bad_list, smart_open("s3://zqt/hd0410-badlist.json", "w"))

    # bad_list = json.load(smart_open("s3:", "r"))
    # good_list = []
    # for json_path in json_list:
    #     if json_path not in bad_list:
    #         good_list.append(json_path)
    
    # json_data["paths"] = good_list
    # json.dump(json_data, smart_open("s3://zqt/dataset/hd_20250410_clip_finish_15773.json", "w"))

    # ste()
