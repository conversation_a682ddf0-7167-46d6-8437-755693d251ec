#!/usr/bin/env python3
"""
快速检查数据完整性的脚本
用于诊断为什么所有图像加载都失败
"""

import sys
import os
import random

# 添加项目路径
sys.path.insert(0, '/Users/<USER>/projects/Perceptron')

def check_data_integrity():
    print("="*60)
    print("数据完整性检查")
    print("="*60)
    
    try:
        # 导入必要的模块
        from perceptron.exps.end2end.private.maptrv2.maptrv2_exp_z10_4v0r_fasternetm_nowarp1500w_warp0522data import Exp
        from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
        
        print("✓ 模块导入成功")
        
        # 创建实验实例
        exp = Exp()
        data_cfg = exp.data_val_cfg_map
        
        print("✓ 实验配置获取成功")
        print(f"数据配置键: {list(data_cfg.keys())}")
        
        # 创建数据集
        dataset = PrivateE2EDataset(**data_cfg)
        print(f"✓ 数据集创建成功，长度: {len(dataset)}")
        
        # 检查数据集的基本信息
        print(f"相机名称: {dataset.camera_names}")

        # 获取原始相机名称
        if hasattr(dataset, 'raw_names'):
            raw_names = dataset.raw_names
        elif hasattr(dataset, 'image') and hasattr(dataset.image, 'raw_names'):
            raw_names = dataset.image.raw_names
        else:
            raw_names = dataset.camera_names  # 使用camera_names作为备选

        print(f"原始相机名称: {raw_names}")

        # 随机检查几个样本的传感器数据
        print("\n检查随机样本的传感器数据...")
        for i in range(5):
            random_idx = random.randint(0, len(dataset) - 1)
            frame_idx = dataset.loader_output["frame_index"][random_idx]
            sensor_data = dataset.loader_output["frame_data_list"][random_idx]["sensor_data"]

            print(f"\n样本 {i+1} (frame_idx: {frame_idx}):")
            print(f"  可用传感器: {list(sensor_data.keys())}")

            # 检查每个相机的数据
            missing_cameras = []
            invalid_cameras = []

            for cam_name in raw_names:
                if cam_name not in sensor_data:
                    missing_cameras.append(cam_name)
                else:
                    cam_data = sensor_data[cam_name]
                    has_nori = "nori_id" in cam_data and cam_data["nori_id"] is not None
                    has_s3 = "s3_path" in cam_data and cam_data["s3_path"] is not None
                    has_data_path = "data_path" in cam_data and cam_data["data_path"] is not None
                    
                    if not (has_nori or has_s3 or has_data_path):
                        invalid_cameras.append(cam_name)
                    else:
                        print(f"    {cam_name}: ✓ (nori: {has_nori}, s3: {has_s3}, data_path: {has_data_path})")
            
            if missing_cameras:
                print(f"    缺失相机: {missing_cameras}")
            if invalid_cameras:
                print(f"    无效相机: {invalid_cameras}")
            
            # 如果所有相机都有问题，这就是问题所在
            if len(missing_cameras) + len(invalid_cameras) == len(raw_names):
                print(f"    ❌ 所有相机都无法使用！")
            else:
                print(f"    ✓ 有 {len(raw_names) - len(missing_cameras) - len(invalid_cameras)} 个相机可用")
        
        # 检查数据集配置
        print(f"\n数据集配置检查:")
        print(f"  数据模式: {getattr(dataset, 'data_mode', 'unknown')}")
        print(f"  GPFS前缀: {getattr(dataset, 'gpfs_prefix', 'unknown')}")
        
        # 检查loader输出
        loader_output = dataset.loader_output
        print(f"\nLoader输出检查:")
        print(f"  JSON集合数量: {len(loader_output['json_collection'])}")
        print(f"  帧数据列表长度: {len(loader_output['frame_data_list'])}")
        print(f"  帧索引长度: {len(loader_output['frame_index'])}")
        
        # 检查第一个JSON文件的路径
        if loader_output['json_collection']:
            first_json = loader_output['json_collection'][0]
            print(f"  第一个JSON文件: {first_json}")
            print(f"  文件是否存在: {os.path.exists(first_json) if first_json.startswith('/') else 'N/A (可能是远程路径)'}")
        
        print("\n" + "="*60)
        print("数据完整性检查完成")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    check_data_integrity()
