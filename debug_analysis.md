# MapTRv2 评估卡住问题调试指南

## 问题描述
程序在完成"Init Undistort Maps"后，在评估循环的第一步卡住，显示进度条：`1: 0%|0/11689 [00:00<?, ?it/s]`

## 已添加的Debug输出

我已经在以下关键位置添加了debug输出：

### 1. End2endVisualizator (perceptron/engine/executors/inference/e2e_inference.py)
- 数据迭代器创建
- 每个batch的获取过程
- batch后处理和前处理步骤
- test_step调用

### 2. PrivateE2EDataset (perceptron/data/det3d/private/private_multimodal.py)
- __getitem__方法调用
- index生成和数据获取过程
- pipeline处理

### 3. 图像加载模块 (perceptron/data/det3d/modules/image/base.py)
- get_images方法调用
- nori fetcher创建
- 每个相机的图像加载过程

### 4. 实验配置 (maptrv2_exp_z10_4v0r_fasternetm_nowarp1500w_warp0522data.py)
- 数据加载器创建
- test_step执行

## 运行调试版本

使用原始命令运行调试版本：
```bash
python3 perceptron/exps/end2end/private/maptrv2/maptrv2_exp_z10_4v0r_fasternetm_nowarp1500w_warp0522data.py --no-clearml -b 1 \
 --ckpt /data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_fasternetm_nowarp1500w_nowarp0522data/latest/dump_model/checkpoint_epoch_2.pth --eval_map
```

## 预期的Debug输出序列

正常情况下，你应该看到以下debug输出序列：

1. `[DEBUG] Creating validation dataset...`
2. `[DEBUG] Validation dataset created, length: XXXX`
3. `[DEBUG] Creating validation dataloader...`
4. `[DEBUG] Validation dataloader created`
5. `[DEBUG] Creating validation iterator...`
6. `[DEBUG] Validation iterator created`
7. `[DEBUG] Starting evaluation loop, total steps: XXXX`
8. `[DEBUG] Step 0: Getting next batch...`
9. `[DEBUG] PrivateE2EDataset.__getitem__ called with index: X`
10. `[DEBUG] Generated index_list: [...]`
11. `[DEBUG] Getting single data for index: X`
12. `[DEBUG] Getting images for index: X`
13. `[DEBUG] Processing camera: cam_xxx`
14. `[DEBUG] Loading image from nori_id: xxx` 或 `[DEBUG] Loading image from file path...`

## 可能的卡住位置分析

### 位置1: 第8步之后
如果在"Getting next batch..."之后卡住，说明数据加载器的第一次迭代有问题。

### 位置2: 第12-14步之间
如果在图像加载过程中卡住，可能是：
- nori数据获取超时
- 网络连接问题
- 文件系统访问问题

### 位置3: pipeline处理阶段
如果在"Running pipeline on sample_queue..."之后卡住，说明数据预处理管道有问题。

## 进一步的调试建议

### 1. 添加超时机制
可以在关键位置添加超时检测：

```python
import signal
import time

def timeout_handler(signum, frame):
    raise TimeoutError("Operation timed out")

# 在可能卡住的地方添加
signal.signal(signal.SIGALRM, timeout_handler)
signal.alarm(30)  # 30秒超时
try:
    # 执行可能卡住的操作
    result = some_operation()
finally:
    signal.alarm(0)  # 取消超时
```

### 2. 检查资源使用
运行时监控：
- CPU使用率
- 内存使用率
- 网络I/O
- 磁盘I/O

### 3. 简化测试
可以尝试：
- 减少batch_size到1
- 减少num_workers到0
- 使用更小的数据集进行测试

### 4. 检查数据完整性
验证：
- nori数据是否可访问
- 图像文件是否损坏
- 网络连接是否稳定

## 常见解决方案

### 1. 网络超时问题
如果是nori数据获取超时，可以：
- 增加网络超时时间
- 使用本地缓存
- 检查网络连接

### 2. 内存不足
如果是内存问题，可以：
- 减少batch_size
- 减少num_workers
- 使用更小的图像分辨率

### 3. 死锁问题
如果是多进程死锁，可以：
- 设置num_workers=0使用单进程
- 检查共享资源的访问

根据debug输出的具体位置，可以进一步定位问题的根本原因。
