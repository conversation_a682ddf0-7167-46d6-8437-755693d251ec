#!/usr/bin/env python3
"""
MapTRv2 评估卡住问题调试脚本
用于在服务器上定位具体的卡住位置
"""

import sys
import time
import signal
import traceback
from contextlib import contextmanager

# 添加超时装饰器
@contextmanager
def timeout_context(seconds, description="Operation"):
    def timeout_handler(signum, frame):
        raise TimeoutError(f"{description} timed out after {seconds} seconds")
    
    old_handler = signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(seconds)
    try:
        yield
    finally:
        signal.alarm(0)
        signal.signal(signal.SIGALRM, old_handler)

def debug_step(step_name, func, timeout_seconds=60):
    """执行一个调试步骤，带超时和错误处理"""
    print(f"\n{'='*50}")
    print(f"[DEBUG STEP] {step_name}")
    print(f"{'='*50}")
    start_time = time.time()
    
    try:
        with timeout_context(timeout_seconds, step_name):
            result = func()
        elapsed = time.time() - start_time
        print(f"[SUCCESS] {step_name} completed in {elapsed:.2f}s")
        return result
    except TimeoutError as e:
        print(f"[TIMEOUT] {step_name} - {e}")
        raise
    except Exception as e:
        print(f"[ERROR] {step_name} failed: {e}")
        print(f"[TRACEBACK] {traceback.format_exc()}")
        raise

def main():
    print("Starting MapTRv2 evaluation debugging...")
    
    # Step 1: 导入基础模块
    def import_basic_modules():
        import torch
        import numpy as np
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA device count: {torch.cuda.device_count()}")
        return True
    
    debug_step("Import basic modules", import_basic_modules, 30)
    
    # Step 2: 导入项目模块
    def import_project_modules():
        sys.path.insert(0, '/Users/<USER>/projects/Perceptron')  # 根据实际路径调整
        from perceptron.exps.end2end.private.maptrv2.maptrv2_exp_z10_4v0r_fasternetm_nowarp1500w_nowarp0522data import Exp
        print("Successfully imported Exp class")
        return Exp
    
    Exp = debug_step("Import project modules", import_project_modules, 60)
    
    # Step 3: 创建实验实例
    def create_exp_instance():
        exp = Exp()
        print(f"Experiment instance created")
        return exp
    
    exp = debug_step("Create experiment instance", create_exp_instance, 30)
    
    # Step 4: 配置数据加载器
    def configure_dataloader():
        exp.batch_size_per_device = 1  # 使用最小batch size
        val_loader = exp._configure_val_dataloader()
        print(f"Validation dataloader created, length: {len(val_loader)}")
        print(f"Dataset length: {len(val_loader.dataset)}")
        return val_loader
    
    val_loader = debug_step("Configure dataloader", configure_dataloader, 120)
    
    # Step 5: 创建数据迭代器
    def create_iterator():
        val_iter = iter(val_loader)
        print("Data iterator created successfully")
        return val_iter
    
    val_iter = debug_step("Create iterator", create_iterator, 30)
    
    # Step 6: 获取第一个batch (这里最可能卡住)
    def get_first_batch():
        print("Attempting to get first batch...")
        batch = next(val_iter)
        print(f"First batch obtained successfully!")
        print(f"Batch keys: {list(batch.keys())}")
        for key, value in batch.items():
            if hasattr(value, 'shape'):
                print(f"  {key}: shape {value.shape}, dtype {value.dtype}")
            elif isinstance(value, (list, tuple)):
                print(f"  {key}: {type(value)} with {len(value)} elements")
            else:
                print(f"  {key}: {type(value)}")
        return batch
    
    batch = debug_step("Get first batch", get_first_batch, 300)  # 给更长的超时时间
    
    # Step 7: 测试模型加载
    def load_model():
        import torch
        ckpt_path = "/data/Perceptron/outputs/maptrv2__maptrv2_exp_z10_4v0r_fasternetm_nowarp1500w_nowarp0522data/latest/dump_model/checkpoint_epoch_2.pth"
        print(f"Loading checkpoint from: {ckpt_path}")
        
        model = exp.get_model()
        if torch.cuda.is_available():
            model = model.cuda()
        
        checkpoint = torch.load(ckpt_path, map_location="cpu")
        model.load_state_dict(checkpoint["model"], strict=False)
        model.eval()
        print("Model loaded and set to eval mode")
        return model
    
    model = debug_step("Load model", load_model, 120)
    
    # Step 8: 测试模型推理
    def test_inference():
        import torch
        print("Testing model inference...")
        with torch.no_grad():
            # 将batch数据移到GPU
            if torch.cuda.is_available():
                for key in batch:
                    if isinstance(batch[key], torch.Tensor):
                        batch[key] = batch[key].cuda()
            
            # 执行推理
            result = model(**batch)
            print("Model inference completed successfully!")
            print(f"Result type: {type(result)}")
            if isinstance(result, (tuple, list)):
                print(f"Result length: {len(result)}")
            return result
    
    result = debug_step("Test inference", test_inference, 180)
    
    print("\n" + "="*50)
    print("[SUCCESS] All debugging steps completed successfully!")
    print("The hanging issue is likely not in the core evaluation loop.")
    print("="*50)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n[INTERRUPTED] Debugging interrupted by user")
    except Exception as e:
        print(f"\n[FATAL ERROR] Debugging failed: {e}")
        print(f"[TRACEBACK] {traceback.format_exc()}")
        sys.exit(1)
