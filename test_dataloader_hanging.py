#!/usr/bin/env python3
"""
专门测试数据加载器的脚本，用于定位卡住位置
"""

import sys
import time
import signal
import traceback
import os

def timeout_handler(signum, frame):
    raise TimeoutError("Operation timed out")

def test_with_timeout(func, timeout_seconds, description):
    """带超时的测试函数"""
    print(f"\n[TEST] {description}")
    print("-" * 40)
    
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(timeout_seconds)
    
    start_time = time.time()
    try:
        result = func()
        elapsed = time.time() - start_time
        print(f"[SUCCESS] Completed in {elapsed:.2f}s")
        return result
    except TimeoutError:
        print(f"[TIMEOUT] Operation timed out after {timeout_seconds}s")
        raise
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"[ERROR] Failed after {elapsed:.2f}s: {e}")
        raise
    finally:
        signal.alarm(0)

def main():
    # 设置路径
    project_root = "/Users/<USER>/projects/Perceptron"  # 根据实际情况调整
    sys.path.insert(0, project_root)
    
    print("="*60)
    print("MapTRv2 数据加载器卡住问题调试")
    print("="*60)
    
    # 测试1: 基础导入
    def test_imports():
        import torch
        from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        return PrivateE2EDataset
    
    PrivateE2EDataset = test_with_timeout(test_imports, 30, "Import modules")
    
    # 测试2: 创建数据集配置
    def test_dataset_config():
        # 从原始实验文件中提取数据配置
        from perceptron.exps.end2end.private.maptrv2.maptrv2_exp_z10_4v0r_fasternetm_nowarp1500w_warp0522data import Exp
        exp = Exp()
        data_cfg = exp.data_val_cfg_map
        print(f"Dataset config keys: {list(data_cfg.keys())}")
        return data_cfg
    
    data_cfg = test_with_timeout(test_dataset_config, 60, "Create dataset config")
    
    # 测试3: 创建数据集实例
    def test_dataset_creation():
        dataset = PrivateE2EDataset(**data_cfg)
        print(f"Dataset created, length: {len(dataset)}")
        return dataset
    
    dataset = test_with_timeout(test_dataset_creation, 120, "Create dataset instance")
    
    # 测试4: 获取单个样本
    def test_single_sample():
        print("Attempting to get first sample...")
        sample = dataset[0]
        print(f"Sample keys: {list(sample.keys())}")
        for key, value in sample.items():
            if hasattr(value, 'shape'):
                print(f"  {key}: shape {value.shape}")
            elif isinstance(value, (list, tuple)):
                print(f"  {key}: {type(value)} with {len(value)} elements")
            else:
                print(f"  {key}: {type(value)}")
        return sample
    
    sample = test_with_timeout(test_single_sample, 300, "Get single sample")
    
    # 测试5: 创建DataLoader
    def test_dataloader_creation():
        import torch.utils.data
        from torch.utils.data import DistributedSampler
        import torch.distributed as dist
        
        loader = torch.utils.data.DataLoader(
            dataset,
            batch_size=1,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,  # 使用单进程避免多进程问题
            pin_memory=False,
        )
        print(f"DataLoader created, length: {len(loader)}")
        return loader
    
    loader = test_with_timeout(test_dataloader_creation, 30, "Create DataLoader")
    
    # 测试6: 获取第一个batch
    def test_first_batch():
        print("Creating iterator...")
        data_iter = iter(loader)
        print("Getting first batch...")
        batch = next(data_iter)
        print(f"Batch obtained! Keys: {list(batch.keys())}")
        return batch
    
    batch = test_with_timeout(test_first_batch, 300, "Get first batch")
    
    # 测试7: 测试多个batch
    def test_multiple_batches():
        data_iter = iter(loader)
        for i in range(min(3, len(loader))):
            print(f"Getting batch {i+1}...")
            batch = next(data_iter)
            print(f"Batch {i+1} obtained successfully")
        return True
    
    test_with_timeout(test_multiple_batches, 600, "Get multiple batches")
    
    print("\n" + "="*60)
    print("[SUCCESS] 所有数据加载测试都成功完成！")
    print("数据加载器本身没有问题，卡住可能发生在其他地方。")
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n[INTERRUPTED] 测试被用户中断")
    except TimeoutError:
        print("\n[TIMEOUT] 测试超时，找到了卡住的位置！")
        print("请检查最后一个测试步骤的具体操作。")
    except Exception as e:
        print(f"\n[ERROR] 测试失败: {e}")
        print(f"错误详情: {traceback.format_exc()}")
        sys.exit(1)
