#!/usr/bin/env python3
"""
最小化的调试脚本，逐步测试每个组件
"""

import sys
import time
import os

# 添加项目路径
sys.path.insert(0, '/Users/<USER>/projects/Perceptron')

def log_with_time(message):
    """带时间戳的日志"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    log_with_time("开始最小化调试测试")
    
    # 1. 测试基础导入
    log_with_time("步骤1: 导入基础模块")
    try:
        import torch
        import numpy as np
        log_with_time(f"PyTorch版本: {torch.__version__}")
        log_with_time(f"CUDA可用: {torch.cuda.is_available()}")
    except Exception as e:
        log_with_time(f"基础模块导入失败: {e}")
        return
    
    # 2. 导入项目模块
    log_with_time("步骤2: 导入项目模块")
    try:
        from perceptron.exps.end2end.private.maptrv2.maptrv2_exp_z10_4v0r_fasternetm_nowarp1500w_warp0522data import Exp
        log_with_time("成功导入Exp类")
    except Exception as e:
        log_with_time(f"项目模块导入失败: {e}")
        return
    
    # 3. 创建实验实例
    log_with_time("步骤3: 创建实验实例")
    try:
        exp = Exp()
        log_with_time("实验实例创建成功")
    except Exception as e:
        log_with_time(f"实验实例创建失败: {e}")
        return
    
    # 4. 获取数据配置
    log_with_time("步骤4: 获取数据配置")
    try:
        data_cfg = exp.data_val_cfg_map
        log_with_time(f"数据配置获取成功，包含键: {list(data_cfg.keys())}")
    except Exception as e:
        log_with_time(f"数据配置获取失败: {e}")
        return
    
    # 5. 创建数据集（这里可能会卡住）
    log_with_time("步骤5: 创建数据集实例")
    log_with_time("注意: 这一步可能会花费较长时间，因为需要加载数据...")
    try:
        from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
        
        # 记录开始时间
        start_time = time.time()
        dataset = PrivateE2EDataset(**data_cfg)
        end_time = time.time()
        
        log_with_time(f"数据集创建成功，耗时: {end_time - start_time:.2f}秒")
        log_with_time(f"数据集长度: {len(dataset)}")
    except Exception as e:
        log_with_time(f"数据集创建失败: {e}")
        import traceback
        log_with_time(f"错误详情: {traceback.format_exc()}")
        return
    
    # 6. 测试获取单个样本（这里也可能卡住）
    log_with_time("步骤6: 测试获取单个样本")
    log_with_time("注意: 这一步可能会卡住，特别是在图像加载时...")
    try:
        start_time = time.time()
        log_with_time("开始获取第一个样本...")
        
        sample = dataset[0]
        
        end_time = time.time()
        log_with_time(f"样本获取成功，耗时: {end_time - start_time:.2f}秒")
        log_with_time(f"样本包含键: {list(sample.keys())}")
        
        # 打印样本信息
        for key, value in sample.items():
            if hasattr(value, 'shape'):
                log_with_time(f"  {key}: shape {value.shape}, dtype {value.dtype}")
            elif isinstance(value, (list, tuple)):
                log_with_time(f"  {key}: {type(value)} 包含 {len(value)} 个元素")
            else:
                log_with_time(f"  {key}: {type(value)}")
                
    except Exception as e:
        log_with_time(f"样本获取失败: {e}")
        import traceback
        log_with_time(f"错误详情: {traceback.format_exc()}")
        return
    
    # 7. 创建DataLoader
    log_with_time("步骤7: 创建DataLoader")
    try:
        import torch.utils.data
        
        # 使用最简单的配置
        loader = torch.utils.data.DataLoader(
            dataset,
            batch_size=1,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,  # 单进程，避免多进程问题
            pin_memory=False,
        )
        log_with_time(f"DataLoader创建成功，长度: {len(loader)}")
    except Exception as e:
        log_with_time(f"DataLoader创建失败: {e}")
        return
    
    # 8. 测试迭代器创建
    log_with_time("步骤8: 创建数据迭代器")
    try:
        data_iter = iter(loader)
        log_with_time("数据迭代器创建成功")
    except Exception as e:
        log_with_time(f"数据迭代器创建失败: {e}")
        return
    
    # 9. 获取第一个batch（最关键的测试）
    log_with_time("步骤9: 获取第一个batch")
    log_with_time("注意: 这是最可能卡住的地方！")
    try:
        start_time = time.time()
        log_with_time("调用 next(data_iter)...")
        
        batch = next(data_iter)
        
        end_time = time.time()
        log_with_time(f"第一个batch获取成功！耗时: {end_time - start_time:.2f}秒")
        log_with_time(f"Batch包含键: {list(batch.keys())}")
        
    except Exception as e:
        log_with_time(f"第一个batch获取失败: {e}")
        import traceback
        log_with_time(f"错误详情: {traceback.format_exc()}")
        return
    
    log_with_time("="*50)
    log_with_time("所有测试步骤都成功完成！")
    log_with_time("数据加载流程没有问题。")
    log_with_time("="*50)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log_with_time("测试被用户中断")
    except Exception as e:
        log_with_time(f"测试过程中发生未捕获的错误: {e}")
        import traceback
        log_with_time(f"错误详情: {traceback.format_exc()}")
